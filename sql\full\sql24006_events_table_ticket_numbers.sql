
-- --------------------------------------------------------

--
-- Table structure for table `ticket_numbers`
--
-- Creation: Jul 18, 2025 at 05:55 PM
--

CREATE TABLE `ticket_numbers` (
  `id` int(11) NOT NULL,
  `ticket_number` varchar(50) NOT NULL COMMENT 'Full ticket number (e.g., RER-2025-001)',
  `prefix` varchar(10) NOT NULL COMMENT 'Ticket prefix (e.g., RER)',
  `year` int(4) NOT NULL COMMENT 'Year component',
  `sequence` int(11) NOT NULL COMMENT 'Sequence number',
  `message_id` int(11) DEFAULT NULL COMMENT 'Associated message ID',
  `email_message_id` varchar(255) DEFAULT NULL COMMENT 'Temporary storage of email message_id during processing to prevent race conditions',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `security_token` varchar(10) DEFAULT NULL COMMENT 'Random security token for ticket validation'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- RELATIONSHIPS FOR TABLE `ticket_numbers`:
--

--
-- Dumping data for table `ticket_numbers`
--

INSERT INTO `ticket_numbers` (`id`, `ticket_number`, `prefix`, `year`, `sequence`, `message_id`, `email_message_id`, `created_at`, `security_token`) VALUES
(13, 'RER-2025-001', 'RER', 2025, 1, NULL, '<<EMAIL>>', '2025-07-19 01:01:18', 'NFQKS9');
