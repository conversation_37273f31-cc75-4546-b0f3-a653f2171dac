
-- --------------------------------------------------------

--
-- Table structure for table `custom_field_definitions`
--
-- Creation: Jun 23, 2025 at 08:30 PM
--

CREATE TABLE `custom_field_definitions` (
  `id` int(11) NOT NULL,
  `field_id` varchar(100) NOT NULL,
  `field_name` varchar(255) NOT NULL,
  `field_type` varchar(50) NOT NULL DEFAULT 'text',
  `entity_type` varchar(50) NOT NULL,
  `is_required` tinyint(1) DEFAULT 0,
  `field_options` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- RELATIONSHIPS FOR TABLE `custom_field_definitions`:
--

--
-- Dumping data for table `custom_field_definitions`
--

INSERT INTO `custom_field_definitions` (`id`, `field_id`, `field_name`, `field_type`, `entity_type`, `is_required`, `field_options`, `created_at`, `updated_at`) VALUES
(1, 'special_features', 'Special Features', 'textarea', 'registration', 0, NULL, '2025-05-27 11:48:25', '2025-05-27 11:48:25'),
(2, 'vehicle_condition', 'Vehicle Condition', 'select', 'registration', 1, '[\"Original\",\"Restored\",\"Modified\",\"Custom\"]', '2025-05-27 11:48:25', '2025-05-27 11:48:25'),
(3, 'year_of_manufacture', 'Year of Manufacture', 'select', 'registration', 1, NULL, '2025-05-27 11:48:25', '2025-05-27 22:31:59'),
(4, 'additional_notes', 'Additional Notes', 'textarea', 'registration', 0, NULL, '2025-05-27 11:48:25', '2025-05-27 11:48:25');
