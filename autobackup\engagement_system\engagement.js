/**
 * Photo Engagement System
 * Handles likes, comments, shares, favorites, and tags
 */

class PhotoEngagement {
    constructor() {
        this.baseUrl = window.BASE_URL || '';
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadEngagementData();
    }
    
    bindEvents() {
        // Like button clicks
        document.addEventListener('click', (e) => {
            if (e.target.closest('.like-btn')) {
                e.preventDefault();
                const btn = e.target.closest('.like-btn');
                const photoId = btn.dataset.photoId;
                this.toggleLike(photoId, btn);
            }
        });
        
        // Favorite button clicks
        document.addEventListener('click', (e) => {
            if (e.target.closest('.favorite-btn')) {
                e.preventDefault();
                const btn = e.target.closest('.favorite-btn');
                const photoId = btn.dataset.photoId;
                this.toggleFavorite(photoId, btn);
            }
        });
        
        // Comment form submissions
        document.addEventListener('submit', (e) => {
            if (e.target.classList.contains('comment-form')) {
                e.preventDefault();
                this.submitComment(e.target);
            }
        });
        
        // Share button clicks
        document.addEventListener('click', (e) => {
            if (e.target.closest('.share-btn')) {
                e.preventDefault();
                const btn = e.target.closest('.share-btn');
                const photoId = btn.dataset.photoId;
                const platform = btn.dataset.platform;
                this.sharePhoto(photoId, platform);
            }
        });
        
        // Show comments toggle
        document.addEventListener('click', (e) => {
            if (e.target.closest('.show-comments-btn')) {
                e.preventDefault();
                const btn = e.target.closest('.show-comments-btn');
                const photoId = btn.dataset.photoId;
                this.toggleComments(photoId);
            }
        });
    }
    
    async toggleLike(photoId, button) {
        if (!this.isLoggedIn()) {
            this.showLoginPrompt();
            return;
        }
        
        try {
            // Optimistic UI update
            const icon = button.querySelector('i');
            const countSpan = button.querySelector('.like-count');
            const isLiked = button.classList.contains('liked');
            
            // Toggle visual state immediately
            if (isLiked) {
                button.classList.remove('liked');
                icon.classList.remove('fas');
                icon.classList.add('far');
            } else {
                button.classList.add('liked');
                icon.classList.remove('far');
                icon.classList.add('fas');
                this.animateHeart(button);
            }
            
            const response = await fetch(`${this.baseUrl}/engagement/toggleLike`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ photo_id: photoId })
            });
            
            const data = await response.json();
            
            if (data.success) {
                // Update count
                if (countSpan) {
                    countSpan.textContent = data.like_count;
                }
                
                // Update button state to match server response
                if (data.liked) {
                    button.classList.add('liked');
                    icon.classList.remove('far');
                    icon.classList.add('fas');
                } else {
                    button.classList.remove('liked');
                    icon.classList.remove('fas');
                    icon.classList.add('far');
                }
            } else {
                // Revert optimistic update on error
                if (isLiked) {
                    button.classList.add('liked');
                    icon.classList.remove('far');
                    icon.classList.add('fas');
                } else {
                    button.classList.remove('liked');
                    icon.classList.remove('fas');
                    icon.classList.add('far');
                }
                this.showError('Failed to update like');
            }
        } catch (error) {
            console.error('Like error:', error);
            this.showError('Failed to update like');
        }
    }
    
    async toggleFavorite(photoId, button) {
        if (!this.isLoggedIn()) {
            this.showLoginPrompt();
            return;
        }
        
        try {
            const icon = button.querySelector('i');
            const isFavorited = button.classList.contains('favorited');
            
            // Optimistic UI update
            if (isFavorited) {
                button.classList.remove('favorited');
                icon.classList.remove('fas');
                icon.classList.add('far');
            } else {
                button.classList.add('favorited');
                icon.classList.remove('far');
                icon.classList.add('fas');
                this.animateBookmark(button);
            }
            
            const response = await fetch(`${this.baseUrl}/engagement/toggleFavorite`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ photo_id: photoId })
            });
            
            const data = await response.json();
            
            if (data.success) {
                // Update button state to match server response
                if (data.favorited) {
                    button.classList.add('favorited');
                    icon.classList.remove('far');
                    icon.classList.add('fas');
                } else {
                    button.classList.remove('favorited');
                    icon.classList.remove('fas');
                    icon.classList.add('far');
                }
            } else {
                // Revert optimistic update on error
                if (isFavorited) {
                    button.classList.add('favorited');
                    icon.classList.remove('far');
                    icon.classList.add('fas');
                } else {
                    button.classList.remove('favorited');
                    icon.classList.remove('fas');
                    icon.classList.add('far');
                }
                this.showError('Failed to update favorite');
            }
        } catch (error) {
            console.error('Favorite error:', error);
            this.showError('Failed to update favorite');
        }
    }
    
    async submitComment(form) {
        if (!this.isLoggedIn()) {
            this.showLoginPrompt();
            return;
        }
        
        const formData = new FormData(form);
        const photoId = formData.get('photo_id');
        const comment = formData.get('comment').trim();
        const parentCommentId = formData.get('parent_comment_id') || null;
        
        if (!comment) {
            this.showError('Please enter a comment');
            return;
        }
        
        try {
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            submitBtn.disabled = true;
            submitBtn.textContent = 'Posting...';
            
            const response = await fetch(`${this.baseUrl}/engagement/addComment`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    photo_id: photoId,
                    comment: comment,
                    parent_comment_id: parentCommentId
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                // Clear form
                form.reset();
                
                // Add comment to UI
                this.addCommentToUI(data.comment, parentCommentId);
                
                // Update comment count
                this.updateCommentCount(photoId);
                
                this.showSuccess('Comment posted!');
            } else {
                this.showError(data.error || 'Failed to post comment');
            }
            
            submitBtn.disabled = false;
            submitBtn.textContent = originalText;
            
        } catch (error) {
            console.error('Comment error:', error);
            this.showError('Failed to post comment');
        }
    }
    
    async sharePhoto(photoId, platform) {
        try {
            // Track the share
            await fetch(`${this.baseUrl}/engagement/trackShare`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    photo_id: photoId,
                    platform: platform
                })
            });
            
            // Get share URL
            const shareUrl = `${this.baseUrl}/photo/share/${photoId}`;
            
            // Handle different platforms
            switch (platform) {
                case 'facebook':
                    this.shareToFacebook(shareUrl);
                    break;
                case 'twitter':
                    this.shareToTwitter(shareUrl);
                    break;
                case 'whatsapp':
                    this.shareToWhatsApp(shareUrl);
                    break;
                case 'copy_link':
                    this.copyToClipboard(shareUrl);
                    break;
                default:
                    this.copyToClipboard(shareUrl);
            }
            
        } catch (error) {
            console.error('Share error:', error);
            this.showError('Failed to share photo');
        }
    }
    
    // Helper methods
    shareToFacebook(url) {
        const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;
        window.open(facebookUrl, '_blank', 'width=600,height=400');
    }

    shareToTwitter(url) {
        const twitterUrl = `https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}`;
        window.open(twitterUrl, '_blank', 'width=600,height=400');
    }

    shareToWhatsApp(url) {
        const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(url)}`;
        window.open(whatsappUrl, '_blank');
    }

    async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            this.showSuccess('Link copied to clipboard!');
        } catch (error) {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            this.showSuccess('Link copied to clipboard!');
        }
    }

    animateHeart(button) {
        button.style.transform = 'scale(1.2)';
        button.style.transition = 'transform 0.2s ease';
        setTimeout(() => {
            button.style.transform = 'scale(1)';
        }, 200);
    }

    animateBookmark(button) {
        button.style.transform = 'scale(1.1)';
        button.style.transition = 'transform 0.2s ease';
        setTimeout(() => {
            button.style.transform = 'scale(1)';
        }, 200);
    }

    async toggleComments(photoId) {
        const commentsContainer = document.querySelector(`#comments-${photoId}`);

        if (commentsContainer.style.display === 'none' || !commentsContainer.style.display) {
            // Load and show comments
            await this.loadComments(photoId);
            commentsContainer.style.display = 'block';
        } else {
            // Hide comments
            commentsContainer.style.display = 'none';
        }
    }

    async loadComments(photoId) {
        try {
            const response = await fetch(`${this.baseUrl}/engagement/getComments/${photoId}`);
            const data = await response.json();

            if (data.success) {
                this.renderComments(photoId, data.comments);
            }
        } catch (error) {
            console.error('Load comments error:', error);
        }
    }

    renderComments(photoId, comments) {
        const container = document.querySelector(`#comments-list-${photoId}`);
        if (!container) return;

        container.innerHTML = '';

        comments.forEach(comment => {
            const commentElement = this.createCommentElement(comment);
            container.appendChild(commentElement);
        });
    }

    createCommentElement(comment) {
        const div = document.createElement('div');
        div.className = 'comment mb-3';
        div.innerHTML = `
            <div class="d-flex">
                <img src="${comment.profile_image || '/public/images/default-avatar.png'}"
                     alt="${comment.user_name}"
                     class="rounded-circle me-2"
                     style="width: 32px; height: 32px; object-fit: cover;">
                <div class="flex-grow-1">
                    <div class="comment-content bg-light rounded p-2">
                        <strong>${comment.user_name}</strong>
                        <p class="mb-0">${this.formatComment(comment.comment)}</p>
                    </div>
                    <small class="text-muted">${this.formatDate(comment.created_at)}</small>
                    ${comment.replies ? comment.replies.map(reply => this.createCommentElement(reply)).join('') : ''}
                </div>
            </div>
        `;
        return div;
    }

    formatComment(comment) {
        // Convert @mentions to links
        return comment.replace(/@(\w+)/g, '<span class="text-primary">@$1</span>');
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diff = now - date;

        if (diff < 60000) return 'Just now';
        if (diff < 3600000) return `${Math.floor(diff / 60000)}m ago`;
        if (diff < 86400000) return `${Math.floor(diff / 3600000)}h ago`;
        return date.toLocaleDateString();
    }

    addCommentToUI(comment, parentCommentId) {
        // Implementation depends on UI structure
        // This would add the new comment to the appropriate place in the DOM
    }

    updateCommentCount(photoId) {
        // Update comment count display
        const countElement = document.querySelector(`#comment-count-${photoId}`);
        if (countElement) {
            const currentCount = parseInt(countElement.textContent) || 0;
            countElement.textContent = currentCount + 1;
        }
    }

    async loadEngagementData() {
        // Load engagement data for all photos on the page
        const photoElements = document.querySelectorAll('[data-photo-id]');

        for (const element of photoElements) {
            const photoId = element.dataset.photoId;
            await this.loadPhotoEngagement(photoId);
        }
    }

    async loadPhotoEngagement(photoId) {
        try {
            const response = await fetch(`${this.baseUrl}/engagement/getEngagementData/${photoId}`);
            const data = await response.json();

            if (data.success) {
                this.updateEngagementUI(photoId, data.stats, data.user_engagement);
            }
        } catch (error) {
            console.error('Load engagement error:', error);
        }
    }

    updateEngagementUI(photoId, stats, userEngagement) {
        // Update like button
        const likeBtn = document.querySelector(`[data-photo-id="${photoId}"] .like-btn`);
        if (likeBtn) {
            const icon = likeBtn.querySelector('i');
            const count = likeBtn.querySelector('.like-count');

            if (userEngagement.liked) {
                likeBtn.classList.add('liked');
                icon.classList.remove('far');
                icon.classList.add('fas');
            }

            if (count) {
                count.textContent = stats.likes_count || 0;
            }
        }

        // Update favorite button
        const favoriteBtn = document.querySelector(`[data-photo-id="${photoId}"] .favorite-btn`);
        if (favoriteBtn) {
            const icon = favoriteBtn.querySelector('i');

            if (userEngagement.favorited) {
                favoriteBtn.classList.add('favorited');
                icon.classList.remove('far');
                icon.classList.add('fas');
            }
        }

        // Update comment count
        const commentCount = document.querySelector(`#comment-count-${photoId}`);
        if (commentCount) {
            commentCount.textContent = stats.comments_count || 0;
        }
    }

    isLoggedIn() {
        // Check if user is logged in (implementation depends on your auth system)
        return document.body.dataset.loggedIn === 'true' ||
               document.querySelector('meta[name="user-logged-in"]')?.content === 'true';
    }

    showLoginPrompt() {
        if (window.pwaFeatures) {
            window.pwaFeatures.showNotification('Login Required', 'Please log in to interact with photos', 'info');
        } else {
            alert('Please log in to interact with photos');
        }
    }

    showSuccess(message) {
        if (window.pwaFeatures) {
            window.pwaFeatures.showNotification('Success', message, 'success');
        } else {
            console.log('Success:', message);
        }
    }

    showError(message) {
        if (window.pwaFeatures) {
            window.pwaFeatures.showNotification('Error', message, 'error');
        } else {
            console.error('Error:', message);
        }
    }
}

// Initialize engagement system when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.photoEngagement = new PhotoEngagement();
});
