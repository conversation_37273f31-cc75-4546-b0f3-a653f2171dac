<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <!-- Title Section -->
            <div class="text-center text-md-start mb-3">
                <h1 class="h3 mb-1">
                    <i class="fas fa-camera me-2"></i>
                    <?php echo htmlspecialchars($data['event_name']); ?> - Photo Gallery
                </h1>
                <p class="text-muted mb-0">Photos shared by attendees at this event</p>
            </div>

            <!-- Action Buttons -->
            <?php if ($data['can_upload']): ?>
                <!-- Desktop: 3 buttons in a row, Mobile: stacked -->
                <div class="d-flex flex-column flex-md-row gap-3 justify-content-center justify-content-md-start">
                    <a href="<?php echo BASE_URL; ?>/image_editor/upload/event_photo/<?php echo $data['event_id']; ?>"
                       class="btn btn-primary btn-action">
                        <i class="fas fa-upload me-2"></i>Upload Photo
                    </a>
                    <button class="btn btn-success btn-action"
                            data-camera-capture="camera-upload"
                            data-entity-type="event_photo"
                            data-entity-id="<?php echo $data['event_id']; ?>"
                            data-csrf-token="<?php echo generateCsrfToken(); ?>"
                            title="Take Photo">
                        <i class="fas fa-camera me-2"></i>Take Photo
                    </button>
                    <a href="<?php echo BASE_URL; ?>/<?php echo $data['event_type']; ?>/view/<?php echo $data['event_id']; ?>"
                       class="btn btn-outline-secondary btn-action">
                        <i class="fas fa-arrow-left me-2"></i>Back to Event
                    </a>
                </div>
            <?php else: ?>
                <!-- Single back button when user can't upload -->
                <div class="d-flex justify-content-center justify-content-md-start">
                    <a href="<?php echo BASE_URL; ?>/<?php echo $data['event_type']; ?>/view/<?php echo $data['event_id']; ?>"
                       class="btn btn-outline-secondary btn-action">
                        <i class="fas fa-arrow-left me-2"></i>Back to Event
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Event Info -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card bg-light">
                <div class="card-body p-3">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h6 class="mb-1"><?php echo htmlspecialchars($data['event_name']); ?></h6>
                            <p class="text-muted mb-0">
                                <i class="fas fa-calendar me-1"></i>
                                <?php echo date('F j, Y', strtotime($data['event_info']->start_date)); ?>
                                <?php if ($data['event_info']->start_date !== $data['event_info']->end_date): ?>
                                    - <?php echo date('F j, Y', strtotime($data['event_info']->end_date)); ?>
                                <?php endif; ?>
                            </p>
                        </div>
                        <div class="col-md-4 text-md-end">
                            <span class="badge bg-primary fs-6">
                                <?php echo count($data['photos']); ?> Photos
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filtering and Search Controls -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <!-- Filter Dropdown -->
                        <div class="col-md-3 mb-3 mb-md-0">
                            <select id="photoFilter" class="form-select form-select-sm">
                                <?php foreach ($data['available_filters'] as $value => $label): ?>
                                    <option value="<?php echo $value; ?>"
                                            <?php echo $data['current_filter'] === $value ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($label); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <!-- Search Input -->
                        <div class="col-md-9 mb-3 mb-md-0">
                            <div class="input-group input-group-sm">
                                <input type="text"
                                       id="photoSearch"
                                       class="form-control"
                                       placeholder="Search photos by caption or uploader..."
                                       value="<?php echo htmlspecialchars($data['current_search']); ?>"
                                       maxlength="100">
                                <button class="btn btn-outline-secondary"
                                        type="button"
                                        id="clearSearch"
                                        title="Clear search">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>


                    </div>

                    <!-- Filter Info -->
                    <?php if ($data['current_search'] || $data['current_filter'] !== 'newest'): ?>
                    <div class="mt-3 pt-3 border-top">
                        <div class="d-flex flex-wrap gap-2">
                            <?php if ($data['current_filter'] !== 'newest'): ?>
                            <span class="badge bg-primary">
                                <i class="fas fa-filter me-1"></i>
                                <?php echo htmlspecialchars($data['available_filters'][$data['current_filter']]); ?>
                            </span>
                            <?php endif; ?>

                            <?php if ($data['current_search']): ?>
                            <span class="badge bg-info">
                                <i class="fas fa-search me-1"></i>
                                "<?php echo htmlspecialchars($data['current_search']); ?>"
                            </span>
                            <?php endif; ?>

                            <button class="btn btn-link btn-sm p-0 ms-2"
                                    onclick="window.location.href = window.location.pathname">
                                <i class="fas fa-times me-1"></i>Clear all filters
                            </button>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Photo Grid -->
    <div class="row"
         id="photoGrid"
         data-event-type="<?php echo htmlspecialchars($data['event_type']); ?>"
         data-event-id="<?php echo $data['event_id']; ?>"
         data-current-filter="<?php echo htmlspecialchars($data['current_filter']); ?>"
         data-current-search="<?php echo htmlspecialchars($data['current_search']); ?>"
         data-current-page="<?php echo $data['current_page']; ?>"
         data-per-page="<?php echo $data['per_page']; ?>">
        <?php if (empty($data['photos'])): ?>
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-camera fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No photos yet</h5>
                    <p class="text-muted">Be the first to share a photo from this event!</p>
                    <?php if ($data['can_upload']): ?>
                        <div class="d-flex flex-column flex-sm-row gap-3 justify-content-center mt-3">
                            <a href="<?php echo BASE_URL; ?>/image_editor/upload/event_photo/<?php echo $data['event_id']; ?>"
                               class="btn btn-primary btn-action">
                                <i class="fas fa-upload me-2"></i>Upload First Photo
                            </a>
                            <button class="btn btn-success btn-action"
                                    data-camera-capture="camera-upload"
                                    data-entity-type="event_photo"
                                    data-entity-id="<?php echo $data['event_id']; ?>"
                                    data-csrf-token="<?php echo generateCsrfToken(); ?>">
                                <i class="fas fa-camera me-2"></i>Take First Photo
                            </button>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        <?php else: ?>
            <?php foreach ($data['photos'] as $photo): ?>
                <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                    <div class="card h-100 shadow-sm">
                        <div class="position-relative">
                            <img src="<?php echo BASE_URL . '/' . $photo->thumbnail_path; ?>"
                                 class="card-img-top"
                                 style="height: 200px; object-fit: cover; cursor: pointer;"
                                 onclick='showPhotoModal("<?php echo $photo->file_path; ?>", <?php echo json_encode($photo->caption ?? ""); ?>)'
                                 alt="Event photo">
                            
                            <!-- Category Badge -->
                            <?php if (isset($photo->category)): ?>
                                <span class="position-absolute top-0 start-0 m-2">
                                    <span class="badge bg-dark">
                                        <?php
                                        // Use admin-configured category labels
                                        $categoryLabels = $data['category_labels'] ?? [
                                            'vehicle' => '🚗 Vehicle',
                                            'atmosphere' => '🎪 Atmosphere',
                                            'awards' => '🏆 Awards',
                                            'vendors' => '🍔 Vendors',
                                            'people' => '👥 People'
                                        ];
                                        echo $categoryLabels[$photo->category] ?? '📷 Photo';
                                        ?>
                                    </span>
                                </span>
                            <?php endif; ?>
                            
                            <!-- Privacy Badge -->
                            <?php if (isset($photo->privacy_level) && $photo->privacy_level !== 'public'): ?>
                                <span class="position-absolute top-0 end-0 m-2">
                                    <span class="badge bg-warning">
                                        <?php
                                        $privacyIcons = [
                                            'attendees' => '👥 Attendees',
                                            'friends' => '👫 Friends',
                                            'private' => '🔒 Private'
                                        ];
                                        echo $privacyIcons[$photo->privacy_level] ?? '🔒 Private';
                                        ?>
                                    </span>
                                </span>
                            <?php endif; ?>
                        </div>
                        
                        <div class="card-body p-3">
                            <!-- Upload Info -->
                            <div class="d-flex align-items-center mb-2">
                                <small class="text-muted">
                                    Uploaded <?php echo timeAgo($photo->created_at); ?>
                                </small>
                            </div>
                            
                            <!-- Caption (always reserve space for consistent layout) -->
                            <div class="caption-area mb-2" style="min-height: 2.5rem;">
                                <?php if (!empty($photo->caption)): ?>
                                    <?php
                                    // Get first line only
                                    $firstLine = strtok($photo->caption, "\n\r");
                                    $needsTruncation = strlen($firstLine) > 40;
                                    $hasMoreLines = $firstLine !== $photo->caption;
                                    $showMore = $needsTruncation || $hasMoreLines;
                                    $displayText = $needsTruncation ? substr($firstLine, 0, 40) : $firstLine;
                                    ?>
                                    <div class="d-flex justify-content-between align-items-start">
                                        <span class="card-text small mb-0 caption-text"
                                              style="cursor: pointer; flex: 1;"
                                              onclick='showPhotoModal("<?php echo $photo->file_path; ?>", <?php echo json_encode($photo->caption ?? ""); ?>)'>
                                            <?php echo htmlspecialchars($displayText); ?>
                                        </span>
                                        <?php if ($showMore): ?>
                                            <small class="text-primary ms-2 fw-bold"
                                                   style="cursor: pointer; text-decoration: underline; font-size: 0.7rem;"
                                                   onclick='showPhotoModal("<?php echo $photo->file_path; ?>", <?php echo json_encode($photo->caption ?? ""); ?>)'>
                                                more
                                            </small>
                                        <?php endif; ?>
                                    </div>
                                <?php else: ?>
                                    <p class="card-text small text-muted mb-0" style="font-style: italic;">
                                        No caption
                                    </p>
                                <?php endif; ?>
                            </div>

                            <!-- Engagement Actions -->
                            <div class="engagement-section mb-3" data-photo-id="<?php echo $photo->id; ?>">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <div class="engagement-actions d-flex gap-3">
                                        <!-- Like Button -->
                                        <button class="btn btn-link p-0 like-btn"
                                                data-photo-id="<?php echo $photo->id; ?>"
                                                title="Like">
                                            <i class="far fa-heart text-danger"></i>
                                            <span class="like-count ms-1"><?php echo $photo->likes_count ?? 0; ?></span>
                                        </button>

                                        <!-- Comment Button -->
                                        <button class="btn btn-link p-0 show-comments-btn"
                                                data-photo-id="<?php echo $photo->id; ?>"
                                                title="Comments">
                                            <i class="far fa-comment text-primary"></i>
                                            <span id="comment-count-<?php echo $photo->id; ?>" class="ms-1"><?php echo $photo->comments_count ?? 0; ?></span>
                                        </button>

                                        <!-- Share Button -->
                                        <div class="dropdown">
                                            <button class="btn btn-link p-0"
                                                    data-bs-toggle="dropdown"
                                                    title="Share">
                                                <i class="far fa-share-square text-success"></i>
                                            </button>
                                            <ul class="dropdown-menu">
                                                <li><a class="dropdown-item share-btn"
                                                       data-photo-id="<?php echo $photo->id; ?>"
                                                       data-platform="facebook" href="#">
                                                    <i class="fab fa-facebook-f me-2"></i>Facebook
                                                </a></li>
                                                <li><a class="dropdown-item share-btn"
                                                       data-photo-id="<?php echo $photo->id; ?>"
                                                       data-platform="twitter" href="#">
                                                    <i class="fab fa-twitter me-2"></i>Twitter
                                                </a></li>
                                                <li><a class="dropdown-item share-btn"
                                                       data-photo-id="<?php echo $photo->id; ?>"
                                                       data-platform="whatsapp" href="#">
                                                    <i class="fab fa-whatsapp me-2"></i>WhatsApp
                                                </a></li>
                                                <li><hr class="dropdown-divider"></li>
                                                <li><a class="dropdown-item share-btn"
                                                       data-photo-id="<?php echo $photo->id; ?>"
                                                       data-platform="copy_link" href="#">
                                                    <i class="fas fa-link me-2"></i>Copy Link
                                                </a></li>
                                            </ul>
                                        </div>
                                    </div>

                                    <!-- Favorite Button -->
                                    <button class="btn btn-link p-0 favorite-btn <?php echo ($photo->is_favorited_by_user ?? false) ? 'favorited' : ''; ?>"
                                            data-photo-id="<?php echo $photo->id; ?>"
                                            title="<?php echo ($photo->is_favorited_by_user ?? false) ? 'Remove from Favorites' : 'Add to Favorites'; ?>">
                                        <i class="fa<?php echo ($photo->is_favorited_by_user ?? false) ? 's' : 'r'; ?> fa-bookmark text-warning"></i>
                                    </button>
                                </div>

                                <!-- Comments Section (Hidden by default) -->
                                <div id="comments-<?php echo $photo->id; ?>" class="comments-section mt-2" style="display: none;">
                                    <!-- Add Comment Form (Above comments for easy access) -->
                                    <?php if (isset($_SESSION['user_id'])): ?>
                                    <div class="add-comment-section mb-3 p-2 bg-light rounded">
                                        <form class="comment-form">
                                            <input type="hidden" name="photo_id" value="<?php echo $photo->id; ?>">
                                            <div class="input-group input-group-sm">
                                                <input type="text"
                                                       name="comment"
                                                       class="form-control"
                                                       placeholder="Add a comment..."
                                                       maxlength="500"
                                                       required>
                                                <button type="submit" class="btn btn-primary">
                                                    <i class="fas fa-paper-plane"></i>
                                                </button>
                                            </div>
                                            <small class="text-muted">Press Enter to post or click the send button</small>
                                        </form>
                                    </div>
                                    <?php else: ?>
                                    <div class="add-comment-section mb-3 p-2 bg-light rounded text-center">
                                        <p class="text-muted small mb-0">
                                            <a href="<?php echo BASE_URL; ?>/auth/login" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-sign-in-alt me-1"></i>Log in to add comments
                                            </a>
                                        </p>
                                    </div>
                                    <?php endif; ?>

                                    <!-- Comments List -->
                                    <div id="comments-list-<?php echo $photo->id; ?>" class="comments-list">
                                        <!-- Comments will be loaded here -->
                                    </div>
                                </div>
                            </div>

                            <!-- Admin Actions -->
                            <div class="d-flex justify-content-between align-items-center">
                                <div class="btn-group btn-group-sm" role="group">
                                    <a href="<?php echo BASE_URL; ?>/image_editor/edit/<?php echo $photo->id; ?>"
                                       class="btn btn-outline-primary btn-sm" title="Edit Image">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" class="btn btn-outline-info btn-sm"
                                            onclick="editEventPhotoDetails(<?php echo $photo->id; ?>)" title="Edit Details">
                                        <i class="fas fa-info-circle"></i>
                                    </button>
                                </div>

                                <div class="dropdown">
                                    <button class="btn btn-sm btn-outline-secondary dropdown-toggle"
                                            type="button" data-bs-toggle="dropdown" title="More Options">
                                        <i class="fas fa-ellipsis-v"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li>
                                            <a class="dropdown-item" href="#" onclick="downloadPhoto('<?php echo $photo->file_path; ?>')">
                                                <i class="fas fa-download text-secondary me-2"></i> Download
                                            </a>
                                        </li>
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <a class="dropdown-item" href="<?php echo BASE_URL; ?>/photo/share/<?php echo $photo->id; ?>" target="_blank">
                                                <i class="fas fa-external-link-alt text-info me-2"></i> View Share Page
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="#" onclick="shareToFacebook('<?php echo $photo->id; ?>')">
                                                <i class="fab fa-facebook text-primary me-2"></i> Share on Facebook
                                            </a>
                                        </li>
                                        <li>
                                            <a class="dropdown-item" href="#" onclick="copyPhotoLink('<?php echo $photo->id; ?>')">
                                                <i class="fas fa-link me-2"></i> Copy Share Link
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>

    <!-- Simple Pagination -->
    <?php if ($data['total_pages'] > 1): ?>
    <div class="row mt-4">
        <div class="col-12">
            <nav aria-label="Photo gallery pagination">
                <ul class="pagination justify-content-center">
                    <!-- Previous Page -->
                    <?php if ($data['current_page'] > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo $data['current_page'] - 1; ?><?php echo !empty($data['current_filter']) && $data['current_filter'] !== 'newest' ? '&filter=' . urlencode($data['current_filter']) : ''; ?><?php echo !empty($data['current_search']) ? '&search=' . urlencode($data['current_search']) : ''; ?>">
                                <i class="fas fa-chevron-left"></i> Previous
                            </a>
                        </li>
                    <?php else: ?>
                        <li class="page-item disabled">
                            <span class="page-link"><i class="fas fa-chevron-left"></i> Previous</span>
                        </li>
                    <?php endif; ?>

                    <!-- Page Numbers -->
                    <?php
                    $start_page = max(1, $data['current_page'] - 2);
                    $end_page = min($data['total_pages'], $data['current_page'] + 2);

                    // Show first page if not in range
                    if ($start_page > 1): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=1<?php echo !empty($data['current_filter']) && $data['current_filter'] !== 'newest' ? '&filter=' . urlencode($data['current_filter']) : ''; ?><?php echo !empty($data['current_search']) ? '&search=' . urlencode($data['current_search']) : ''; ?>">1</a>
                        </li>
                        <?php if ($start_page > 2): ?>
                            <li class="page-item disabled"><span class="page-link">...</span></li>
                        <?php endif; ?>
                    <?php endif; ?>

                    <!-- Current page range -->
                    <?php for ($i = $start_page; $i <= $end_page; $i++): ?>
                        <li class="page-item <?php echo $i === $data['current_page'] ? 'active' : ''; ?>">
                            <?php if ($i === $data['current_page']): ?>
                                <span class="page-link"><?php echo $i; ?></span>
                            <?php else: ?>
                                <a class="page-link" href="?page=<?php echo $i; ?><?php echo !empty($data['current_filter']) && $data['current_filter'] !== 'newest' ? '&filter=' . urlencode($data['current_filter']) : ''; ?><?php echo !empty($data['current_search']) ? '&search=' . urlencode($data['current_search']) : ''; ?>"><?php echo $i; ?></a>
                            <?php endif; ?>
                        </li>
                    <?php endfor; ?>

                    <!-- Show last page if not in range -->
                    <?php if ($end_page < $data['total_pages']): ?>
                        <?php if ($end_page < $data['total_pages'] - 1): ?>
                            <li class="page-item disabled"><span class="page-link">...</span></li>
                        <?php endif; ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo $data['total_pages']; ?><?php echo !empty($data['current_filter']) && $data['current_filter'] !== 'newest' ? '&filter=' . urlencode($data['current_filter']) : ''; ?><?php echo !empty($data['current_search']) ? '&search=' . urlencode($data['current_search']) : ''; ?>"><?php echo $data['total_pages']; ?></a>
                        </li>
                    <?php endif; ?>

                    <!-- Next Page -->
                    <?php if ($data['current_page'] < $data['total_pages']): ?>
                        <li class="page-item">
                            <a class="page-link" href="?page=<?php echo $data['current_page'] + 1; ?><?php echo !empty($data['current_filter']) && $data['current_filter'] !== 'newest' ? '&filter=' . urlencode($data['current_filter']) : ''; ?><?php echo !empty($data['current_search']) ? '&search=' . urlencode($data['current_search']) : ''; ?>">
                                Next <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                    <?php else: ?>
                        <li class="page-item disabled">
                            <span class="page-link">Next <i class="fas fa-chevron-right"></i></span>
                        </li>
                    <?php endif; ?>
                </ul>
            </nav>

            <!-- Page Info -->
            <div class="text-center mt-2">
                <small class="text-muted">
                    Showing page <?php echo $data['current_page']; ?> of <?php echo $data['total_pages']; ?>
                    (<?php echo count($data['photos']); ?> of <?php echo $data['total_photos']; ?> photos)
                </small>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<!-- Photo Modal -->
<div class="modal fade" id="photoModal" tabindex="-1">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Event Photo</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body text-center">
                <img id="modalPhoto" src="" class="img-fluid" alt="Event photo">
                <div id="modalCaption" class="mt-3 text-start" style="white-space: pre-line; max-width: 100%; word-wrap: break-word;"></div>
            </div>
        </div>
    </div>
</div>

<style>
.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.btn-group-sm .btn {
    font-size: 0.75rem;
}

/* Action Button Styling */
.btn-action {
    min-height: 48px;
    min-width: 140px;
    font-weight: 500;
    border-radius: 8px;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    white-space: nowrap;
    padding: 12px 20px;
}

.btn-action:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    text-decoration: none;
}

.btn-action:active {
    transform: translateY(0);
}

/* Desktop: Equal width buttons */
@media (min-width: 768px) {
    .btn-action {
        flex: 1;
        max-width: 200px;
    }
}

/* Mobile: Full width stacked buttons */
@media (max-width: 767px) {
    .btn-action {
        width: 100%;
        min-height: 52px;
        font-size: 1rem;
    }

    .btn-action i {
        font-size: 1.1rem;
    }
}

/* Racing theme button enhancements */
.btn-primary.btn-action {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
    color: white;
}

.btn-success.btn-action {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
    border: none;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
    color: white;
}

.btn-outline-secondary.btn-action {
    border: 2px solid #6c757d;
    color: #6c757d;
    background: transparent;
}

.btn-outline-secondary.btn-action:hover {
    background: #6c757d;
    color: white;
    box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
}
</style>

<script>
// Photo modal
function showPhotoModal(imagePath, caption) {
    document.getElementById('modalPhoto').src = '<?php echo BASE_URL; ?>/' + imagePath;

    const captionElement = document.getElementById('modalCaption');
    if (caption && caption.trim()) {
        captionElement.textContent = caption;
        captionElement.style.display = 'block';
    } else {
        captionElement.style.display = 'none';
    }

    const modal = new bootstrap.Modal(document.getElementById('photoModal'));
    modal.show();
}

// Download photo
function downloadPhoto(imagePath) {
    const link = document.createElement('a');
    link.href = '<?php echo BASE_URL; ?>/' + imagePath;
    link.download = imagePath.split('/').pop();
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// Share to Facebook
function shareToFacebook(photoId) {
    const shareUrl = `<?php echo BASE_URL; ?>/photo/share/${photoId}`;

    // Use Facebook SDK if available, otherwise fallback to simple share
    if (typeof FB !== 'undefined') {
        FB.ui({
            method: 'share',
            href: shareUrl
        }, function(response) {
            if (response && !response.error_message) {
                if (window.pwaFeatures) {
                    window.pwaFeatures.showNotification('Success', 'Photo shared successfully', 'success');
                }
            }
        });
    } else {
        // Fallback to Facebook share URL
        const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}`;
        window.open(facebookUrl, '_blank', 'width=600,height=400');
    }
}

// Copy photo link
function copyPhotoLink(photoId) {
    const url = `<?php echo BASE_URL; ?>/photo/share/${photoId}`;

    if (navigator.clipboard) {
        navigator.clipboard.writeText(url).then(() => {
            if (window.pwaFeatures) {
                window.pwaFeatures.showNotification('Success', 'Share link copied to clipboard', 'success');
            } else {
                alert('Share link copied to clipboard');
            }
        });
    } else {
        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = url;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
        alert('Photo link copied to clipboard');
    }
}

// Set event data for PWA camera
document.addEventListener('DOMContentLoaded', function() {
    // Store event data for PWA camera integration
    if (window.pwaFeatures) {
        window.pwaFeatures.currentEventType = '<?php echo $data['event_type']; ?>';
        window.pwaFeatures.currentEventId = '<?php echo $data['event_id']; ?>';
    }
});
</script>

<!-- Edit Event Photo Details Modal -->
<div class="modal fade" id="editPhotoDetailsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit me-2"></i>Edit Photo Details
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="editPhotoDetailsForm">
                    <input type="hidden" id="edit_photo_id" name="photo_id">

                    <!-- Category -->
                    <div class="mb-3">
                        <label for="edit_photo_category" class="form-label">
                            <i class="fas fa-tag me-2"></i>Category <span class="text-danger">*</span>
                        </label>
                        <select class="form-select" id="edit_photo_category" name="category" required>
                            <option value="">Select Category</option>
                            <option value="vehicle">🚗 Vehicle</option>
                            <option value="atmosphere">⭐ Atmosphere</option>
                            <option value="awards">🏆 Awards</option>
                            <option value="vendors">🍔 Vendors</option>
                            <option value="people">👥 People</option>
                        </select>
                    </div>

                    <!-- Privacy Level -->
                    <div class="mb-3">
                        <label for="edit_photo_privacy" class="form-label">
                            <i class="fas fa-lock me-2"></i>Privacy Level
                        </label>
                        <select class="form-select" id="edit_photo_privacy" name="privacy_level">
                            <option value="public">Public - Visible to everyone</option>
                            <option value="event_only">Event Only - Visible to event attendees</option>
                            <option value="private">Private - Only visible to you</option>
                        </select>
                    </div>

                    <!-- Caption -->
                    <div class="mb-3">
                        <label for="edit_photo_caption" class="form-label">
                            <i class="fas fa-comment me-2"></i>Caption (Optional)
                        </label>
                        <textarea class="form-control" id="edit_photo_caption" name="caption"
                                  rows="3" maxlength="500"
                                  placeholder="Describe your photo... What makes this shot special?"></textarea>
                        <div class="form-text">
                            <i class="fas fa-info-circle me-1"></i>
                            This caption will appear when shared on social media.
                            <span class="float-end">
                                <span id="edit-caption-count">0</span>/500 characters
                            </span>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-2"></i>Cancel
                </button>
                <button type="button" class="btn btn-primary" onclick="savePhotoDetails()">
                    <i class="fas fa-save me-2"></i>Save Changes
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Edit event photo details
function editEventPhotoDetails(photoId) {
    // Get current photo data
    fetch(`<?php echo BASE_URL; ?>/image_editor/getEventPhotoDetails/${photoId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Populate form
                document.getElementById('edit_photo_id').value = photoId;
                document.getElementById('edit_photo_category').value = data.photo.category || '';
                document.getElementById('edit_photo_privacy').value = data.photo.privacy_level || 'public';
                document.getElementById('edit_photo_caption').value = data.photo.caption || '';

                // Update character count
                updateEditCaptionCount();

                // Show modal
                new bootstrap.Modal(document.getElementById('editPhotoDetailsModal')).show();
            } else {
                alert('Error loading photo details: ' + (data.message || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error loading photo details');
        });
}

// Save photo details
function savePhotoDetails() {
    const form = document.getElementById('editPhotoDetailsForm');
    const formData = new FormData(form);

    // Disable save button
    const saveBtn = event.target;
    saveBtn.disabled = true;
    saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Saving...';

    fetch(`<?php echo BASE_URL; ?>/image_editor/updateEventPhotoDetails`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Close modal
            bootstrap.Modal.getInstance(document.getElementById('editPhotoDetailsModal')).hide();

            // Show success message
            if (window.pwaFeatures) {
                window.pwaFeatures.showNotification('Success', 'Photo details updated successfully', 'success');
            } else {
                alert('Photo details updated successfully');
            }

            // Reload page to show changes
            location.reload();
        } else {
            alert('Error saving changes: ' + (data.message || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error saving changes');
    })
    .finally(() => {
        // Re-enable save button
        saveBtn.disabled = false;
        saveBtn.innerHTML = '<i class="fas fa-save me-2"></i>Save Changes';
    });
}

// Update caption character count for edit form
function updateEditCaptionCount() {
    const captionField = document.getElementById('edit_photo_caption');
    const captionCount = document.getElementById('edit-caption-count');

    if (captionField && captionCount) {
        const count = captionField.value.length;
        captionCount.textContent = count;

        // Change color based on character count
        if (count > 450) {
            captionCount.style.color = '#dc3545'; // Red
        } else if (count > 400) {
            captionCount.style.color = '#fd7e14'; // Orange
        } else {
            captionCount.style.color = '#6c757d'; // Gray
        }
    }
}

// Add event listener for caption character counting
document.addEventListener('DOMContentLoaded', function() {
    const editCaptionField = document.getElementById('edit_photo_caption');
    if (editCaptionField) {
        editCaptionField.addEventListener('input', updateEditCaptionCount);
    }
});
</script>

<!-- Engagement System CSS -->
<style>
.engagement-section {
    border-top: 1px solid #e9ecef;
    padding-top: 0.75rem;
}

.engagement-actions .btn-link {
    color: #6c757d;
    text-decoration: none;
    font-size: 0.9rem;
}

.engagement-actions .btn-link:hover {
    color: #495057;
}

.like-btn.liked i {
    color: #dc3545 !important;
}

.favorite-btn.favorited i {
    color: #ffc107 !important;
}

.comment {
    font-size: 0.85rem;
}

.comment img {
    border: 1px solid #dee2e6;
}

.comments-section {
    max-height: 300px;
    overflow-y: auto;
}

.engagement-actions .dropdown-toggle::after {
    display: none;
}
</style>

<!-- Engagement JavaScript -->
<script src="<?php echo BASE_URL; ?>/public/js/engagement.js"></script>

<!-- Simple Gallery JavaScript -->
<script>
document.body.dataset.loggedIn = '<?php echo isset($_SESSION['user_id']) ? 'true' : 'false'; ?>';

document.addEventListener('DOMContentLoaded', function() {
    // Handle filter changes
    const filterSelect = document.getElementById('photoFilter');
    if (filterSelect) {
        filterSelect.addEventListener('change', function() {
            const currentUrl = new URL(window.location);
            currentUrl.searchParams.set('filter', this.value);
            currentUrl.searchParams.set('page', '1'); // Reset to page 1
            window.location.href = currentUrl.toString();
        });
    }

    // Handle search
    const searchInput = document.getElementById('photoSearch');
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                const currentUrl = new URL(window.location);
                if (this.value.trim()) {
                    currentUrl.searchParams.set('search', this.value.trim());
                } else {
                    currentUrl.searchParams.delete('search');
                }
                currentUrl.searchParams.set('page', '1'); // Reset to page 1
                window.location.href = currentUrl.toString();
            }
        });
    }

    // Handle clear search
    const clearSearchBtn = document.getElementById('clearSearch');
    if (clearSearchBtn) {
        clearSearchBtn.addEventListener('click', function() {
            const currentUrl = new URL(window.location);
            currentUrl.searchParams.delete('search');
            currentUrl.searchParams.set('page', '1');
            window.location.href = currentUrl.toString();
        });
    }


});
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>
