-- Add main_image_id column to calendar_events table
-- This will reference the images table for the main event image

ALTER TABLE `calendar_events` 
ADD COLUMN `main_image_id` int(10) UNSIGNED DEFAULT NULL AFTER `show_id`,
ADD INDEX `idx_main_image_id` (`main_image_id`);

-- Add foreign key constraint to ensure referential integrity
ALTER TABLE `calendar_events` 
ADD CONSTRAINT `fk_calendar_events_main_image` 
FOREIGN KEY (`main_image_id`) REFERENCES `images` (`id`) 
ON DELETE SET NULL ON UPDATE CASCADE;