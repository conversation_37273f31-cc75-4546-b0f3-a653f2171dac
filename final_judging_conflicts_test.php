<?php
/**
 * Final Judging Conflicts System Test
 */

require_once 'config/config.php';
require_once 'core/Database.php';

echo "<h1>Final Judging Conflicts System Test</h1>\n";

$allPassed = true;

try {
    // Test 1: Database tables
    echo "<h2>1. Database Tables Test</h2>\n";
    $db = new Database();
    
    $tables = ['judging_conflicts', 'judging_conflict_comments', 'judging_conflict_related_scores', 'judging_conflict_related_judges'];
    foreach ($tables as $table) {
        try {
            $db->query("SELECT COUNT(*) as count FROM $table");
            $result = $db->single();
            echo "<p style='color: green;'>✓ Table $table accessible (count: {$result->count})</p>\n";
        } catch (Exception $e) {
            echo "<p style='color: red;'>✗ Table $table error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
            $allPassed = false;
        }
    }
    
    // Test 2: Settings
    echo "<h2>2. Configuration Settings Test</h2>\n";
    try {
        $db->query("SELECT name, value FROM settings WHERE name LIKE 'conflict_%' ORDER BY name");
        $settings = $db->resultSet();
        
        if (empty($settings)) {
            echo "<p style='color: red;'>✗ No conflict settings found</p>\n";
            $allPassed = false;
        } else {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>\n";
            echo "<tr><th>Setting</th><th>Value</th></tr>\n";
            foreach ($settings as $setting) {
                echo "<tr><td>{$setting->name}</td><td>{$setting->value}</td></tr>\n";
            }
            echo "</table>\n";
            echo "<p style='color: green;'>✓ Found " . count($settings) . " conflict settings</p>\n";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Settings error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
        $allPassed = false;
    }
    
    // Test 3: Model loading and functionality
    echo "<h2>3. Model Loading and Functionality Test</h2>\n";
    try {
        require_once APPROOT . '/models/JudgingConflictModel.php';
        $conflictModel = new JudgingConflictModel();
        echo "<p style='color: green;'>✓ JudgingConflictModel loaded successfully</p>\n";
        
        // Test statistics method
        $stats = $conflictModel->getConflictStatistics();
        echo "<p style='color: green;'>✓ getConflictStatistics() method works</p>\n";
        
        // Test conflicts retrieval
        $conflicts = $conflictModel->getConflicts();
        echo "<p style='color: green;'>✓ getConflicts() method works (found " . count($conflicts) . " conflicts)</p>\n";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Model error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
        $allPassed = false;
    }
    
    // Test 4: Controller loading
    echo "<h2>4. Controller Loading Test</h2>\n";
    try {
        require_once APPROOT . '/controllers/JudgingConflictController.php';
        echo "<p style='color: green;'>✓ JudgingConflictController loaded successfully</p>\n";
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Controller error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
        $allPassed = false;
    }
    
    // Test 5: View files
    echo "<h2>5. View Files Test</h2>\n";
    $viewFiles = [
        'views/judging_conflict/dashboard.php',
        'views/judging_conflict/view.php',
        'views/judging_conflict/report.php',
        'views/judging_conflict/my_reports.php'
    ];
    
    $viewsExist = 0;
    foreach ($viewFiles as $viewFile) {
        $fullPath = APPROOT . '/' . $viewFile;
        if (file_exists($fullPath)) {
            echo "<p style='color: green;'>✓ View file exists: $viewFile</p>\n";
            $viewsExist++;
        } else {
            echo "<p style='color: red;'>✗ View file missing: $viewFile</p>\n";
            $allPassed = false;
        }
    }
    
    // Test 6: Foreign key constraints
    echo "<h2>6. Foreign Key Constraints Test</h2>\n";
    try {
        $db->query("SELECT 
            TABLE_NAME,
            COLUMN_NAME,
            CONSTRAINT_NAME,
            REFERENCED_TABLE_NAME,
            REFERENCED_COLUMN_NAME
        FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
        WHERE REFERENCED_TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME LIKE 'judging_conflict%'
        ORDER BY TABLE_NAME, COLUMN_NAME");
        
        $constraints = $db->resultSet();
        
        if (empty($constraints)) {
            echo "<p style='color: orange;'>⚠ No foreign key constraints found (optional)</p>\n";
        } else {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>\n";
            echo "<tr><th>Table</th><th>Column</th><th>References</th></tr>\n";
            foreach ($constraints as $constraint) {
                echo "<tr>";
                echo "<td>{$constraint->TABLE_NAME}</td>";
                echo "<td>{$constraint->COLUMN_NAME}</td>";
                echo "<td>{$constraint->REFERENCED_TABLE_NAME}.{$constraint->REFERENCED_COLUMN_NAME}</td>";
                echo "</tr>\n";
            }
            echo "</table>\n";
            echo "<p style='color: green;'>✓ Found " . count($constraints) . " foreign key constraints</p>\n";
        }
    } catch (Exception $e) {
        echo "<p style='color: orange;'>⚠ Foreign key check failed: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    }
    
    echo "<hr>\n";
    echo "<h2>System Status</h2>\n";
    
    if ($allPassed) {
        echo "<p style='color: green; font-weight: bold; font-size: 1.2em;'>🎉 ALL TESTS PASSED!</p>\n";
        echo "<p style='color: green;'>The Judging Conflicts System is fully functional and ready for use.</p>\n";
    } else {
        echo "<p style='color: orange; font-weight: bold; font-size: 1.2em;'>⚠ SOME TESTS FAILED</p>\n";
        echo "<p style='color: orange;'>The system may still be functional, but some features might not work properly.</p>\n";
    }
    
    echo "<h3>🚀 System Ready - Access Points:</h3>\n";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>\n";
    echo "<ul>\n";
    echo "<li><strong>Admin Dashboard:</strong> <a href='" . BASE_URL . "/judging_conflict/dashboard' target='_blank'>" . BASE_URL . "/judging_conflict/dashboard</a></li>\n";
    echo "<li><strong>Report Conflict:</strong> <a href='" . BASE_URL . "/judging_conflict/report' target='_blank'>" . BASE_URL . "/judging_conflict/report</a></li>\n";
    echo "<li><strong>My Reports:</strong> <a href='" . BASE_URL . "/judging_conflict/my_reports' target='_blank'>" . BASE_URL . "/judging_conflict/my_reports</a></li>\n";
    echo "<li><strong>View Conflict:</strong> " . BASE_URL . "/judging_conflict/viewConflict/{conflict_id}</li>\n";
    echo "</ul>\n";
    echo "</div>\n";
    
    echo "<h3>📋 Features Available:</h3>\n";
    echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 10px 0;'>\n";
    echo "<ul>\n";
    echo "<li>✅ <strong>Conflict Reporting:</strong> Users can report judging conflicts</li>\n";
    echo "<li>✅ <strong>Admin Management:</strong> Admins can review and resolve conflicts</li>\n";
    echo "<li>✅ <strong>Automatic Detection:</strong> System detects score discrepancies</li>\n";
    echo "<li>✅ <strong>Comment System:</strong> Internal and public comments for resolution</li>\n";
    echo "<li>✅ <strong>Priority Tracking:</strong> Low, Normal, High, Urgent priorities</li>\n";
    echo "<li>✅ <strong>Status Management:</strong> Open, Under Review, Resolved, Dismissed, Escalated</li>\n";
    echo "<li>✅ <strong>Statistical Reporting:</strong> Comprehensive conflict statistics</li>\n";
    echo "<li>✅ <strong>Mobile Responsive:</strong> Works on all devices</li>\n";
    echo "<li>✅ <strong>Notification System:</strong> Integrated with UnifiedMessageModel</li>\n";
    echo "</ul>\n";
    echo "</div>\n";
    
    echo "<h3>⚙️ Configuration Settings:</h3>\n";
    echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>\n";
    echo "<ul>\n";
    echo "<li><strong>Auto Detection:</strong> Enabled by default</li>\n";
    echo "<li><strong>Score Threshold:</strong> 15% discrepancy triggers detection</li>\n";
    echo "<li><strong>Notifications:</strong> Enabled for all conflict events</li>\n";
    echo "<li><strong>Time Limit:</strong> 72 hours to report conflicts</li>\n";
    echo "<li><strong>Escalation:</strong> 48 hours before auto-escalation</li>\n";
    echo "</ul>\n";
    echo "<p><em>These can be modified in the admin settings panel.</em></p>\n";
    echo "</div>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>Test failed:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
    $allPassed = false;
}

echo "<hr>\n";
echo "<p><a href='" . BASE_URL . "'>← Return to Home</a> | <a href='" . BASE_URL . "/judging_conflict/dashboard'>Go to Dashboard</a></p>\n";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 900px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
}

p {
    margin: 5px 0;
}

hr {
    margin: 20px 0;
    border: none;
    border-top: 1px solid #ccc;
}

ul {
    padding-left: 20px;
}

table {
    width: 100%;
    margin: 10px 0;
}

th, td {
    padding: 8px;
    text-align: left;
    border: 1px solid #ddd;
}

th {
    background-color: #f2f2f2;
    font-weight: bold;
}

tr:nth-child(even) {
    background-color: #f9f9f9;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

div {
    margin: 10px 0;
}
</style>