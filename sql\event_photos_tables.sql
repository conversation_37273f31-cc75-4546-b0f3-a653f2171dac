-- Event Photo Sharing System Database Schema (INTEGRATED APPROACH)
-- Version: 2.0 - Integrated with existing image system
-- Date: 2025-01-29
--
-- IMPORTANT: This system integrates with your existing image upload system.
-- Event photos are stored in the existing 'images' table with entity_type = 'event_photo'
-- This file only creates the additional metadata table needed for event-specific features.
--
-- DO NOT CREATE SEPARATE event_photos TABLE - Use existing images table instead!

-- Custom Categories Table for unlimited category support
CREATE TABLE IF NOT EXISTS event_photo_custom_categories (
    id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    category_key VARCHAR(50) NOT NULL UNIQUE,
    category_label VARCHAR(100) NOT NULL,
    emoji_icon VARCHAR(10) NOT NULL,
    sort_order INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_active_sort (is_active, sort_order),
    INDEX idx_category_key (category_key)
);

-- Event Photo Metadata Table
-- This extends the existing images table to add event photo specific metadata
CREATE TABLE IF NOT EXISTS event_photo_metadata (
    id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
    image_id INT UNSIGNED NOT NULL,
    category VARCHAR(50) NOT NULL DEFAULT 'atmosphere',
    caption TEXT,
    privacy_level ENUM('public', 'attendees', 'friends', 'private') DEFAULT 'public',
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    event_type ENUM('event', 'show') NOT NULL,
    event_id INT UNSIGNED NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (image_id) REFERENCES images(id) ON DELETE CASCADE,
    FOREIGN KEY (category) REFERENCES event_photo_custom_categories(category_key) ON UPDATE CASCADE,
    INDEX idx_event_photo_image (image_id),
    INDEX idx_event_photo_event (event_type, event_id),
    INDEX idx_event_photo_category (category),
    INDEX idx_event_photo_privacy (privacy_level),
    INDEX idx_event_photo_location (latitude, longitude)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default categories
INSERT INTO event_photo_custom_categories (category_key, category_label, emoji_icon, sort_order, is_active) VALUES
('vehicle', 'Vehicle', '🚗', 1, TRUE),
('atmosphere', 'Atmosphere', '🎪', 2, TRUE),
('awards', 'Awards', '🏆', 3, TRUE),
('vendors', 'Vendors', '🍔', 4, TRUE),
('people', 'People', '👥', 5, TRUE),
('engine', 'Engine Bay', '🔧', 6, TRUE),
('wheels', 'Wheels & Tires', '⚙️', 7, TRUE),
('interior', 'Interior', '🪑', 8, TRUE),
('exterior', 'Exterior', '✨', 9, TRUE),
('paint', 'Paint & Graphics', '🎨', 10, TRUE),
('suspension', 'Suspension', '🏁', 11, TRUE),
('exhaust', 'Exhaust', '💨', 12, TRUE),
('sound_system', 'Sound System', '🔊', 13, TRUE),
('racing', 'Racing Action', '🏎️', 14, TRUE),
('burnout', 'Burnouts', '🔥', 15, TRUE),
('dyno', 'Dyno Runs', '📊', 16, TRUE),
('judging', 'Judging', '👨‍⚖️', 17, TRUE),
('setup', 'Setup/Prep', '🛠️', 18, TRUE),
('crowd', 'Crowd Shots', '👥', 19, TRUE),
('sponsors', 'Sponsors', '🏢', 20, TRUE);

-- INTEGRATION NOTES:
--
-- Event photos are now stored in your existing 'images' table with:
-- - entity_type = 'event_photo'
-- - entity_id = 'event_123' or 'show_456' (event type + underscore + event ID)
-- - All existing image features work: thumbnails, optimization, management, etc.
--
-- The event_photo_metadata table above stores additional event-specific data:
-- - Photo category (vehicle, atmosphere, awards, vendors, people)
-- - Caption and privacy level
-- - GPS coordinates where photo was taken
-- - Reference to which event/show the photo belongs to
--
-- USAGE:
-- 1. Run this SQL file to create the event_photo_metadata table
-- 2. Event photos will automatically use your existing image upload system
-- 3. Access event photo galleries via: /image_editor/eventGallery/show/123 or /image_editor/eventGallery/event/456
--
-- FEATURES AVAILABLE:
-- ✅ Location-based photo sharing (GPS verification)
-- ✅ Photo categories and captions
-- ✅ Privacy controls
-- ✅ Integration with existing image editor
-- ✅ PWA camera support
-- ✅ Event-specific photo galleries
--
-- FUTURE ENHANCEMENTS (Optional):
-- If you want to add social features like likes/comments later, you can create additional tables
-- that reference the image_id from your existing images table.
