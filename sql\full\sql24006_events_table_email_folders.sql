
-- --------------------------------------------------------

--
-- Table structure for table `email_folders`
--
-- Creation: Jul 17, 2025 at 12:33 PM
--

CREATE TABLE `email_folders` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `color` varchar(7) DEFAULT '#007bff',
  `icon` varchar(50) DEFAULT 'fas fa-folder',
  `is_system` tinyint(1) DEFAULT 0,
  `created_by` int(11) DEFAULT 1,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_uca1400_ai_ci;

--
-- RELATIONSHIPS FOR TABLE `email_folders`:
--

--
-- Dumping data for table `email_folders`
--

INSERT INTO `email_folders` (`id`, `name`, `description`, `color`, `icon`, `is_system`, `created_by`, `created_at`, `updated_at`) VALUES
(1, 'Inbox', 'Default inbox for new emails', '#007bff', 'fas fa-inbox', 1, 1, '2025-07-17 12:33:44', '2025-07-17 12:33:44'),
(2, 'Important', 'High priority emails', '#dc3545', 'fas fa-star', 1, 1, '2025-07-17 12:33:44', '2025-07-17 12:33:44'),
(3, 'Follow Up', 'Emails requiring follow-up', '#ffc107', 'fas fa-clock', 1, 1, '2025-07-17 12:33:44', '2025-07-17 12:33:44');
