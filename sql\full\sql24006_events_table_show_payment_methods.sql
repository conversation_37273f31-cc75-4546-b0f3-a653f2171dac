
-- --------------------------------------------------------

--
-- Table structure for table `show_payment_methods`
--
-- Creation: Jun 23, 2025 at 08:30 PM
--

CREATE TABLE `show_payment_methods` (
  `id` int(10) UNSIGNED NOT NULL,
  `show_id` int(10) UNSIGNED NOT NULL,
  `payment_method_id` int(10) UNSIGNED NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- RELATIONSHIPS FOR TABLE `show_payment_methods`:
--   `show_id`
--       `shows` -> `id`
--   `payment_method_id`
--       `payment_methods` -> `id`
--

--
-- Dumping data for table `show_payment_methods`
--

INSERT INTO `show_payment_methods` (`id`, `show_id`, `payment_method_id`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 5, 7, 1, '2025-06-11 20:56:42', '2025-06-11 20:56:42'),
(2, 5, 6, 1, '2025-06-11 20:56:42', '2025-06-11 20:56:42'),
(3, 5, 5, 1, '2025-06-11 20:56:42', '2025-06-11 20:56:42'),
(4, 5, 4, 1, '2025-06-11 20:56:42', '2025-06-11 20:56:42'),
(5, 5, 3, 1, '2025-06-11 20:56:42', '2025-06-11 20:56:42'),
(6, 5, 2, 1, '2025-06-11 20:56:42', '2025-06-11 20:56:42'),
(7, 5, 1, 1, '2025-06-11 20:56:42', '2025-06-11 20:56:42'),
(8, 9, 7, 1, '2025-06-11 20:56:42', '2025-06-11 20:56:42'),
(9, 9, 6, 1, '2025-06-11 20:56:42', '2025-06-11 20:56:42'),
(10, 9, 5, 1, '2025-06-11 20:56:42', '2025-06-11 20:56:42'),
(11, 9, 4, 1, '2025-06-11 20:56:42', '2025-06-11 20:56:42'),
(12, 9, 3, 1, '2025-06-11 20:56:42', '2025-06-11 20:56:42'),
(13, 9, 2, 1, '2025-06-11 20:56:42', '2025-06-11 20:56:42'),
(14, 9, 1, 1, '2025-06-11 20:56:42', '2025-06-11 20:56:42');
