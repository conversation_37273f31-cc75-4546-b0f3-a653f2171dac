
-- --------------------------------------------------------

--
-- Table structure for table `scores`
--
-- Creation: Jun 23, 2025 at 08:30 PM
--

CREATE TABLE `scores` (
  `id` int(10) UNSIGNED NOT NULL,
  `registration_id` int(10) UNSIGNED NOT NULL,
  `judge_id` int(10) UNSIGNED NOT NULL,
  `metric_id` int(10) UNSIGNED NOT NULL,
  `score` int(11) NOT NULL,
  `comments` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `is_draft` tinyint(1) NOT NULL DEFAULT 0,
  `auto_saved` tinyint(1) NOT NULL DEFAULT 0,
  `last_auto_save` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- RELATIONSHIPS FOR TABLE `scores`:
--   `registration_id`
--       `registrations` -> `id`
--   `judge_id`
--       `judges` -> `id`
--   `metric_id`
--       `judging_metrics` -> `id`
--
