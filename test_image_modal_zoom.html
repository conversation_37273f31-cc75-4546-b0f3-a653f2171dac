<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Modal Zoom Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        /* Enhanced Image Modal Styles */
        .image-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.95);
            z-index: 10000;
            display: none;
            justify-content: center;
            align-items: center;
            touch-action: none;
            user-select: none;
        }

        .image-modal.active {
            display: flex;
        }

        .image-modal-content {
            position: relative;
            max-width: 95vw;
            max-height: 95vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .image-modal-img {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
            transition: transform 0.3s ease;
            cursor: grab;
            touch-action: none;
        }

        .image-modal-img:active {
            cursor: grabbing;
        }

        .image-modal-img.zoomed {
            cursor: grab;
        }

        .image-modal-close {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            font-size: 24px;
            cursor: pointer;
            z-index: 10001;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            color: #333;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }

        .image-modal-close:hover {
            background: rgba(255, 255, 255, 1);
            transform: scale(1.1);
        }

        .image-modal-controls {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 10px;
            background: rgba(0, 0, 0, 0.7);
            padding: 10px 20px;
            border-radius: 25px;
            backdrop-filter: blur(10px);
        }

        .image-modal-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
            min-width: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .image-modal-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
        }

        .image-modal-btn:active {
            transform: scale(0.95);
        }

        .zoom-info {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 8px 12px;
            border-radius: 15px;
            font-size: 12px;
            backdrop-filter: blur(10px);
        }

        /* Mobile-specific styles */
        @media (max-width: 768px) {
            .image-modal-close {
                top: 10px;
                right: 10px;
                width: 44px;
                height: 44px;
                font-size: 20px;
            }
            
            .image-modal-controls {
                bottom: 10px;
                padding: 8px 16px;
            }
            
            .image-modal-btn {
                padding: 6px 10px;
                font-size: 12px;
                min-width: 36px;
            }
            
            .zoom-info {
                top: 10px;
                left: 10px;
                padding: 6px 10px;
                font-size: 11px;
            }
            
            .image-modal-content {
                max-width: 100vw;
                max-height: 100vh;
            }
        }

        /* Test page styles */
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .test-image {
            width: 100%;
            max-width: 400px;
            height: 300px;
            object-fit: cover;
            cursor: pointer;
            border-radius: 8px;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }

        .test-image:hover {
            transform: scale(1.02);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Image Modal Zoom Test</h1>
        <p>Click on the image below to test the enhanced image modal with zoom functionality:</p>
        
        <div class="text-center mb-4">
            <img src="https://picsum.photos/800/600?random=1" 
                 alt="Test Image" 
                 class="test-image"
                 onclick="showImageModal('https://picsum.photos/1200/900?random=1', 'Test Image')">
        </div>
        
        <div class="alert alert-info">
            <h5>Features to Test:</h5>
            <ul class="mb-0">
                <li><strong>Desktop:</strong> Mouse wheel to zoom, drag to pan when zoomed</li>
                <li><strong>Mobile:</strong> Pinch to zoom, drag to pan, double-tap to zoom</li>
                <li><strong>Controls:</strong> Zoom in/out buttons, reset zoom, fullscreen toggle</li>
                <li><strong>Close:</strong> ESC key, close button, or click outside image</li>
            </ul>
        </div>
    </div>

    <script>
        // Enhanced Image Modal with Zoom Functionality
        function showImageModal(imageSrc, imageAlt) {
            // Remove existing modal if present
            const existingModal = document.getElementById('imageModal');
            if (existingModal) {
                existingModal.remove();
            }

            // Create enhanced modal HTML
            const modalHtml = `
                <div class="image-modal" id="imageModal">
                    <button class="image-modal-close" id="closeModal" aria-label="Close">
                        <i class="fas fa-times"></i>
                    </button>
                    
                    <div class="zoom-info" id="zoomInfo">
                        100%
                    </div>
                    
                    <div class="image-modal-content" id="modalContent">
                        <img src="${imageSrc}" alt="${imageAlt}" class="image-modal-img" id="modalImage">
                    </div>
                    
                    <div class="image-modal-controls">
                        <button class="image-modal-btn" id="zoomOut" title="Zoom Out">
                            <i class="fas fa-search-minus"></i>
                        </button>
                        <button class="image-modal-btn" id="zoomReset" title="Reset Zoom">
                            <i class="fas fa-expand-arrows-alt"></i>
                        </button>
                        <button class="image-modal-btn" id="zoomIn" title="Zoom In">
                            <i class="fas fa-search-plus"></i>
                        </button>
                        <button class="image-modal-btn" id="fullscreenBtn" title="Toggle Fullscreen">
                            <i class="fas fa-expand"></i>
                        </button>
                    </div>
                </div>
            `;

            // Add modal to body
            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // Get modal elements
            const modal = document.getElementById('imageModal');
            const modalImage = document.getElementById('modalImage');
            const closeBtn = document.getElementById('closeModal');
            const zoomInfo = document.getElementById('zoomInfo');
            const zoomInBtn = document.getElementById('zoomIn');
            const zoomOutBtn = document.getElementById('zoomOut');
            const zoomResetBtn = document.getElementById('zoomReset');
            const fullscreenBtn = document.getElementById('fullscreenBtn');

            // Modal state
            let scale = 1;
            let translateX = 0;
            let translateY = 0;
            let isDragging = false;
            let startX = 0;
            let startY = 0;
            let lastTouchDistance = 0;
            let isFullscreen = false;

            // Update transform
            function updateTransform() {
                modalImage.style.transform = `translate(${translateX}px, ${translateY}px) scale(${scale})`;
                zoomInfo.textContent = Math.round(scale * 100) + '%';
                
                if (scale > 1) {
                    modalImage.classList.add('zoomed');
                } else {
                    modalImage.classList.remove('zoomed');
                }
            }

            // Reset position and zoom
            function resetTransform() {
                scale = 1;
                translateX = 0;
                translateY = 0;
                updateTransform();
            }

            // Close modal function
            function closeModal() {
                console.log('Closing image modal');
                modal.classList.remove('active');
                document.body.style.overflow = '';
                document.body.style.position = '';
                document.body.style.width = '';
                
                setTimeout(() => {
                    modal.remove();
                }, 300);
            }

            // Show modal
            modal.classList.add('active');
            document.body.style.overflow = 'hidden';
            document.body.style.position = 'fixed';
            document.body.style.width = '100%';

            // Close button events
            closeBtn.addEventListener('click', closeModal);
            closeBtn.addEventListener('touchend', (e) => {
                e.preventDefault();
                closeModal();
            });

            // Click outside to close
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    closeModal();
                }
            });

            // Keyboard events
            document.addEventListener('keydown', function handleKeydown(e) {
                if (e.key === 'Escape') {
                    closeModal();
                    document.removeEventListener('keydown', handleKeydown);
                }
            });

            // Zoom controls
            zoomInBtn.addEventListener('click', () => {
                scale = Math.min(scale * 1.5, 5);
                console.log('Zoom in - new scale:', scale);
                updateTransform();
            });

            zoomOutBtn.addEventListener('click', () => {
                scale = Math.max(scale / 1.5, 0.5);
                console.log('Zoom out - new scale:', scale);
                updateTransform();
            });

            zoomResetBtn.addEventListener('click', resetTransform);

            // Fullscreen toggle
            fullscreenBtn.addEventListener('click', () => {
                isFullscreen = !isFullscreen;
                if (isFullscreen) {
                    modal.style.background = 'rgba(0, 0, 0, 1)';
                    fullscreenBtn.innerHTML = '<i class="fas fa-compress"></i>';
                    fullscreenBtn.title = 'Exit Fullscreen';
                } else {
                    modal.style.background = 'rgba(0, 0, 0, 0.95)';
                    fullscreenBtn.innerHTML = '<i class="fas fa-expand"></i>';
                    fullscreenBtn.title = 'Toggle Fullscreen';
                }
            });

            // Mouse wheel zoom
            modalImage.addEventListener('wheel', (e) => {
                e.preventDefault();
                const delta = e.deltaY > 0 ? 0.9 : 1.1;
                scale = Math.max(0.5, Math.min(5, scale * delta));
                updateTransform();
            });

            // Mouse drag
            modalImage.addEventListener('mousedown', (e) => {
                if (scale > 1) {
                    isDragging = true;
                    startX = e.clientX - translateX;
                    startY = e.clientY - translateY;
                    modalImage.style.cursor = 'grabbing';
                }
            });

            document.addEventListener('mousemove', (e) => {
                if (isDragging && scale > 1) {
                    translateX = e.clientX - startX;
                    translateY = e.clientY - startY;
                    updateTransform();
                }
            });

            document.addEventListener('mouseup', () => {
                isDragging = false;
                modalImage.style.cursor = scale > 1 ? 'grab' : 'default';
            });

            // Touch events for mobile - Fixed pinch-to-zoom
            let touches = [];
            let initialPinchDistance = 0;
            let initialScale = 1;
            let isPinching = false;

            modalImage.addEventListener('touchstart', (e) => {
                e.preventDefault();
                touches = Array.from(e.touches);
                
                console.log('Touch start - touches:', touches.length);
                
                if (touches.length === 1) {
                    // Single touch - start drag
                    if (scale > 1) {
                        isDragging = true;
                        startX = touches[0].clientX - translateX;
                        startY = touches[0].clientY - translateY;
                        console.log('Starting drag at scale:', scale);
                    }
                    isPinching = false;
                } else if (touches.length === 2) {
                    // Two finger pinch - initialize pinch state
                    isDragging = false;
                    isPinching = true;
                    
                    const dx = touches[0].clientX - touches[1].clientX;
                    const dy = touches[0].clientY - touches[1].clientY;
                    initialPinchDistance = Math.sqrt(dx * dx + dy * dy);
                    initialScale = scale; // Store the current scale when pinch starts
                    
                    console.log('Pinch start - initial distance:', initialPinchDistance, 'initial scale:', initialScale);
                }
            });

            modalImage.addEventListener('touchmove', (e) => {
                e.preventDefault();
                touches = Array.from(e.touches);
                
                if (touches.length === 1 && isDragging && scale > 1) {
                    // Single touch drag
                    translateX = touches[0].clientX - startX;
                    translateY = touches[0].clientY - startY;
                    updateTransform();
                } else if (touches.length === 2 && isPinching) {
                    // Pinch zoom - calculate relative to initial pinch distance
                    const dx = touches[0].clientX - touches[1].clientX;
                    const dy = touches[0].clientY - touches[1].clientY;
                    const currentDistance = Math.sqrt(dx * dx + dy * dy);
                    
                    if (initialPinchDistance > 0) {
                        // Calculate scale relative to initial pinch distance and scale
                        const pinchRatio = currentDistance / initialPinchDistance;
                        const newScale = initialScale * pinchRatio;
                        scale = Math.max(0.5, Math.min(5, newScale));
                        
                        console.log('Pinch move - ratio:', pinchRatio.toFixed(2), 'new scale:', scale.toFixed(2));
                        
                        updateTransform();
                    }
                }
            });

            modalImage.addEventListener('touchend', (e) => {
                e.preventDefault();
                touches = Array.from(e.touches);
                
                console.log('Touch end - remaining touches:', touches.length, 'final scale:', scale);
                
                if (touches.length === 0) {
                    // All fingers lifted
                    isDragging = false;
                    isPinching = false;
                    initialPinchDistance = 0;
                    initialScale = scale; // Preserve the current scale
                } else if (touches.length === 1) {
                    // One finger lifted during pinch
                    isPinching = false;
                    initialPinchDistance = 0;
                    // Don't reset scale - keep the current zoom level
                    
                    // If we still have scale > 1, prepare for potential drag
                    if (scale > 1) {
                        startX = touches[0].clientX - translateX;
                        startY = touches[0].clientY - translateY;
                        isDragging = true;
                    }
                }
            });

            // Double tap to zoom - separate event handler to avoid conflicts
            let lastTap = 0;
            let tapTimeout = null;
            
            modalImage.addEventListener('touchstart', (e) => {
                // Only handle double-tap for single finger touches
                if (e.touches.length === 1 && !isPinching) {
                    const currentTime = new Date().getTime();
                    const tapLength = currentTime - lastTap;
                    
                    if (tapLength < 300 && tapLength > 0) {
                        // Double tap detected
                        e.preventDefault();
                        
                        console.log('Double tap detected - current scale:', scale);
                        
                        if (scale <= 1.1) {
                            // Zoom in to 2x
                            scale = 2;
                            initialScale = scale;
                        } else {
                            // Reset zoom
                            resetTransform();
                            initialScale = 1;
                        }
                        updateTransform();
                        
                        // Clear any pending single tap
                        if (tapTimeout) {
                            clearTimeout(tapTimeout);
                            tapTimeout = null;
                        }
                        
                        lastTap = 0; // Reset to prevent triple tap
                    } else {
                        lastTap = currentTime;
                    }
                }
            });

            // Initialize
            updateTransform();
        }
    </script>
</body>
</html>