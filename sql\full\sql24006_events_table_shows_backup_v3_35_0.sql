
-- --------------------------------------------------------

--
-- Table structure for table `shows_backup_v3_35_0`
--
-- Creation: Jun 23, 2025 at 08:30 PM
--

CREATE TABLE `shows_backup_v3_35_0` (
  `id` int(10) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `location` varchar(255) NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `registration_start` date NOT NULL,
  `registration_end` date NOT NULL,
  `coordinator_id` int(10) UNSIGNED DEFAULT NULL,
  `status` varchar(50) NOT NULL DEFAULT 'draft',
  `fan_voting_enabled` tinyint(1) DEFAULT NULL,
  `banner_image` varchar(255) DEFAULT NULL,
  `form_template_id` int(11) DEFAULT NULL,
  `registration_fee` decimal(10,2) NOT NULL DEFAULT 0.00,
  `is_free` tinyint(1) NOT NULL DEFAULT 0,
  `listing_fee` decimal(10,2) NOT NULL DEFAULT 0.00,
  `listing_paid` tinyint(1) NOT NULL DEFAULT 0,
  `featured_image_id` int(10) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `template_id` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- RELATIONSHIPS FOR TABLE `shows_backup_v3_35_0`:
--

--
-- Dumping data for table `shows_backup_v3_35_0`
--

INSERT INTO `shows_backup_v3_35_0` (`id`, `name`, `description`, `location`, `start_date`, `end_date`, `registration_start`, `registration_end`, `coordinator_id`, `status`, `fan_voting_enabled`, `banner_image`, `form_template_id`, `registration_fee`, `is_free`, `listing_fee`, `listing_paid`, `featured_image_id`, `created_at`, `updated_at`, `template_id`) VALUES
(5, 'Name of show', 'This is a description of a carRoll up to the biggest auto bash of the year and feast your eyes on everything from classic roadsters to souped-up tuners.  We\'ve got chrome, curves, and horsepower galore, with something to rev everyone\'s engine.  Check out the off-road rigs tearing up the dirt track and admire the sleek lines of vintage sports cars. Bikes, trucks, and even custom creations will be on display, so don\'t miss out!', 'Salisbury nc', '2025-06-05', '2025-06-05', '2025-05-28', '2025-06-02', 5, 'completed', 1, NULL, NULL, 0.00, 1, 0.00, 0, NULL, '2025-06-01 11:01:03', '2025-06-08 11:33:51', NULL),
(9, 'Summer Classic Auto Show 2025', 'Welcome to the Summer Classic Auto Show 2025! This premier automotive event showcases the finest classic, custom, and modern vehicles from across the region. From meticulously restored vintage classics to cutting-edge custom builds, this show offers something for every automotive enthusiast. Enjoy live music, food vendors, and special guest appearances throughout the weekend. Bring the whole family for a day filled with automotive excellence and community spirit. All proceeds benefit local youth automotive education programs.', 'Riverside Park, 123 Main Street, Anytown, USA', '2025-07-08', '2025-07-09', '2025-05-09', '2025-07-03', 13, 'published', 1, NULL, NULL, 20.00, 0, 0.00, 0, NULL, '2025-06-08 13:38:32', '2025-06-12 22:42:18', NULL),
(14, 'gfdgdfgdf', 'gdfgfdgdf', 'gdfgdfg', '2025-06-21', '2025-06-21', '2025-06-12', '2025-06-17', 13, 'payment_pending', 1, NULL, NULL, 25.00, 0, 20.00, 0, NULL, '2025-06-12 23:47:46', '2025-06-12 23:47:46', NULL);
