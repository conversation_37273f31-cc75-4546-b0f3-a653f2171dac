
-- --------------------------------------------------------

--
-- Table structure for table `calendar_event_images`
--
-- Creation: Jun 23, 2025 at 08:30 PM
--

CREATE TABLE `calendar_event_images` (
  `id` int(10) UNSIGNED NOT NULL,
  `event_id` int(10) UNSIGNED NOT NULL,
  `user_id` int(10) UNSIGNED NOT NULL,
  `image_data` longtext NOT NULL COMMENT 'Base64 encoded image data',
  `file_name` varchar(255) NOT NULL,
  `file_type` varchar(50) NOT NULL,
  `file_size` int(11) NOT NULL COMMENT 'Original file size in bytes',
  `width` int(11) DEFAULT NULL,
  `height` int(11) DEFAULT NULL,
  `alt_text` varchar(255) DEFAULT NULL,
  `caption` text DEFAULT NULL,
  `display_order` int(11) DEFAULT 0,
  `is_featured` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Featured image for social sharing',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores Base64 encoded images for calendar events';

--
-- RELATIONSHIPS FOR TABLE `calendar_event_images`:
--   `event_id`
--       `calendar_events` -> `id`
--   `user_id`
--       `users` -> `id`
--

--
-- Triggers `calendar_event_images`
--
DELIMITER $$
CREATE TRIGGER `tr_event_images_featured_unique` BEFORE INSERT ON `calendar_event_images` FOR EACH ROW BEGIN
    IF NEW.is_featured = 1 THEN
        UPDATE `calendar_event_images` 
        SET `is_featured` = 0 
        WHERE `event_id` = NEW.event_id AND `is_featured` = 1;
    END IF;
END
$$
DELIMITER ;
DELIMITER $$
CREATE TRIGGER `tr_event_images_featured_unique_update` BEFORE UPDATE ON `calendar_event_images` FOR EACH ROW BEGIN
    IF NEW.is_featured = 1 AND OLD.is_featured = 0 THEN
        UPDATE `calendar_event_images` 
        SET `is_featured` = 0 
        WHERE `event_id` = NEW.event_id AND `id` != NEW.id AND `is_featured` = 1;
    END IF;
END
$$
DELIMITER ;
