-- Migration: Add Judging Conflicts Tables
-- Description: Creates tables for managing judging conflicts and disputes
-- Version: 1.0
-- Date: 2024-12-19

-- Create judging_conflicts table
CREATE TABLE IF NOT EXISTS `judging_conflicts` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `show_id` int(11) NOT NULL,
    `registration_id` int(11) DEFAULT NULL,
    `title` varchar(255) NOT NULL,
    `description` text NOT NULL,
    `conflict_type` enum('score_discrepancy','assignment_conflict','scoring_dispute','technical_error','owner_complaint','judge_concern') NOT NULL,
    `priority` enum('low','normal','high','urgent') NOT NULL DEFAULT 'normal',
    `status` enum('open','under_review','resolved','dismissed','escalated') NOT NULL DEFAULT 'open',
    `reported_by_user_id` int(11) NOT NULL,
    `reported_by_role` enum('user','judge','coordinator','admin') NOT NULL,
    `assigned_to_admin_id` int(11) DEFAULT NULL,
    `resolved_by_admin_id` int(11) DEFAULT NULL,
    `resolution_notes` text DEFAULT NULL,
    `resolution_action` text DEFAULT NULL,
    `auto_detected` tinyint(1) NOT NULL DEFAULT 0,
    `detection_criteria` text DEFAULT NULL,
    `related_data` json DEFAULT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `resolved_at` timestamp NULL DEFAULT NULL,
    `escalated_at` timestamp NULL DEFAULT NULL,
    PRIMARY KEY (`id`),
    KEY `idx_show_id` (`show_id`),
    KEY `idx_registration_id` (`registration_id`),
    KEY `idx_status` (`status`),
    KEY `idx_priority` (`priority`),
    KEY `idx_reported_by` (`reported_by_user_id`),
    KEY `idx_assigned_to` (`assigned_to_admin_id`),
    KEY `idx_created_at` (`created_at`),
    CONSTRAINT `fk_conflicts_show` FOREIGN KEY (`show_id`) REFERENCES `shows` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_conflicts_registration` FOREIGN KEY (`registration_id`) REFERENCES `registrations` (`id`) ON DELETE SET NULL,
    CONSTRAINT `fk_conflicts_reported_by` FOREIGN KEY (`reported_by_user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_conflicts_assigned_to` FOREIGN KEY (`assigned_to_admin_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
    CONSTRAINT `fk_conflicts_resolved_by` FOREIGN KEY (`resolved_by_admin_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create judging_conflict_comments table
CREATE TABLE IF NOT EXISTS `judging_conflict_comments` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `conflict_id` int(11) NOT NULL,
    `user_id` int(11) NOT NULL,
    `comment` text NOT NULL,
    `is_internal` tinyint(1) NOT NULL DEFAULT 0,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_conflict_id` (`conflict_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_created_at` (`created_at`),
    CONSTRAINT `fk_comments_conflict` FOREIGN KEY (`conflict_id`) REFERENCES `judging_conflicts` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_comments_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create judging_conflict_related_scores table (for linking conflicts to specific scores)
CREATE TABLE IF NOT EXISTS `judging_conflict_related_scores` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `conflict_id` int(11) NOT NULL,
    `score_id` int(11) NOT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_conflict_score` (`conflict_id`, `score_id`),
    KEY `idx_conflict_id` (`conflict_id`),
    KEY `idx_score_id` (`score_id`),
    CONSTRAINT `fk_related_scores_conflict` FOREIGN KEY (`conflict_id`) REFERENCES `judging_conflicts` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_related_scores_score` FOREIGN KEY (`score_id`) REFERENCES `scores` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create judging_conflict_related_judges table (for linking conflicts to specific judges)
CREATE TABLE IF NOT EXISTS `judging_conflict_related_judges` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `conflict_id` int(11) NOT NULL,
    `judge_id` int(11) NOT NULL,
    `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `unique_conflict_judge` (`conflict_id`, `judge_id`),
    KEY `idx_conflict_id` (`conflict_id`),
    KEY `idx_judge_id` (`judge_id`),
    CONSTRAINT `fk_related_judges_conflict` FOREIGN KEY (`conflict_id`) REFERENCES `judging_conflicts` (`id`) ON DELETE CASCADE,
    CONSTRAINT `fk_related_judges_judge` FOREIGN KEY (`judge_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add settings for conflict management
INSERT IGNORE INTO `settings` (`name`, `value`, `description`) VALUES
('conflict_auto_detection_enabled', '1', 'Enable automatic conflict detection when scores are finalized'),
('conflict_score_discrepancy_threshold', '15', 'Percentage threshold for score discrepancy detection'),
('conflict_notification_enabled', '1', 'Send notifications when conflicts are created or updated'),
('conflict_time_limit_hours', '72', 'Time limit in hours for reporting conflicts after results are posted'),
('conflict_escalation_threshold_hours', '48', 'Hours after which unresolved conflicts are escalated');

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS `idx_conflicts_status_priority` ON `judging_conflicts` (`status`, `priority`);
CREATE INDEX IF NOT EXISTS `idx_conflicts_show_status` ON `judging_conflicts` (`show_id`, `status`);
CREATE INDEX IF NOT EXISTS `idx_conflicts_auto_detected` ON `judging_conflicts` (`auto_detected`);

-- Add a view for conflict statistics
CREATE OR REPLACE VIEW `judging_conflict_stats` AS
SELECT 
    COUNT(*) as total_conflicts,
    SUM(CASE WHEN status = 'open' THEN 1 ELSE 0 END) as open_conflicts,
    SUM(CASE WHEN status = 'under_review' THEN 1 ELSE 0 END) as under_review_conflicts,
    SUM(CASE WHEN status = 'resolved' THEN 1 ELSE 0 END) as resolved_conflicts,
    SUM(CASE WHEN status = 'dismissed' THEN 1 ELSE 0 END) as dismissed_conflicts,
    SUM(CASE WHEN status = 'escalated' THEN 1 ELSE 0 END) as escalated_conflicts,
    SUM(CASE WHEN priority = 'urgent' THEN 1 ELSE 0 END) as urgent_conflicts,
    SUM(CASE WHEN priority = 'high' THEN 1 ELSE 0 END) as high_conflicts,
    SUM(CASE WHEN auto_detected = 1 THEN 1 ELSE 0 END) as auto_detected_conflicts,
    AVG(CASE 
        WHEN resolved_at IS NOT NULL 
        THEN TIMESTAMPDIFF(HOUR, created_at, resolved_at) 
        ELSE NULL 
    END) as avg_resolution_hours,
    COUNT(DISTINCT show_id) as shows_with_conflicts,
    COUNT(DISTINCT reported_by_user_id) as users_reporting_conflicts
FROM judging_conflicts;