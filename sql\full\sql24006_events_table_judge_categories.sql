
-- --------------------------------------------------------

--
-- Table structure for table `judge_categories`
--
-- Creation: Jun 23, 2025 at 08:30 PM
--

CREATE TABLE `judge_categories` (
  `id` int(11) NOT NULL,
  `judge_id` int(11) NOT NULL,
  `category_id` int(11) NOT NULL,
  `created_at` datetime DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- RELATIONSHIPS FOR TABLE `judge_categories`:
--

--
-- Dumping data for table `judge_categories`
--

INSERT INTO `judge_categories` (`id`, `judge_id`, `category_id`, `created_at`) VALUES
(19, 1, 104, '2025-06-02 12:31:30'),
(20, 1, 105, '2025-06-02 12:31:30'),
(21, 1, 106, '2025-06-02 12:31:30'),
(22, 1, 107, '2025-06-02 12:31:30'),
(23, 1, 108, '2025-06-02 12:31:30'),
(24, 1, 109, '2025-06-02 12:31:30'),
(25, 1, 110, '2025-06-02 12:31:30'),
(26, 1, 111, '2025-06-02 12:31:30'),
(27, 1, 112, '2025-06-02 12:31:30'),
(28, 2, 121, '2025-06-02 16:26:31'),
(29, 3, 121, '2025-06-04 16:27:42'),
(60, 94, 152, '2025-07-08 01:51:41'),
(61, 94, 153, '2025-07-08 01:51:41'),
(62, 94, 158, '2025-07-08 01:51:41'),
(63, 95, 827, '2025-07-09 21:05:26'),
(64, 95, 828, '2025-07-09 21:05:26'),
(65, 95, 829, '2025-07-09 21:05:26'),
(66, 95, 830, '2025-07-09 21:05:26'),
(67, 95, 831, '2025-07-09 21:05:26'),
(68, 96, 827, '2025-07-09 21:05:26'),
(69, 96, 828, '2025-07-09 21:05:26'),
(70, 96, 829, '2025-07-09 21:05:26'),
(71, 96, 830, '2025-07-09 21:05:26'),
(72, 96, 831, '2025-07-09 21:05:26'),
(73, 97, 827, '2025-07-09 21:05:26'),
(74, 97, 828, '2025-07-09 21:05:26'),
(75, 97, 829, '2025-07-09 21:05:26'),
(76, 97, 830, '2025-07-09 21:05:26'),
(77, 97, 831, '2025-07-09 21:05:26'),
(78, 98, 846, '2025-07-10 01:55:28'),
(79, 98, 847, '2025-07-10 01:55:28'),
(80, 98, 848, '2025-07-10 01:55:28'),
(81, 98, 849, '2025-07-10 01:55:28'),
(154, 102, 152, '2025-07-11 11:37:05'),
(155, 102, 153, '2025-07-11 11:37:05'),
(156, 102, 154, '2025-07-11 11:37:05'),
(157, 102, 155, '2025-07-11 11:37:05'),
(158, 102, 156, '2025-07-11 11:37:05'),
(159, 102, 157, '2025-07-11 11:37:05'),
(160, 102, 158, '2025-07-11 11:37:05'),
(161, 102, 159, '2025-07-11 11:37:05'),
(162, 102, 160, '2025-07-11 11:37:05');
