
-- --------------------------------------------------------

--
-- Table structure for table `show_role_requests`
--
-- Creation: Jul 11, 2025 at 10:54 AM
--

CREATE TABLE `show_role_requests` (
  `id` int(11) NOT NULL,
  `show_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `requested_role` enum('coordinator','judge','staff') NOT NULL,
  `requested_by` int(11) NOT NULL COMMENT 'User ID of coordinator/admin who made the request',
  `status` enum('pending','approved','declined','expired') NOT NULL DEFAULT 'pending',
  `denial_note` text DEFAULT NULL,
  `denied_by` int(10) UNSIGNED DEFAULT NULL,
  `denied_at` timestamp NULL DEFAULT NULL,
  `request_message` text DEFAULT NULL COMMENT 'Optional message from requester',
  `response_message` text DEFAULT NULL COMMENT 'Optional message from user when responding',
  `requested_at` datetime NOT NULL DEFAULT current_timestamp(),
  `responded_at` datetime DEFAULT NULL,
  `expires_at` datetime NOT NULL COMMENT 'When this request expires if not responded to',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_uca1400_ai_ci COMMENT='Stores role assignment requests awaiting user approval';

--
-- RELATIONSHIPS FOR TABLE `show_role_requests`:
--   `denied_by`
--       `users` -> `id`
--

--
-- Dumping data for table `show_role_requests`
--

INSERT INTO `show_role_requests` (`id`, `show_id`, `user_id`, `requested_role`, `requested_by`, `status`, `denial_note`, `denied_by`, `denied_at`, `request_message`, `response_message`, `requested_at`, `responded_at`, `expires_at`, `created_at`, `updated_at`) VALUES
(1, 113, 3, 'judge', 2406, 'approved', NULL, NULL, NULL, 'test', 'thanks', '2025-07-10 21:48:31', '2025-07-10 21:52:36', '2025-07-17 21:48:31', '2025-07-10 21:48:31', '2025-07-10 21:52:36'),
(4, 9, 3, 'judge', 3, 'approved', NULL, NULL, NULL, NULL, NULL, '2025-07-11 11:37:05', '2025-07-11 11:37:15', '0000-00-00 00:00:00', '2025-07-11 11:37:05', '2025-07-11 11:37:15');
