
-- --------------------------------------------------------

--
-- Table structure for table `engagement_notifications`
--
-- Creation: Aug 01, 2025 at 01:07 PM
--

CREATE TABLE `engagement_notifications` (
  `id` int(10) UNSIGNED NOT NULL,
  `user_id` int(10) UNSIGNED NOT NULL,
  `photo_id` int(10) UNSIGNED NOT NULL,
  `type` enum('like','comment','tag','mention','reply') NOT NULL,
  `from_user_id` int(10) UNSIGNED NOT NULL,
  `message` text DEFAULT NULL,
  `is_read` tinyint(1) DEFAULT 0,
  `created_at` timestamp NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_uca1400_ai_ci;

--
-- RELATIONSHIPS FOR TABLE `engagement_notifications`:
--   `user_id`
--       `users` -> `id`
--   `photo_id`
--       `images` -> `id`
--   `from_user_id`
--       `users` -> `id`
--
