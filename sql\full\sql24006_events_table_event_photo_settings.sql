
-- --------------------------------------------------------

--
-- Table structure for table `event_photo_settings`
--
-- Creation: Jul 29, 2025 at 09:31 PM
--

CREATE TABLE `event_photo_settings` (
  `id` int(10) UNSIGNED NOT NULL,
  `setting_name` varchar(100) NOT NULL,
  `setting_value` text NOT NULL,
  `description` text DEFAULT NULL,
  `setting_type` enum('boolean','integer','decimal','string','text') DEFAULT 'string',
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- RELATIONSHIPS FOR TABLE `event_photo_settings`:
--

--
-- Dumping data for table `event_photo_settings`
--

INSERT INTO `event_photo_settings` (`id`, `setting_name`, `setting_value`, `description`, `setting_type`, `created_at`, `updated_at`) VALUES
(1, 'category_label_vehicle', '🚗 Vehicle', NULL, 'string', '2025-07-30 15:39:29', '2025-07-30 15:43:58'),
(2, 'category_label_atmosphere', '⭐ Atmosphere', NULL, 'string', '2025-07-30 15:39:29', '2025-07-30 15:45:42'),
(3, 'category_label_awards', '🏆 Awards', NULL, 'string', '2025-07-30 15:39:29', '2025-07-30 15:43:58'),
(4, 'category_label_vendors', '🍔 Vendors', NULL, 'string', '2025-07-30 15:39:29', '2025-07-30 15:43:58'),
(5, 'category_label_people', '👥 People', NULL, 'string', '2025-07-30 15:39:29', '2025-07-30 15:43:58');
