-- Engagement System Database Schema
-- Photo Likes System
CREATE TABLE IF NOT EXISTS photo_likes (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    photo_id INT UNSIGNED NOT NULL,
    user_id INT UNSIGNED NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (photo_id) REFERENCES images(id) ON DELETE CASCADE,
    <PERSON>OR<PERSON><PERSON><PERSON> KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_like (photo_id, user_id)
);

-- Photo Comments System
CREATE TABLE IF NOT EXISTS photo_comments (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    photo_id INT UNSIGNED NOT NULL,
    user_id INT UNSIGNED NOT NULL,
    comment TEXT NOT NULL,
    parent_comment_id INT UNSIGNED NULL, -- For reply threads
    mentions JSON NULL, -- Store mentioned user IDs as <PERSON>SON array
    is_approved TINYINT(1) DEFAULT 1, -- For moderation
    is_edited TINYINT(1) DEFAULT 0, -- Track if comment was edited
    edited_at TIMESTAMP NULL, -- When comment was last edited
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (photo_id) REFERENCES images(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_comment_id) REFERENCES photo_comments(id) ON DELETE CASCADE,
    INDEX idx_photo_comments (photo_id, created_at),
    INDEX idx_user_comments (user_id, created_at)
);

-- Photo User Tags System
CREATE TABLE IF NOT EXISTS photo_user_tags (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    photo_id INT UNSIGNED NOT NULL,
    tagged_user_id INT UNSIGNED NOT NULL,
    tagged_by_user_id INT UNSIGNED NOT NULL,
    x_position DECIMAL(5,2) NULL, -- X coordinate percentage (0-100)
    y_position DECIMAL(5,2) NULL, -- Y coordinate percentage (0-100)
    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    approved_at TIMESTAMP NULL,
    FOREIGN KEY (photo_id) REFERENCES images(id) ON DELETE CASCADE,
    FOREIGN KEY (tagged_user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (tagged_by_user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_tag (photo_id, tagged_user_id),
    INDEX idx_user_tags (tagged_user_id, status)
);

-- Photo Favorites System
CREATE TABLE IF NOT EXISTS photo_favorites (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    photo_id INT UNSIGNED NOT NULL,
    user_id INT UNSIGNED NOT NULL,
    collection_name VARCHAR(100) DEFAULT 'My Favorites',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (photo_id) REFERENCES images(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_favorite (photo_id, user_id),
    INDEX idx_user_favorites (user_id, collection_name, created_at)
);

-- Photo Shares Tracking (for analytics)
CREATE TABLE IF NOT EXISTS photo_shares (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    photo_id INT UNSIGNED NOT NULL,
    user_id INT UNSIGNED NULL, -- NULL for anonymous shares
    platform ENUM('facebook', 'twitter', 'instagram', 'whatsapp', 'email', 'copy_link') NOT NULL,
    shared_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (photo_id) REFERENCES images(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_photo_shares (photo_id, platform),
    INDEX idx_user_shares (user_id, shared_at)
);

-- Photo Engagement Stats (for performance)
CREATE TABLE IF NOT EXISTS photo_engagement_stats (
    photo_id INT UNSIGNED PRIMARY KEY,
    likes_count INT UNSIGNED DEFAULT 0,
    comments_count INT UNSIGNED DEFAULT 0,
    shares_count INT UNSIGNED DEFAULT 0,
    favorites_count INT UNSIGNED DEFAULT 0,
    tags_count INT UNSIGNED DEFAULT 0,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (photo_id) REFERENCES images(id) ON DELETE CASCADE
);

-- Notification System for Engagement
CREATE TABLE IF NOT EXISTS engagement_notifications (
    id INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id INT UNSIGNED NOT NULL, -- User receiving the notification
    photo_id INT UNSIGNED NOT NULL,
    type ENUM('like', 'comment', 'tag', 'mention', 'reply') NOT NULL,
    from_user_id INT UNSIGNED NOT NULL, -- User who performed the action
    message TEXT NULL,
    is_read TINYINT(1) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (photo_id) REFERENCES images(id) ON DELETE CASCADE,
    FOREIGN KEY (from_user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_notifications (user_id, is_read, created_at),
    INDEX idx_photo_notifications (photo_id, type)
);

-- Add new columns to existing photo_comments table if they don't exist
-- Check and add is_edited column
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_schema = DATABASE()
     AND table_name = 'photo_comments'
     AND column_name = 'is_edited') > 0,
    'SELECT "Column is_edited already exists in photo_comments table"',
    'ALTER TABLE photo_comments ADD COLUMN is_edited TINYINT(1) DEFAULT 0 AFTER is_approved'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Check and add edited_at column
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_schema = DATABASE()
     AND table_name = 'photo_comments'
     AND column_name = 'edited_at') > 0,
    'SELECT "Column edited_at already exists in photo_comments table"',
    'ALTER TABLE photo_comments ADD COLUMN edited_at TIMESTAMP NULL AFTER is_edited'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Initialize engagement stats for existing photos
INSERT IGNORE INTO photo_engagement_stats (photo_id)
SELECT id FROM images WHERE entity_type = 'event_photo';

-- Note: Indexes may already exist, so we skip adding them to avoid errors
-- If needed, you can manually add these indexes:
-- ALTER TABLE images ADD INDEX idx_entity_engagement (entity_type, created_at);
-- ALTER TABLE event_photo_metadata ADD INDEX idx_event_photos (event_type, event_id);
