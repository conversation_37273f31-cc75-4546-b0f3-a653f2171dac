
-- --------------------------------------------------------

--
-- Table structure for table `pre_approved_cords`
--
-- Creation: Jun 23, 2025 at 08:30 PM
--

CREATE TABLE `pre_approved_cords` (
  `id` int(10) UNSIGNED NOT NULL,
  `coordinator_id` int(10) UNSIGNED NOT NULL,
  `begin_date` date NOT NULL,
  `end_date` date NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- RELATIONSHIPS FOR TABLE `pre_approved_cords`:
--   `coordinator_id`
--       `users` -> `id`
--

--
-- Dumping data for table `pre_approved_cords`
--

INSERT INTO `pre_approved_cords` (`id`, `coordinator_id`, `begin_date`, `end_date`, `created_at`, `updated_at`) VALUES
(1, 5, '2025-06-12', '2025-07-12', '2025-06-12 22:12:44', '2025-06-12 22:12:44');
