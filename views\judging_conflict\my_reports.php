<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container">
    <div class="row mb-4">
        <div class="col-md-8">
            <h1><i class="fas fa-flag me-2"></i>My Conflict Reports</h1>
            <p class="text-muted">Track the status of your judging conflict reports</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="<?php echo BASE_URL; ?>/judging_conflict/report" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>Report New Conflict
            </a>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Filter Reports</h5>
        </div>
        <div class="card-body">
            <form method="GET" action="<?php echo BASE_URL; ?>/judging_conflict/my_reports">
                <div class="row">
                    <div class="col-md-3">
                        <label for="status" class="form-label">Status</label>
                        <select name="status" id="status" class="form-select">
                            <option value="">All Statuses</option>
                            <option value="open" <?php echo $filters['status'] === 'open' ? 'selected' : ''; ?>>Open</option>
                            <option value="under_review" <?php echo $filters['status'] === 'under_review' ? 'selected' : ''; ?>>Under Review</option>
                            <option value="resolved" <?php echo $filters['status'] === 'resolved' ? 'selected' : ''; ?>>Resolved</option>
                            <option value="dismissed" <?php echo $filters['status'] === 'dismissed' ? 'selected' : ''; ?>>Dismissed</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label for="search" class="form-label">Search</label>
                        <input type="text" name="search" id="search" class="form-control" 
                               placeholder="Search your reports..." 
                               value="<?php echo htmlspecialchars($filters['search']); ?>">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>Filter
                            </button>
                            <a href="<?php echo BASE_URL; ?>/judging_conflict/my_reports" class="btn btn-secondary">
                                <i class="fas fa-times me-2"></i>Clear
                            </a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Reports List -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0"><i class="fas fa-list me-2"></i>Your Reports</h5>
        </div>
        <div class="card-body">
            <?php if (empty($conflicts)): ?>
                <div class="text-center py-5">
                    <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                    <h5>No conflict reports found</h5>
                    <p class="text-muted">You haven't reported any judging conflicts yet.</p>
                    <a href="<?php echo BASE_URL; ?>/judging_conflict/report" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Report Your First Conflict
                    </a>
                </div>
            <?php else: ?>
                <div class="row">
                    <?php foreach ($conflicts as $conflict): ?>
                        <div class="col-md-6 mb-4">
                            <div class="card h-100">
                                <div class="card-header d-flex justify-content-between align-items-center">
                                    <span class="badge bg-secondary">#<?php echo $conflict->id; ?></span>
                                    <div>
                                        <?php
                                        $priorityClass = [
                                            'urgent' => 'bg-danger',
                                            'high' => 'bg-warning',
                                            'normal' => 'bg-primary',
                                            'low' => 'bg-secondary'
                                        ][$conflict->priority] ?? 'bg-secondary';
                                        
                                        $statusClass = [
                                            'open' => 'bg-warning',
                                            'under_review' => 'bg-info',
                                            'resolved' => 'bg-success',
                                            'dismissed' => 'bg-secondary',
                                            'escalated' => 'bg-danger'
                                        ][$conflict->status] ?? 'bg-secondary';
                                        ?>
                                        <span class="badge <?php echo $statusClass; ?> me-1">
                                            <?php echo ucwords(str_replace('_', ' ', $conflict->status)); ?>
                                        </span>
                                        <span class="badge <?php echo $priorityClass; ?>">
                                            <?php echo ucfirst($conflict->priority); ?>
                                        </span>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <h6 class="card-title"><?php echo htmlspecialchars($conflict->title); ?></h6>
                                    
                                    <div class="mb-2">
                                        <small class="text-muted">
                                            <i class="fas fa-calendar me-1"></i>
                                            <strong>Show:</strong> <?php echo htmlspecialchars($conflict->show_name); ?>
                                        </small>
                                    </div>
                                    
                                    <?php if ($conflict->registration_number): ?>
                                        <div class="mb-2">
                                            <small class="text-muted">
                                                <i class="fas fa-car me-1"></i>
                                                <strong>Vehicle:</strong> #<?php echo htmlspecialchars($conflict->registration_number); ?>
                                                <?php if ($conflict->year && $conflict->make && $conflict->model): ?>
                                                    - <?php echo htmlspecialchars($conflict->year . ' ' . $conflict->make . ' ' . $conflict->model); ?>
                                                <?php endif; ?>
                                            </small>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="mb-2">
                                        <small class="text-muted">
                                            <i class="fas fa-tag me-1"></i>
                                            <strong>Type:</strong> <?php echo ucwords(str_replace('_', ' ', $conflict->conflict_type)); ?>
                                        </small>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <small class="text-muted">
                                            <i class="fas fa-clock me-1"></i>
                                            <strong>Reported:</strong> <?php echo formatDateTimeForUser($conflict->created_at, $_SESSION['user_id'] ?? null, 'M j, Y g:i A'); ?>
                                        </small>
                                    </div>
                                    
                                    <p class="card-text">
                                        <?php 
                                        $description = htmlspecialchars($conflict->description);
                                        echo strlen($description) > 150 ? substr($description, 0, 150) . '...' : $description;
                                        ?>
                                    </p>
                                    
                                    <?php if ($conflict->assigned_to_name): ?>
                                        <div class="mb-2">
                                            <small class="text-info">
                                                <i class="fas fa-user-tie me-1"></i>
                                                Assigned to: <?php echo htmlspecialchars($conflict->assigned_to_name); ?>
                                            </small>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if ($conflict->resolved_at): ?>
                                        <div class="mb-2">
                                            <small class="text-success">
                                                <i class="fas fa-check-circle me-1"></i>
                                                Resolved: <?php echo formatDateTimeForUser($conflict->resolved_at, $_SESSION['user_id'] ?? null, 'M j, Y g:i A'); ?>
                                            </small>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <div class="card-footer">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted">
                                            <?php
                                            $hoursOpen = $conflict->resolved_at ? 
                                                round((strtotime($conflict->resolved_at) - strtotime($conflict->created_at)) / 3600, 1) :
                                                round((time() - strtotime($conflict->created_at)) / 3600, 1);
                                            ?>
                                            <?php echo $conflict->status === 'resolved' ? 'Resolved in' : 'Open for'; ?> <?php echo $hoursOpen; ?> hours
                                        </small>
                                        <a href="<?php echo BASE_URL; ?>/judging_conflict/viewConflict/<?php echo $conflict->id; ?>" 
                                           class="btn btn-outline-primary btn-sm">
                                            <i class="fas fa-eye me-1"></i>View Details
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- Pagination -->
                <?php if ($pagination['total_pages'] > 1): ?>
                    <nav aria-label="Reports pagination" class="mt-4">
                        <ul class="pagination justify-content-center">
                            <?php if ($pagination['has_prev']): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo $pagination['current_page'] - 1; ?>&<?php echo http_build_query(array_filter($filters)); ?>">
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                </li>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $pagination['current_page'] - 2); $i <= min($pagination['total_pages'], $pagination['current_page'] + 2); $i++): ?>
                                <li class="page-item <?php echo $i === $pagination['current_page'] ? 'active' : ''; ?>">
                                    <a class="page-link" href="?page=<?php echo $i; ?>&<?php echo http_build_query(array_filter($filters)); ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                            <?php endfor; ?>
                            
                            <?php if ($pagination['has_next']): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo $pagination['current_page'] + 1; ?>&<?php echo http_build_query(array_filter($filters)); ?>">
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                    
                    <div class="text-center text-muted">
                        Showing <?php echo (($pagination['current_page'] - 1) * $pagination['per_page']) + 1; ?> to 
                        <?php echo min($pagination['current_page'] * $pagination['per_page'], $pagination['total']); ?> of 
                        <?php echo $pagination['total']; ?> reports
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>

    <!-- Help Section -->
    <div class="card mt-4">
        <div class="card-header">
            <h5 class="mb-0"><i class="fas fa-question-circle me-2"></i>Understanding Report Status</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <div class="d-flex align-items-center mb-2">
                        <span class="badge bg-warning me-2">Open</span>
                        <small>Report submitted, awaiting review</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="d-flex align-items-center mb-2">
                        <span class="badge bg-info me-2">Under Review</span>
                        <small>Being investigated by staff</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="d-flex align-items-center mb-2">
                        <span class="badge bg-success me-2">Resolved</span>
                        <small>Issue has been addressed</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="d-flex align-items-center mb-2">
                        <span class="badge bg-secondary me-2">Dismissed</span>
                        <small>No action required</small>
                    </div>
                </div>
            </div>
            
            <hr>
            
            <div class="row">
                <div class="col-md-6">
                    <h6>What to expect:</h6>
                    <ul class="mb-0">
                        <li>Initial review within 24 hours</li>
                        <li>Updates via notifications and email</li>
                        <li>Resolution within 48-72 hours</li>
                        <li>Detailed explanation of any decisions</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>Need help?</h6>
                    <p class="mb-2">If you have questions about a report or need to provide additional information:</p>
                    <a href="<?php echo BASE_URL; ?>/contact" class="btn btn-outline-primary btn-sm">
                        <i class="fas fa-envelope me-2"></i>Contact Support
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Auto-submit form on filter change
document.addEventListener('DOMContentLoaded', function() {
    const statusSelect = document.getElementById('status');
    
    statusSelect.addEventListener('change', function() {
        this.form.submit();
    });
});
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>