
-- --------------------------------------------------------

--
-- Table structure for table `club_ownership_verifications`
--
-- Creation: Jul 09, 2025 at 11:09 AM
--

CREATE TABLE `club_ownership_verifications` (
  `id` int(10) UNSIGNED NOT NULL,
  `club_id` int(10) UNSIGNED NOT NULL,
  `user_id` int(10) UNSIGNED NOT NULL,
  `request_type` enum('new_ownership','transfer_ownership') NOT NULL DEFAULT 'new_ownership',
  `status` enum('pending','approved','denied') NOT NULL DEFAULT 'pending',
  `user_name` varchar(255) NOT NULL,
  `user_email` varchar(255) NOT NULL,
  `user_phone` varchar(50) DEFAULT NULL,
  `club_information` text NOT NULL,
  `verification_documents` text DEFAULT NULL,
  `additional_info` text DEFAULT NULL,
  `admin_notes` text DEFAULT NULL,
  `requested_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `reviewed_at` timestamp NULL DEFAULT NULL,
  `reviewed_by` int(10) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- RELATIONSHIPS FOR TABLE `club_ownership_verifications`:
--   `club_id`
--       `calendar_clubs` -> `id`
--   `user_id`
--       `users` -> `id`
--   `reviewed_by`
--       `users` -> `id`
--
