<?php require APPROOT . '/views/includes/header.php'; ?>

<style>
    /* Chalkboard Formula Visualization Styles */
    .chalkboard {
        background-color: #2a3b35;
        border: 10px solid #5d4037;
        box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        position: relative;
        background-image: url('data:image/png;base64,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');
        background-blend-mode: overlay;
        opacity: 1;
    }
    
    /* Add chalk dust at the bottom of the chalkboard */
    .chalkboard::after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 10px;
        background: rgba(255, 255, 255, 0.2);
        border-top: 1px solid rgba(255, 255, 255, 0.3);
    }
    
    .chalk-text {
        color: rgba(255, 255, 255, 0.9);
        font-family: 'Comic Sans MS', 'Chalkduster', cursive, sans-serif;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
        letter-spacing: 0.5px;
        line-height: 1.5;
        position: relative;
    }
    
    /* Create a chalk-like effect with slightly uneven text */
    .chalk-text::before {
        content: attr(data-text);
        position: absolute;
        left: 0.5px;
        top: 0.5px;
        color: rgba(255, 255, 255, 0.3);
        z-index: -1;
    }
    
    .formula-display {
        background-color: rgba(0, 0, 0, 0.2);
        border: 2px dashed rgba(255, 255, 255, 0.3);
    }
    
    .step-number {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background-color: rgba(255, 255, 255, 0.2);
        color: white;
        font-weight: bold;
    }
    
    .chalk-highlight-red {
        color: #ff9e9e;
        font-weight: bold;
    }
    
    .chalk-highlight-blue {
        color: #9eceff;
        font-weight: bold;
    }
    
    .chalk-highlight-green {
        color: #9effb1;
        font-weight: bold;
    }
    
    .chalk-highlight-yellow {
        color: #fff59e;
        font-weight: bold;
    }
    
    .chalk-result {
        color: #ffcc00;
        font-weight: bold;
        font-size: 1.2em;
    }
    
    .final-result {
        background-color: rgba(0, 0, 0, 0.3);
        border-top: 2px dashed rgba(255, 255, 255, 0.3);
    }
    
    /* Final Score Badge Styles */
    .final-score-badge {
        transition: transform 0.3s ease;
    }
    .final-score-badge:hover {
        transform: scale(1.05);
    }
    
    /* Score Calculation Table Styles */
    .table-calculation th, .table-calculation td {
        vertical-align: middle;
    }
    
    /* Judge tab styles */
    .nav-tabs .nav-link {
        border: 1px solid #dee2e6;
        border-top-left-radius: 0.25rem;
        border-top-right-radius: 0.25rem;
        padding: 0.5rem 1rem;
        margin-right: 0.25rem;
        background-color: #f8f9fa;
    }
    
    .nav-tabs .nav-link.active {
        background-color: #fff;
        border-bottom-color: transparent;
        font-weight: bold;
    }
    
    .tab-content {
        border: 1px solid #dee2e6;
        border-top: none;
        padding: 1rem;
        background-color: #fff;
    }
</style>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="<?php echo URLROOT; ?>/user/dashboard">Dashboard</a></li>
                    <li class="breadcrumb-item"><a href="<?php echo URLROOT; ?>/user/registrations">My Registrations</a></li>
                    <li class="breadcrumb-item active" aria-current="page">View Scores</li>
                </ol>
            </nav>
            
            <h1><?php echo $data['title']; ?></h1>
            
            <?php flash('user_message'); ?>
            
            <div class="row mb-4">
                <div class="col-md-12">
                    <div class="card shadow-sm">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0">Vehicle Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Registration #:</strong> <?php echo $data['registration']->registration_number; ?></p>
                                    <?php if (isset($data['registration']->display_number) && !empty($data['registration']->display_number)) : ?>
                                        <p><strong>Display Number:</strong> <span class="badge bg-primary px-3 py-2" style="font-size: 1.1em;"><?php echo $data['registration']->display_number; ?></span></p>
                                    <?php endif; ?>
                                    <p><strong>Year:</strong> <?php echo $data['registration']->year; ?></p>
                                    <p><strong>Make:</strong> <?php echo $data['registration']->make; ?></p>
                                    <p><strong>Model:</strong> <?php echo $data['registration']->model; ?></p>
                                    <p><strong>Category:</strong> <?php echo $data['registration']->category_name; ?></p>
                                </div>
                                <div class="col-md-6">
                                    <?php if (!empty($data['registration']->description)) : ?>
                                        <p><strong>Description:</strong> <?php echo $data['registration']->description; ?></p>
                                    <?php endif; ?>
                                    <?php if (!empty($data['registration']->modifications)) : ?>
                                        <p><strong>Modifications:</strong> <?php echo $data['registration']->modifications; ?></p>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <?php if (!empty($data['images'])) : ?>
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card shadow-sm">
                            <div class="card-header bg-primary text-white">
                                <h5 class="mb-0">Vehicle Images</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <?php foreach ($data['images'] as $image) : ?>
                                        <?php 
                                        $imageFilename = '';
                                        if (isset($image->filename) && !empty($image->filename)) {
                                            $imageFilename = $image->filename;
                                        } elseif (isset($image->file_name) && !empty($image->file_name)) {
                                            $imageFilename = $image->file_name;
                                        } elseif (isset($image->image_path) && !empty($image->image_path)) {
                                            $imageFilename = basename($image->image_path);
                                        }
                                        
                                        if (!empty($imageFilename)) :
                                        ?>
                                        <div class="col-md-3 mb-3">
                                            <a href="<?php echo BASE_URL; ?>/uploads/vehicles/<?php echo $imageFilename; ?>" data-lightbox="vehicle-gallery">
                                                <img src="<?php echo BASE_URL; ?>/uploads/vehicles/thumbnails/<?php echo $imageFilename; ?>" 
                                                     class="img-fluid img-thumbnail" alt="Vehicle Image">
                                            </a>
                                        </div>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
            
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Judging Scores</h5>
                </div>
                <div class="card-body">
                    <?php if (isset($data['weighted_total_score'])) : ?>
                    <!-- Score Badge and Formula Visualization Side by Side -->
                    <div class="row mb-4 align-items-stretch">
                        <!-- Final Score Badge - Left Side -->
                        <div class="col-md-4 d-flex align-items-center justify-content-center mb-3 mb-md-0">
                            <div class="p-4 bg-success text-white rounded shadow final-score-badge w-100 text-center">
                                <h2 class="mb-0">Final Score</h2>
                                <div class="display-1 fw-bold"><?php echo number_format($data['weighted_total_score'] ?? 0, 1); ?></div>
                                <?php if (isset($data['scoring_settings']->normalize_scores) && $data['scoring_settings']->normalize_scores): ?>
                                <div class="small">(Normalized to 100-point scale)</div>
                                <?php endif; ?>
                                
                                <div class="mt-3 small">
                                    <?php if (isset($data['age_weight']) && $data['age_weight'] != 1.0) : ?>
                                    <span class="badge bg-info">Age Weight: <?php echo number_format($data['age_weight'] ?? 1.0, 2); ?>x</span>
                                    <?php endif; ?>
                                    <span class="badge bg-secondary">Vehicle Year: <?php echo $data['registration']->year; ?></span>
                                </div>
                                
                                <div class="mt-2">
                                    <span class="badge bg-primary fs-6">Formula: <?php echo htmlspecialchars($data['formula_name']); ?></span>
                                </div>
                                
                                <!-- Report Issue Button -->
                                <div class="mt-3">
                                    <a href="<?php echo BASE_URL; ?>/judging_conflict/report?show_id=<?php echo $data['registration']->show_id; ?>&registration_id=<?php echo $data['registration']->id; ?>" 
                                       class="btn btn-outline-warning btn-sm">
                                        <i class="fas fa-flag me-1"></i>Report Issue
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Formula Mini-Visualization - Right Side -->
                        <div class="col-md-8">
                            <?php
                            // Get a sample score to use for visualization
                            if (!empty($data['scores'])) {
                                $sampleScore = $data['scores'][0];
                                $sampleRawScore = (float)$sampleScore->score;
                                
                                // Find the metric weight
                                $sampleMetricWeight = 1.0;
                                foreach ($data['metrics'] as $metric) {
                                    if ($metric->id == $sampleScore->metric_id) {
                                        $sampleMetricWeight = (float)$metric->weight;
                                        break;
                                    }
                                }
                                
                                $sampleWeightedScore = $sampleRawScore * $sampleMetricWeight;
                                $ageWeight = isset($data['age_weight']) ? (float)$data['age_weight'] : 1.0;
                                $sampleAgeWeightedScore = $sampleWeightedScore * $ageWeight;
                            ?>
                            <div class="chalkboard p-3 h-100">
                                <h5 class="chalk-text mb-3" data-text="How Your Score Was Calculated">How Your Score Was Calculated</h5>
                                
                                <div class="formula-display mb-3 p-2 rounded">
                                    <?php 
                                    // Format the formula for display
                                    $displayFormula = htmlspecialchars($data['active_formula']);
                                    $plainFormula = $displayFormula; // Store a plain version for data-text attribute
                                    
                                    $displayFormula = str_replace('rawScore', '<span class="chalk-highlight-red">rawScore</span>', $displayFormula);
                                    $displayFormula = str_replace('metricWeight', '<span class="chalk-highlight-blue">metricWeight</span>', $displayFormula);
                                    $displayFormula = str_replace('ageWeight', '<span class="chalk-highlight-green">ageWeight</span>', $displayFormula);
                                    $displayFormula = str_replace('maxScore', '<span class="chalk-highlight-yellow">maxScore</span>', $displayFormula);
                                    ?>
                                    <div class="chalk-text" data-text="<?php echo $plainFormula; ?>"><?php echo $displayFormula; ?></div>
                                </div>
                                
                                <div class="d-flex align-items-center mb-2">
                                    <div class="step-number me-2">1</div>
                                    <div class="chalk-text" data-text="Raw Score from Judge">Raw Score from Judge: <span class="chalk-highlight-red"><?php echo $sampleRawScore; ?></span></div>
                                </div>
                                
                                <div class="d-flex align-items-center mb-2">
                                    <div class="step-number me-2">2</div>
                                    <div class="chalk-text" data-text="Metric Weight">Metric Weight: <span class="chalk-highlight-blue"><?php echo $sampleMetricWeight; ?></span></div>
                                </div>
                                
                                <?php if ($ageWeight != 1.0): ?>
                                <div class="d-flex align-items-center mb-2">
                                    <div class="step-number me-2">3</div>
                                    <div class="chalk-text" data-text="Age Weight">Age Weight: <span class="chalk-highlight-green"><?php echo $ageWeight; ?></span></div>
                                </div>
                                <?php endif; ?>
                                
                                <div class="final-result mt-3 p-2">
                                    <div class="chalk-text" data-text="Final Score">Final Score: <span class="chalk-result"><?php echo number_format($data['weighted_total_score'] ?? 0, 1); ?></span></div>
                                </div>
                            </div>
                            <?php } ?>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <!-- Detailed Scores by Judge with Tabs -->
                    <?php
                    // Group scores by judge
                    $scoresByJudge = [];
                    foreach ($data['scores'] as $score) {
                        if (!isset($scoresByJudge[$score->judge_id])) {
                            $scoresByJudge[$score->judge_id] = [
                                'judge_name' => $score->judge_name,
                                'scores' => []
                            ];
                        }
                        $scoresByJudge[$score->judge_id]['scores'][] = $score;
                    }
                    
                    if (count($scoresByJudge) > 0) :
                    ?>
                    <!-- Nav tabs for judges -->
                    <ul class="nav nav-tabs" id="judgeScoresTabs" role="tablist">
                        <?php 
                        $isFirst = true;
                        foreach ($scoresByJudge as $judgeId => $judgeData) : 
                        ?>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link <?php echo $isFirst ? 'active' : ''; ?>" 
                                        id="judge-<?php echo $judgeId; ?>-tab" 
                                        data-bs-toggle="tab" 
                                        data-bs-target="#judge-<?php echo $judgeId; ?>" 
                                        type="button" 
                                        role="tab" 
                                        aria-controls="judge-<?php echo $judgeId; ?>" 
                                        aria-selected="<?php echo $isFirst ? 'true' : 'false'; ?>">
                                    <?php echo $judgeData['judge_name']; ?>
                                </button>
                            </li>
                        <?php 
                            $isFirst = false;
                        endforeach; 
                        ?>
                    </ul>
                    
                    <!-- Tab content for judges -->
                    <div class="tab-content" id="judgeScoresTabContent">
                        <?php 
                        $isFirst = true;
                        foreach ($scoresByJudge as $judgeId => $judgeData) : 
                        ?>
                            <div class="tab-pane fade <?php echo $isFirst ? 'show active' : ''; ?>" 
                                 id="judge-<?php echo $judgeId; ?>" 
                                 role="tabpanel" 
                                 aria-labelledby="judge-<?php echo $judgeId; ?>-tab">
                                
                                <div class="table-responsive mt-3">
                                    <table class="table table-bordered">
                                        <thead class="table-light">
                                            <tr>
                                                <th style="width: 25%">Metric</th>
                                                <th style="width: 10%" class="text-center">Score</th>
                                                <th style="width: 10%" class="text-center">Max</th>
                                                <th style="width: 10%" class="text-center">Weight</th>
                                                <th>Comments</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($judgeData['scores'] as $score) : ?>
                                                <tr>
                                                    <td>
                                                        <strong><?php echo $score->metric_name; ?></strong>
                                                        <?php if (isset($score->metric_description) && !empty($score->metric_description)) : ?>
                                                            <p class="small text-muted mb-0"><?php echo $score->metric_description; ?></p>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td class="text-center">
                                                        <span class="badge bg-primary fs-6"><?php echo number_format($score->score, 1); ?></span>
                                                    </td>
                                                    <td class="text-center">
                                                        <?php echo number_format($score->max_score, 1); ?>
                                                    </td>
                                                    <td class="text-center">
                                                        <?php echo number_format($score->weight, 2); ?>
                                                    </td>
                                                    <td>
                                                        <?php echo $score->comments; ?>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        <?php 
                            $isFirst = false;
                        endforeach; 
                        ?>
                    </div>
                    <?php else : ?>
                        <div class="alert alert-warning">
                            No detailed judge scores are available.
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require APPROOT . '/views/includes/footer.php'; ?>