<?php
/**
 * Fix Judging Conflicts Installation Issues
 * 
 * This script fixes the foreign key constraints and adds the missing settings.
 */

require_once 'config/config.php';
require_once 'core/Database.php';

echo "<h1>Fixing Judging Conflicts Installation</h1>\n";

try {
    $db = new Database();
    
    $successCount = 0;
    $errorCount = 0;
    
    // 1. Add missing settings (without description column)
    echo "<h2>1. Adding Configuration Settings</h2>\n";
    $settings = [
        ['conflict_auto_detection_enabled', '1'],
        ['conflict_score_discrepancy_threshold', '15'],
        ['conflict_notification_enabled', '1'],
        ['conflict_time_limit_hours', '72'],
        ['conflict_escalation_threshold_hours', '48']
    ];
    
    foreach ($settings as $setting) {
        try {
            $db->query("INSERT IGNORE INTO `settings` (`name`, `value`) VALUES (?, ?)");
            $db->bind(1, $setting[0]);
            $db->bind(2, $setting[1]);
            $db->execute();
            echo "<p style='color: green;'>✓ Added setting: {$setting[0]} = {$setting[1]}</p>\n";
            $successCount++;
        } catch (Exception $e) {
            echo "<p style='color: red;'>✗ Error adding setting {$setting[0]}: " . htmlspecialchars($e->getMessage()) . "</p>\n";
            $errorCount++;
        }
    }
    
    // 2. Fix foreign key constraints by modifying table columns to match referenced tables
    echo "<h2>2. Fixing Column Types for Foreign Key Constraints</h2>\n";
    
    $columnFixes = [
        "ALTER TABLE `judging_conflicts` MODIFY `show_id` int(10) unsigned NOT NULL",
        "ALTER TABLE `judging_conflicts` MODIFY `registration_id` int(10) unsigned DEFAULT NULL",
        "ALTER TABLE `judging_conflicts` MODIFY `reported_by_user_id` int(10) unsigned NOT NULL",
        "ALTER TABLE `judging_conflicts` MODIFY `assigned_to_admin_id` int(10) unsigned DEFAULT NULL",
        "ALTER TABLE `judging_conflicts` MODIFY `resolved_by_admin_id` int(10) unsigned DEFAULT NULL",
        "ALTER TABLE `judging_conflict_comments` MODIFY `user_id` int(10) unsigned NOT NULL",
        "ALTER TABLE `judging_conflict_related_scores` MODIFY `score_id` int(10) unsigned NOT NULL",
        "ALTER TABLE `judging_conflict_related_judges` MODIFY `judge_id` int(10) unsigned NOT NULL"
    ];
    
    foreach ($columnFixes as $fix) {
        try {
            $db->query($fix);
            $db->execute();
            echo "<p style='color: green;'>✓ Fixed column type</p>\n";
            $successCount++;
        } catch (Exception $e) {
            echo "<p style='color: red;'>✗ Error fixing column: " . htmlspecialchars($e->getMessage()) . "</p>\n";
            $errorCount++;
        }
    }
    
    // 3. Add foreign key constraints with correct column types
    echo "<h2>3. Adding Foreign Key Constraints</h2>\n";
    
    $constraints = [
        ["judging_conflicts", "fk_conflicts_show", "show_id", "shows", "id"],
        ["judging_conflicts", "fk_conflicts_registration", "registration_id", "registrations", "id"],
        ["judging_conflicts", "fk_conflicts_reported_by", "reported_by_user_id", "users", "id"],
        ["judging_conflicts", "fk_conflicts_assigned_to", "assigned_to_admin_id", "users", "id"],
        ["judging_conflicts", "fk_conflicts_resolved_by", "resolved_by_admin_id", "users", "id"],
        ["judging_conflict_comments", "fk_comments_conflict", "conflict_id", "judging_conflicts", "id"],
        ["judging_conflict_comments", "fk_comments_user", "user_id", "users", "id"],
        ["judging_conflict_related_scores", "fk_related_scores_conflict", "conflict_id", "judging_conflicts", "id"],
        ["judging_conflict_related_scores", "fk_related_scores_score", "score_id", "scores", "id"],
        ["judging_conflict_related_judges", "fk_related_judges_conflict", "conflict_id", "judging_conflicts", "id"],
        ["judging_conflict_related_judges", "fk_related_judges_judge", "judge_id", "users", "id"]
    ];
    
    foreach ($constraints as $constraint) {
        list($table, $constraintName, $column, $refTable, $refColumn) = $constraint;
        
        try {
            // First, try to drop the constraint if it exists
            $db->query("ALTER TABLE `$table` DROP FOREIGN KEY `$constraintName`");
            $db->execute();
        } catch (Exception $e) {
            // Constraint doesn't exist, which is fine
        }
        
        try {
            $onDelete = ($column === 'registration_id' || $column === 'assigned_to_admin_id' || $column === 'resolved_by_admin_id') ? 'SET NULL' : 'CASCADE';
            $sql = "ALTER TABLE `$table` ADD CONSTRAINT `$constraintName` FOREIGN KEY (`$column`) REFERENCES `$refTable` (`$refColumn`) ON DELETE $onDelete";
            
            $db->query($sql);
            $db->execute();
            echo "<p style='color: green;'>✓ Added foreign key: $table.$column -> $refTable.$refColumn</p>\n";
            $successCount++;
        } catch (Exception $e) {
            echo "<p style='color: orange;'>⚠ Foreign key constraint skipped ($table.$column): " . htmlspecialchars($e->getMessage()) . "</p>\n";
            // Don't count as error since foreign keys are optional for functionality
        }
    }
    
    // 4. Test the system
    echo "<h2>4. System Test</h2>\n";
    
    try {
        $db->query("SELECT COUNT(*) as count FROM judging_conflicts");
        $result = $db->single();
        echo "<p style='color: green;'>✓ judging_conflicts table accessible (count: " . $result->count . ")</p>\n";
        
        $db->query("SELECT COUNT(*) as count FROM settings WHERE name LIKE 'conflict_%'");
        $result = $db->single();
        echo "<p style='color: green;'>✓ Configuration settings installed: " . $result->count . " settings</p>\n";
        
        // Test the model
        require_once APPROOT . '/models/JudgingConflictModel.php';
        $conflictModel = new JudgingConflictModel();
        echo "<p style='color: green;'>✓ JudgingConflictModel can be instantiated</p>\n";
        
        // Test the controller
        require_once APPROOT . '/controllers/JudgingConflictController.php';
        echo "<p style='color: green;'>✓ JudgingConflictController can be loaded</p>\n";
        
        $successCount += 4;
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ System test failed: " . htmlspecialchars($e->getMessage()) . "</p>\n";
        $errorCount++;
    }
    
    echo "<hr>\n";
    echo "<h2>Fix Summary</h2>\n";
    echo "<p><strong>Successful operations:</strong> $successCount</p>\n";
    echo "<p><strong>Failed operations:</strong> $errorCount</p>\n";
    
    if ($errorCount === 0) {
        echo "<p style='color: green; font-weight: bold;'>✓ All issues fixed successfully!</p>\n";
    } else {
        echo "<p style='color: orange; font-weight: bold;'>⚠ Some issues remain, but the system should be functional.</p>\n";
    }
    
    echo "<h3>System is now ready!</h3>\n";
    echo "<ul>\n";
    echo "<li><a href='" . BASE_URL . "/judging_conflict/dashboard'>Judging Conflicts Dashboard</a> (Admin/Coordinator)</li>\n";
    echo "<li><a href='" . BASE_URL . "/judging_conflict/report'>Report a Conflict</a> (All Users)</li>\n";
    echo "<li><a href='" . BASE_URL . "/judging_conflict/my_reports'>My Conflict Reports</a> (All Users)</li>\n";
    echo "</ul>\n";
    
    echo "<h3>Current Settings:</h3>\n";
    try {
        $db->query("SELECT name, value FROM settings WHERE name LIKE 'conflict_%' ORDER BY name");
        $settings = $db->resultSet();
        
        if (!empty($settings)) {
            echo "<ul>\n";
            foreach ($settings as $setting) {
                echo "<li><strong>{$setting->name}:</strong> {$setting->value}</li>\n";
            }
            echo "</ul>\n";
        }
    } catch (Exception $e) {
        echo "<p>Could not retrieve settings.</p>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>Fix failed:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

echo "<hr>\n";
echo "<p><a href='" . BASE_URL . "'>← Return to Home</a></p>\n";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
}

p {
    margin: 5px 0;
}

hr {
    margin: 20px 0;
    border: none;
    border-top: 1px solid #ccc;
}

ul {
    padding-left: 20px;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>