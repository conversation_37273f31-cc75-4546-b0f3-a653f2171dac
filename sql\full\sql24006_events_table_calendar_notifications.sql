
-- --------------------------------------------------------

--
-- Table structure for table `calendar_notifications`
--
-- Creation: Jun 23, 2025 at 08:30 PM
--

CREATE TABLE `calendar_notifications` (
  `id` int(10) UNSIGNED NOT NULL,
  `event_id` int(10) UNSIGNED NOT NULL,
  `user_id` int(10) UNSIGNED NOT NULL,
  `notification_time` datetime NOT NULL,
  `notification_type` enum('email','system') NOT NULL DEFAULT 'email',
  `status` enum('pending','sent','failed') NOT NULL DEFAULT 'pending',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- RELATIONSHIPS FOR TABLE `calendar_notifications`:
--   `event_id`
--       `calendar_events` -> `id`
--   `user_id`
--       `users` -> `id`
--
