
-- --------------------------------------------------------

--
-- Table structure for table `settings`
--
-- Creation: Jun 23, 2025 at 08:30 PM
--

CREATE TABLE `settings` (
  `id` int(10) UNSIGNED NOT NULL,
  `name` varchar(100) NOT NULL,
  `value` text DEFAULT NULL,
  `category` varchar(50) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- RELATIONSHIPS FOR TABLE `settings`:
--

--
-- Dumping data for table `settings`
--

INSERT INTO `settings` (`id`, `name`, `value`, `category`, `created_at`, `updated_at`) VALUES
(1, 'app_version', '3.34.37', NULL, '2025-05-20 12:38:54', '2025-06-12 12:09:14'),
(2, 'default_listing_fee', '25.00', NULL, '2025-06-12 21:16:34', '2025-06-12 21:16:34'),
(3, 'listing_fee_type', 'per_show', NULL, '2025-06-12 21:16:34', '2025-06-12 21:16:34');
