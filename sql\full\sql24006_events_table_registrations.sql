
-- --------------------------------------------------------

--
-- Table structure for table `registrations`
--
-- Creation: Jul 10, 2025 at 01:46 PM
--

CREATE TABLE `registrations` (
  `id` int(10) UNSIGNED NOT NULL,
  `show_id` int(10) UNSIGNED NOT NULL,
  `vehicle_id` int(10) UNSIGNED NOT NULL,
  `registration_number` varchar(20) DEFAULT NULL,
  `owner_id` int(10) UNSIGNED NOT NULL,
  `category_id` int(10) UNSIGNED NOT NULL,
  `status` enum('pending','approved','rejected','cancelled') NOT NULL DEFAULT 'pending',
  `payment_status` varchar(50) DEFAULT NULL,
  `payment_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `payment_method` varchar(50) DEFAULT NULL,
  `payment_reference` varchar(255) DEFAULT NULL,
  `qr_code_path` varchar(255) DEFAULT NULL,
  `qr_code_generated_at` timestamp NULL DEFAULT NULL,
  `payment_date` datetime DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `fee` decimal(10,2) NOT NULL DEFAULT 0.00,
  `payment_method_id` int(10) UNSIGNED DEFAULT NULL,
  `user_id` int(10) UNSIGNED NOT NULL,
  `checked_in` tinyint(1) NOT NULL DEFAULT 0,
  `check_in_time` timestamp NULL DEFAULT NULL,
  `template_id` int(11) DEFAULT NULL,
  `qr_code` varchar(255) DEFAULT NULL,
  `qr_code_url` varchar(255) DEFAULT NULL,
  `display_number` varchar(50) DEFAULT NULL COMMENT 'Display number for the vehicle at the show',
  `notes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- RELATIONSHIPS FOR TABLE `registrations`:
--   `show_id`
--       `shows` -> `id`
--   `user_id`
--       `users` -> `id`
--   `vehicle_id`
--       `vehicles` -> `id`
--   `category_id`
--       `show_categories` -> `id`
--

--
-- Dumping data for table `registrations`
--

INSERT INTO `registrations` (`id`, `show_id`, `vehicle_id`, `registration_number`, `owner_id`, `category_id`, `status`, `payment_status`, `payment_amount`, `payment_method`, `payment_reference`, `qr_code_path`, `qr_code_generated_at`, `payment_date`, `created_at`, `updated_at`, `fee`, `payment_method_id`, `user_id`, `checked_in`, `check_in_time`, `template_id`, `qr_code`, `qr_code_url`, `display_number`, `notes`) VALUES
(60, 9, 2, 'RER-0000000002', 3, 152, 'pending', 'paid', 0.00, NULL, '', NULL, NULL, NULL, '2025-06-11 18:58:40', '2025-07-10 13:38:00', 20.00, 4, 3, 1, '2025-06-13 16:07:10', NULL, 'qr_60_1749670528.png', NULL, 'CL-60', NULL);
