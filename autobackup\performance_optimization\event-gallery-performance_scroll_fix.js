/**
 * High-Performance Event Gallery with Infinite Scroll and Filtering
 */

class EventGalleryPerformance {
    constructor() {
        this.baseUrl = window.BASE_URL || '';
        this.currentPage = 1;
        this.perPage = 20;
        this.loading = false;
        this.hasMore = true;
        this.currentFilter = 'newest';
        this.currentSearch = '';
        this.eventType = '';
        this.eventId = 0;
        this.loadedPhotoIds = new Set(); // Prevent duplicates
        
        this.init();
    }
    
    init() {
        this.extractPageData();
        this.bindEvents();
        this.setupInfiniteScroll();
        this.batchLoadEngagementData();
    }
    
    extractPageData() {
        // Get data from page
        const galleryContainer = document.getElementById('photoGrid');
        if (galleryContainer) {
            this.eventType = galleryContainer.dataset.eventType || '';
            this.eventId = parseInt(galleryContainer.dataset.eventId) || 0;
            this.currentFilter = galleryContainer.dataset.currentFilter || 'newest';
            this.currentSearch = galleryContainer.dataset.currentSearch || '';
            this.currentPage = parseInt(galleryContainer.dataset.currentPage) || 1;
            this.perPage = parseInt(galleryContainer.dataset.perPage) || 20;
            
            // Mark initially loaded photos
            const existingPhotos = galleryContainer.querySelectorAll('[data-photo-id]');
            existingPhotos.forEach(photo => {
                const photoId = photo.dataset.photoId;
                if (photoId) {
                    this.loadedPhotoIds.add(photoId);
                }
            });
        }
    }
    
    bindEvents() {
        // Filter dropdown changes
        const filterSelect = document.getElementById('photoFilter');
        if (filterSelect) {
            filterSelect.addEventListener('change', (e) => {
                this.changeFilter(e.target.value);
            });
        }
        
        // Search input
        const searchInput = document.getElementById('photoSearch');
        if (searchInput) {
            let searchTimeout;
            searchInput.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    this.changeSearch(e.target.value);
                }, 500); // Debounce search
            });
        }
        
        // Clear search button
        const clearSearchBtn = document.getElementById('clearSearch');
        if (clearSearchBtn) {
            clearSearchBtn.addEventListener('click', () => {
                this.changeSearch('');
                if (searchInput) searchInput.value = '';
            });
        }
        
        // Load more button (fallback for infinite scroll)
        const loadMoreBtn = document.getElementById('loadMoreBtn');
        if (loadMoreBtn) {
            loadMoreBtn.addEventListener('click', () => {
                this.loadMorePhotos();
            });
        }
    }
    
    setupInfiniteScroll() {
        // Disable infinite scroll for now to prevent photo disappearing issue
        // Will re-enable after fixing the bug
        return;

        let scrollTimeout;
        window.addEventListener('scroll', () => {
            clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(() => {
                if (this.shouldLoadMore()) {
                    this.loadMorePhotos();
                }
            }, 100); // Throttle scroll events
        });
    }

    shouldLoadMore() {
        if (this.loading || !this.hasMore) return false;

        const scrollPosition = window.innerHeight + window.scrollY;
        const documentHeight = document.documentElement.offsetHeight;
        const threshold = 1000; // Load when 1000px from bottom

        return scrollPosition >= documentHeight - threshold;
    }
    
    async changeFilter(newFilter) {
        if (newFilter === this.currentFilter) return;
        
        this.currentFilter = newFilter;
        this.currentPage = 1;
        this.hasMore = true;
        this.loadedPhotoIds.clear();
        
        // Clear existing photos
        const photoGrid = document.getElementById('photoGrid');
        if (photoGrid) {
            photoGrid.innerHTML = '<div class="col-12 text-center"><div class="spinner-border" role="status"></div></div>';
        }
        
        await this.loadMorePhotos();
        this.updateUrl();
    }
    
    async changeSearch(newSearch) {
        if (newSearch === this.currentSearch) return;
        
        this.currentSearch = newSearch;
        this.currentPage = 1;
        this.hasMore = true;
        this.loadedPhotoIds.clear();
        
        // Clear existing photos
        const photoGrid = document.getElementById('photoGrid');
        if (photoGrid) {
            photoGrid.innerHTML = '<div class="col-12 text-center"><div class="spinner-border" role="status"></div></div>';
        }
        
        await this.loadMorePhotos();
        this.updateUrl();
    }
    
    async loadMorePhotos() {
        if (this.loading || !this.hasMore) return;
        
        this.loading = true;
        this.showLoadingIndicator();
        
        try {
            const response = await fetch(`${this.baseUrl}/image_editor/loadMorePhotos`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    event_type: this.eventType,
                    event_id: this.eventId,
                    page: this.currentPage,
                    per_page: this.perPage,
                    filter: this.currentFilter,
                    search: this.currentSearch
                })
            });
            
            const data = await response.json();
            
            if (data.success) {
                this.renderPhotos(data.photos);
                this.hasMore = data.has_more;
                this.currentPage++;
                
                // Update photo count
                this.updatePhotoCount(data.total);
                
                // Load engagement data for new photos
                this.batchLoadEngagementData();
            } else {
                this.showError('Failed to load photos');
            }
            
        } catch (error) {
            console.error('Load more photos error:', error);
            this.showError('Failed to load photos');
        } finally {
            this.loading = false;
            this.hideLoadingIndicator();
        }
    }
    
    renderPhotos(photos) {
        const photoGrid = document.getElementById('photoGrid');
        if (!photoGrid) return;

        // Only clear the grid if this is a new filter/search (page 1 of new results)
        // Don't clear if we're just loading more photos from infinite scroll
        if (this.currentPage === 1) {
            // Remove any existing loading indicators or "no more" messages
            const loadingIndicator = document.getElementById('loading-indicator');
            if (loadingIndicator) loadingIndicator.remove();

            const endMessage = photoGrid.querySelector('.col-12.text-center');
            if (endMessage) endMessage.remove();
        }

        photos.forEach(photo => {
            // Skip if already loaded (prevent duplicates)
            if (this.loadedPhotoIds.has(photo.id.toString())) {
                return;
            }

            this.loadedPhotoIds.add(photo.id.toString());
            const photoElement = this.createPhotoElement(photo);
            photoGrid.appendChild(photoElement);
        });

        // Show "no more photos" message if needed
        if (!this.hasMore && photos.length === 0 && this.currentPage === 1) {
            photoGrid.innerHTML = `
                <div class="col-12 text-center py-5">
                    <i class="fas fa-camera fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No photos found</h5>
                    <p class="text-muted">Try adjusting your filters or search terms.</p>
                </div>
            `;
        } else if (!this.hasMore && this.loadedPhotoIds.size > 0) {
            // Only add end message if it doesn't already exist
            if (!photoGrid.querySelector('.end-of-gallery')) {
                photoGrid.insertAdjacentHTML('beforeend', `
                    <div class="col-12 text-center py-3 end-of-gallery">
                        <p class="text-muted">You've reached the end of the gallery!</p>
                    </div>
                `);
            }
        }
    }
    
    createPhotoElement(photo) {
        const col = document.createElement('div');
        col.className = 'col-lg-3 col-md-4 col-sm-6 mb-4';
        
        const thumbnailPath = photo.thumbnail_path || photo.file_path;
        const caption = photo.caption || '';
        const uploaderName = photo.uploader_name || 'Unknown';
        const likesCount = photo.likes_count || 0;
        const commentsCount = photo.comments_count || 0;
        const isFavorited = photo.is_favorited_by_user || false;
        
        col.innerHTML = `
            <div class="card h-100 shadow-sm" data-photo-id="${photo.id}">
                <div class="position-relative">
                    <img src="${this.baseUrl}/${thumbnailPath}" 
                         class="card-img-top" 
                         alt="${caption}"
                         style="height: 200px; object-fit: cover;"
                         loading="lazy">
                    
                    <!-- Quick actions overlay -->
                    <div class="position-absolute top-0 end-0 p-2">
                        <button class="btn btn-sm btn-light rounded-circle favorite-btn ${isFavorited ? 'favorited' : ''}" 
                                data-photo-id="${photo.id}" 
                                title="Add to Favorites">
                            <i class="fa${isFavorited ? 's' : 'r'} fa-bookmark text-warning"></i>
                        </button>
                    </div>
                </div>
                
                <div class="card-body p-3">
                    <!-- Photo info -->
                    <div class="mb-2">
                        <small class="text-muted">
                            <i class="fas fa-user me-1"></i>${uploaderName}
                        </small>
                    </div>
                    
                    <!-- Caption -->
                    ${caption ? `<p class="card-text small mb-2">${caption}</p>` : ''}
                    
                    <!-- Engagement actions -->
                    <div class="engagement-section" data-photo-id="${photo.id}">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <div class="engagement-actions d-flex gap-3">
                                <button class="btn btn-link p-0 like-btn" 
                                        data-photo-id="${photo.id}" 
                                        title="Like">
                                    <i class="far fa-heart text-danger"></i>
                                    <span class="like-count ms-1">${likesCount}</span>
                                </button>
                                
                                <button class="btn btn-link p-0 show-comments-btn" 
                                        data-photo-id="${photo.id}" 
                                        title="Comments">
                                    <i class="far fa-comment text-primary"></i>
                                    <span id="comment-count-${photo.id}" class="ms-1">${commentsCount}</span>
                                </button>
                                
                                <div class="dropdown">
                                    <button class="btn btn-link p-0" 
                                            data-bs-toggle="dropdown" 
                                            title="Share">
                                        <i class="far fa-share-square text-success"></i>
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item share-btn" 
                                               data-photo-id="${photo.id}" 
                                               data-platform="facebook" href="#">
                                            <i class="fab fa-facebook-f me-2"></i>Facebook
                                        </a></li>
                                        <li><a class="dropdown-item share-btn" 
                                               data-photo-id="${photo.id}" 
                                               data-platform="copy_link" href="#">
                                            <i class="fas fa-link me-2"></i>Copy Link
                                        </a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Comments section (hidden by default) -->
                        <div id="comments-${photo.id}" class="comments-section mt-2" style="display: none;">
                            <!-- Add comment form -->
                            <div class="add-comment-section mb-3 p-2 bg-light rounded">
                                <form class="comment-form">
                                    <input type="hidden" name="photo_id" value="${photo.id}">
                                    <div class="input-group input-group-sm">
                                        <input type="text" 
                                               name="comment" 
                                               class="form-control" 
                                               placeholder="Add a comment..." 
                                               maxlength="500"
                                               required>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-paper-plane"></i>
                                        </button>
                                    </div>
                                </form>
                            </div>
                            
                            <!-- Comments list -->
                            <div id="comments-list-${photo.id}" class="comments-list">
                                <!-- Comments will be loaded here -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        return col;
    }
    
    batchLoadEngagementData() {
        // Load engagement data for photos that don't have it yet
        const photosNeedingData = document.querySelectorAll('[data-photo-id]:not([data-engagement-loaded])');

        if (photosNeedingData.length === 0) return;

        // Process in batches to avoid overwhelming the server
        const batchSize = 10;
        const photoIds = Array.from(photosNeedingData).map(el => el.dataset.photoId);

        for (let i = 0; i < photoIds.length; i += batchSize) {
            const batch = photoIds.slice(i, i + batchSize);
            setTimeout(() => {
                this.loadEngagementBatch(batch);
            }, i * 100); // Stagger requests
        }
    }

    async loadEngagementBatch(photoIds) {
        try {
            const promises = photoIds.map(photoId =>
                fetch(`${this.baseUrl}/engagement/getEngagementData/${photoId}`)
                    .then(response => response.json())
                    .then(data => ({ photoId, data }))
                    .catch(error => ({ photoId, error }))
            );

            const results = await Promise.all(promises);

            results.forEach(({ photoId, data, error }) => {
                if (data && data.success) {
                    this.updatePhotoEngagement(photoId, data.stats, data.user_engagement);
                }

                // Mark as loaded regardless of success/failure
                const photoElement = document.querySelector(`[data-photo-id="${photoId}"]`);
                if (photoElement) {
                    photoElement.setAttribute('data-engagement-loaded', 'true');
                }
            });

        } catch (error) {
            console.error('Batch engagement load error:', error);
        }
    }

    updatePhotoEngagement(photoId, stats, userEngagement) {
        const photoElement = document.querySelector(`[data-photo-id="${photoId}"]`);
        if (!photoElement) return;

        // Update like button
        const likeBtn = photoElement.querySelector('.like-btn');
        if (likeBtn) {
            const icon = likeBtn.querySelector('i');
            const count = likeBtn.querySelector('.like-count');

            if (userEngagement.liked) {
                likeBtn.classList.add('liked');
                icon.classList.remove('far');
                icon.classList.add('fas');
            }

            if (count) {
                count.textContent = stats.likes_count || 0;
            }
        }

        // Update favorite button
        const favoriteBtn = photoElement.querySelector('.favorite-btn');
        if (favoriteBtn) {
            const icon = favoriteBtn.querySelector('i');

            if (userEngagement.favorited) {
                favoriteBtn.classList.add('favorited');
                icon.classList.remove('far');
                icon.classList.add('fas');
            }
        }

        // Update comment count
        const commentCount = photoElement.querySelector(`#comment-count-${photoId}`);
        if (commentCount) {
            commentCount.textContent = stats.comments_count || 0;
        }
    }

    showLoadingIndicator() {
        const loadMoreBtn = document.getElementById('loadMoreBtn');
        if (loadMoreBtn) {
            loadMoreBtn.disabled = true;
            loadMoreBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Loading...';
        }

        // Add loading indicator to bottom of grid
        const photoGrid = document.getElementById('photoGrid');
        if (photoGrid && !document.getElementById('loading-indicator')) {
            photoGrid.insertAdjacentHTML('beforeend', `
                <div id="loading-indicator" class="col-12 text-center py-3">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
            `);
        }
    }

    hideLoadingIndicator() {
        const loadMoreBtn = document.getElementById('loadMoreBtn');
        if (loadMoreBtn) {
            loadMoreBtn.disabled = false;
            loadMoreBtn.innerHTML = '<i class="fas fa-plus me-2"></i>Load More';
        }

        const loadingIndicator = document.getElementById('loading-indicator');
        if (loadingIndicator) {
            loadingIndicator.remove();
        }
    }

    updatePhotoCount(total) {
        // Update the main photo count in the header
        const headerCountElement = document.querySelector('.photo-count');
        if (headerCountElement) {
            headerCountElement.textContent = `${total} photo${total !== 1 ? 's' : ''}`;
        }
    }

    updateUrl() {
        const url = new URL(window.location);
        url.searchParams.set('filter', this.currentFilter);
        if (this.currentSearch) {
            url.searchParams.set('search', this.currentSearch);
        } else {
            url.searchParams.delete('search');
        }
        url.searchParams.set('page', '1'); // Reset to page 1 for new filters

        window.history.replaceState({}, '', url);
    }

    showError(message) {
        if (window.pwaFeatures) {
            window.pwaFeatures.showNotification('Error', message, 'error');
        } else {
            console.error('Error:', message);
        }
    }
}

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.eventGalleryPerformance = new EventGalleryPerformance();
});
