
-- --------------------------------------------------------

--
-- Structure for view `pending_role_requests`
--
DROP TABLE IF EXISTS `pending_role_requests`;

CREATE ALGORITHM=UNDEFINED DEFINER=`sql24006_forward-limpet`@`108.70.196.156` SQL SECURITY DEFINER VIEW `pending_role_requests`  AS SELECT `srr`.`id` AS `id`, `srr`.`user_id` AS `user_id`, `srr`.`show_id` AS `show_id`, `s`.`name` AS `show_name`, `srr`.`requested_role` AS `requested_role`, `srr`.`requested_at` AS `requested_at`, `srr`.`expires_at` AS `expires_at`, `u`.`name` AS `user_name`, `u`.`email` AS `user_email`, `requester`.`name` AS `requested_by_name` FROM (((`show_role_requests` `srr` join `shows` `s` on(`srr`.`show_id` = `s`.`id`)) join `users` `u` on(`srr`.`user_id` = `u`.`id`)) join `users` `requester` on(`srr`.`requested_by` = `requester`.`id`)) WHERE `srr`.`status` = 'pending' AND `srr`.`expires_at` > current_timestamp() ;
