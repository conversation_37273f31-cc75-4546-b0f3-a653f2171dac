
-- --------------------------------------------------------

--
-- Table structure for table `notification_queue`
--
-- Creation: Jul 12, 2025 at 04:48 PM
--

CREATE TABLE `notification_queue` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `subscription_id` int(11) DEFAULT NULL,
  `notification_type` enum('email','sms','push','toast') NOT NULL,
  `event_id` int(11) DEFAULT NULL,
  `event_type` enum('calendar_event','car_show','test') DEFAULT NULL,
  `notification_category` enum('event_reminder','registration_deadline','test') NOT NULL DEFAULT 'event_reminder',
  `scheduled_for` datetime NOT NULL,
  `subject` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `notification_data` text DEFAULT NULL COMMENT 'JSON data for notification content',
  `metadata` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'Additional context data' CHECK (json_valid(`metadata`)),
  `status` enum('pending','sent','failed','cancelled') DEFAULT 'pending',
  `attempts` int(11) DEFAULT 0,
  `last_attempt` datetime DEFAULT NULL,
  `error_message` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `sent_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- RELATIONSHIPS FOR TABLE `notification_queue`:
--

--
-- Dumping data for table `notification_queue`
--

INSERT INTO `notification_queue` (`id`, `user_id`, `subscription_id`, `notification_type`, `event_id`, `event_type`, `notification_category`, `scheduled_for`, `subject`, `message`, `notification_data`, `metadata`, `status`, `attempts`, `last_attempt`, `error_message`, `created_at`, `updated_at`, `sent_at`) VALUES
(396, 3, 0, 'toast', 0, 'calendar_event', 'event_reminder', '2025-07-11 20:51:36', 'Queued Toast Test [TEST]', 'This is a queued toast notification test at 20:51:36', NULL, NULL, 'sent', 0, '2025-07-11 20:51:43', NULL, '2025-07-11 20:51:36', '2025-07-11 20:51:43', '2025-07-11 20:51:43'),
(398, 3, 0, 'toast', 0, 'calendar_event', 'event_reminder', '2025-07-11 20:55:34', 'Queued Toast Test [TEST]', 'This is a queued toast notification test at 20:55:34', NULL, NULL, 'sent', 0, '2025-07-11 20:55:44', NULL, '2025-07-11 20:55:34', '2025-07-11 20:55:44', '2025-07-11 20:55:44'),
(399, 3, 0, 'email', 0, 'calendar_event', 'event_reminder', '2025-07-12 18:01:24', 'dfgfdgfdg [TEST]', 'Hello Brian Correll,\n\nYou have received a message from Brian Correll regarding the show \'Summer Classic Auto Show 2025\':\n\n---\ngfdgfdgdfgd\n---\n\nYou can reply to this message through the notification center. Your reply will be sent as a notification to Brian Correll.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-12 18:05:06', NULL, '2025-07-12 18:01:24', '2025-07-12 18:05:06', '2025-07-12 18:05:06'),
(401, 3, 0, 'toast', 0, 'calendar_event', 'event_reminder', '2025-07-12 18:01:24', 'dfgfdgfdg [TEST]', 'Hello Brian Correll,\n\nYou have received a message from Brian Correll regarding the show \'Summer Classic Auto Show 2025\':\n\n---\ngfdgfdgdfgd\n---\n\nYou can reply to this message through the notification center. Your reply will be sent as a notification to Brian Correll.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-12 18:05:07', NULL, '2025-07-12 18:01:24', '2025-07-12 18:05:07', '2025-07-12 18:05:07'),
(402, 3, 0, 'email', 0, 'calendar_event', 'event_reminder', '2025-07-12 20:09:13', 'Test Judge Message - 2025-07-12 20:09:13 [TEST]', 'You received a message from Brian Correll:\n\nThis is a test message from Judge Management system.\n\nPlease confirm you received this message.\n\n[reply]', NULL, NULL, 'sent', 0, '2025-07-12 20:09:13', NULL, '2025-07-12 20:09:13', '2025-07-12 20:09:13', '2025-07-12 20:09:13'),
(404, 3, 0, 'toast', 0, 'calendar_event', 'event_reminder', '2025-07-12 20:09:13', 'Test Judge Message - 2025-07-12 20:09:13 [TEST]', 'You received a message from Brian Correll:\n\nThis is a test message from Judge Management system.\n\nPlease confirm you received this message.\n\n[reply]', NULL, NULL, 'sent', 0, '2025-07-12 20:09:14', NULL, '2025-07-12 20:09:13', '2025-07-12 20:09:14', '2025-07-12 20:09:14'),
(405, 3, 0, 'email', 0, 'calendar_event', 'event_reminder', '2025-07-12 20:10:12', 'Test Judge Message - 2025-07-12 20:10:12 [TEST]', 'You received a message from Brian Correll:\n\nThis is a test message from Judge Management system.\n\nPlease confirm you received this message.\n\n[reply]', NULL, NULL, 'sent', 0, '2025-07-12 20:10:12', NULL, '2025-07-12 20:10:12', '2025-07-12 20:10:12', '2025-07-12 20:10:12'),
(407, 3, 0, 'toast', 0, 'calendar_event', 'event_reminder', '2025-07-12 20:10:12', 'Test Judge Message - 2025-07-12 20:10:12 [TEST]', 'You received a message from Brian Correll:\n\nThis is a test message from Judge Management system.\n\nPlease confirm you received this message.\n\n[reply]', NULL, NULL, 'sent', 0, '2025-07-12 20:10:12', NULL, '2025-07-12 20:10:12', '2025-07-12 20:10:12', '2025-07-12 20:10:12'),
(408, 3, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'Re: Simple Test Message - 2025-07-12 19:29:40', 'Hello Brian Correll,\n\nYou have received a message from Brian Correll:\n\nSubject: Re: Simple Test Message - 2025-07-12 19:29:40\n\nxcgxcgxcvx\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-13 01:15:05', NULL, '2025-07-13 01:14:35', '2025-07-13 01:15:05', '2025-07-13 01:15:05'),
(409, 3, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'Re: Simple Test Message - 2025-07-12 19:29:40', 'Hello Brian Correll,\n\nYou have received a message from Brian Correll:\n\nSubject: Re: Simple Test Message - 2025-07-12 19:29:40\n\nhfghfghfg\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-13 11:15:05', NULL, '2025-07-13 11:12:16', '2025-07-13 11:15:05', '2025-07-13 11:15:05'),
(410, 3, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'Re: Simple Test Message - 2025-07-12 19:29:40', 'Hello Brian Correll,\n\nYou have received a message from Brian Correll:\n\nSubject: Re: Simple Test Message - 2025-07-12 19:29:40\n\ndfhdfgdf\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-13 11:15:06', NULL, '2025-07-13 11:13:46', '2025-07-13 11:15:06', '2025-07-13 11:15:06'),
(411, 3, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'Re: dfgfdgfdg', 'Hello Brian Correll,\n\nYou have received a message from Brian Correll:\n\nSubject: Re: dfgfdgfdg\n\ngfdgdfgdfg\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-13 11:15:06', NULL, '2025-07-13 11:13:52', '2025-07-13 11:15:06', '2025-07-13 11:15:06'),
(412, 3, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'Re: Simple Test Message - 2025-07-12 19:29:40', 'Hello Brian Correll,\n\nYou have received a message from Brian Correll:\n\nSubject: Re: Simple Test Message - 2025-07-12 19:29:40\n\ntesting again\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-13 13:15:04', NULL, '2025-07-13 13:11:29', '2025-07-13 13:15:04', '2025-07-13 13:15:04'),
(413, 3, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'Re: dfgfdgfdg', 'Hello Brian Correll,\n\nYou have received a message from Brian Correll:\n\nSubject: Re: dfgfdgfdg\n\ntest\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-13 13:40:04', NULL, '2025-07-13 13:36:43', '2025-07-13 13:40:04', '2025-07-13 13:40:04'),
(414, 3, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'Re: Simple Test Message - 2025-07-12 19:29:40', 'Hello Brian Correll,\n\nYou have received a message from Brian Correll:\n\nSubject: Re: Simple Test Message - 2025-07-12 19:29:40\n\ntest4\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-13 14:00:05', NULL, '2025-07-13 13:56:11', '2025-07-13 14:00:05', '2025-07-13 14:00:05'),
(415, 3, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'Re: Simple Test Message - 2025-07-12 19:29:40', 'Hello Brian Correll,\n\nYou have received a message from Brian Correll:\n\nSubject: Re: Simple Test Message - 2025-07-12 19:29:40\n\nfdghfdgdfgd\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-13 15:05:04', NULL, '2025-07-13 15:04:42', '2025-07-13 15:05:04', '2025-07-13 15:05:04'),
(416, 3, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'Re: Simple Test Message - 2025-07-12 19:29:40', 'Hello Brian Correll,\n\nYou have received a message from Brian Correll:\n\nSubject: Re: Simple Test Message - 2025-07-12 19:29:40\n\ngfdgdfgdfgd\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-13 15:05:04', NULL, '2025-07-13 15:04:57', '2025-07-13 15:05:04', '2025-07-13 15:05:04'),
(417, 3, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'dfgfdgdfgfdg', 'Hello Brian Correll,\n\nYou have received a message from Brian Correll:\n\nSubject: dfgfdgdfgfdg\n\ngdgdfgdfgfdgdfg\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-13 15:10:04', NULL, '2025-07-13 15:08:22', '2025-07-13 15:10:04', '2025-07-13 15:10:04'),
(418, 3, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'Re: dfgfdgdfgfdg', 'Hello Brian Correll,\n\nYou have received a message from Brian Correll:\n\nSubject: Re: dfgfdgdfgfdg\n\nfgfdgdfgd\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-13 15:10:04', NULL, '2025-07-13 15:08:54', '2025-07-13 15:10:04', '2025-07-13 15:10:04'),
(419, 3, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'Re: dfgfdgdfgfdg', 'Hello Brian Correll,\n\nYou have received a message from Brian Correll:\n\nSubject: Re: dfgfdgdfgfdg\n\nfdgfdgdfgd\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-13 15:10:04', NULL, '2025-07-13 15:09:01', '2025-07-13 15:10:04', '2025-07-13 15:10:04'),
(420, 3, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'Re: dfgfdgdfgfdg', 'Hello Brian Correll,\n\nYou have received a message from Brian Correll:\n\nSubject: Re: dfgfdgdfgfdg\n\ndfgdfgdfgdfgdfgd\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-13 15:15:06', NULL, '2025-07-13 15:15:03', '2025-07-13 15:15:06', '2025-07-13 15:15:06'),
(421, 3, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'Re: dfgfdgdfgfdg', 'Hello Brian Correll,\n\nYou have received a message from Brian Correll:\n\nSubject: Re: dfgfdgdfgfdg\n\nuyfguygyjgyjgyj\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-13 15:30:05', NULL, '2025-07-13 15:25:19', '2025-07-13 15:30:05', '2025-07-13 15:30:05'),
(422, 3, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'Re: dfgfdgdfgfdg', 'Hello Brian Correll,\n\nYou have received a message from Brian Correll:\n\nSubject: Re: dfgfdgdfgfdg\n\nghfghfghfhg\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-13 15:30:05', NULL, '2025-07-13 15:26:00', '2025-07-13 15:30:05', '2025-07-13 15:30:05'),
(423, 3, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'Re: dfgfdgdfgfdg', 'Hello Brian Correll,\n\nYou have received a message from Brian Correll:\n\nSubject: Re: dfgfdgdfgfdg\n\nghfgfghfgh\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-13 15:30:06', NULL, '2025-07-13 15:26:08', '2025-07-13 15:30:06', '2025-07-13 15:30:06'),
(424, 3, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'Re: dfgfdgdfgfdg', 'Hello Brian Correll,\n\nYou have received a message from Brian Correll:\n\nSubject: Re: dfgfdgdfgfdg\n\ntest\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-13 19:10:05', NULL, '2025-07-13 19:08:09', '2025-07-13 19:10:05', '2025-07-13 19:10:05'),
(425, 3, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'dfgdfgdfg', 'Hello Brian Correll,\n\nYou have received a message from Brian Correll:\n\nSubject: dfgdfgdfg\n\ndfgfdgdfgdfgdfgd\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-14 00:45:06', NULL, '2025-07-14 00:40:42', '2025-07-14 00:45:06', '2025-07-14 00:45:06'),
(426, 3, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'fdgfdgdfg', 'Hello Brian Correll,\n\nYou have received a message from Brian Correll:\n\nSubject: fdgfdgdfg\n\ndfgdfgdfgdfg\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-14 15:40:04', NULL, '2025-07-14 15:38:30', '2025-07-14 15:40:04', '2025-07-14 15:40:04'),
(427, 3, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'hjfghfgh', 'Hello Brian Correll,\n\nYou have received a message from Brian Correll:\n\nSubject: hjfghfgh\n\nhjfghfghfghf\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-14 18:55:06', NULL, '2025-07-14 18:51:18', '2025-07-14 18:55:06', '2025-07-14 18:55:06'),
(428, 3, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'dfgdfgfdgfd', 'Hello Brian Correll,\n\nYou have received a message from Brian Correll:\n\nSubject: dfgdfgfdgfd\n\ngdfgdfgfdgdfgd\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-15 00:45:04', NULL, '2025-07-15 00:41:44', '2025-07-15 00:45:04', '2025-07-15 00:45:04'),
(429, 3, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'ndndnx', 'Hello Brian Correll,\n\nYou have received a message from Brian Correll:\n\nSubject: ndndnx\n\nndnxndn\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-15 01:55:04', NULL, '2025-07-15 01:50:10', '2025-07-15 01:55:04', '2025-07-15 01:55:04'),
(431, 1, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'New Message: 🎉 Test Message Subject', 'Hello System,\n\nYou have received a message from System:\n\nSubject: 🎉 Test Message Subject\n\nThis is a test message to verify that both the subject and full message content are properly sent via FCM push notifications and email. The message should appear in both the notification title/subject and the message body.\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-15 13:15:04', NULL, '2025-07-15 13:10:55', '2025-07-15 13:15:04', '2025-07-15 13:15:04'),
(432, 1, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'New Message: Test Message', 'Hello System,\n\nYou have received a message from System:\n\nSubject: Test Message\n\nThis is a simple test message.\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-15 13:15:04', NULL, '2025-07-15 13:11:24', '2025-07-15 13:15:04', '2025-07-15 13:15:04'),
(433, 1, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'New Message: Test Message', 'Hello System,\n\nYou have received a message from System:\n\nSubject: Test Message\n\nThis is a simple test message.\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-15 13:20:06', NULL, '2025-07-15 13:16:30', '2025-07-15 13:20:06', '2025-07-15 13:20:06'),
(434, 1, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'New Message: Test Message', 'Hello System,\n\nYou have received a message from System:\n\nSubject: Test Message\n\nThis is a simple test message.\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-15 13:20:07', NULL, '2025-07-15 13:16:38', '2025-07-15 13:20:07', '2025-07-15 13:20:07'),
(435, 1, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'New Message: Test Message', 'Hello System,\n\nYou have received a message from System:\n\nSubject: Test Message\n\nThis is a simple test message.\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-15 13:20:07', NULL, '2025-07-15 13:16:39', '2025-07-15 13:20:07', '2025-07-15 13:20:07'),
(436, 1, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'New Message: Test Message', 'Hello System,\n\nYou have received a message from System:\n\nSubject: Test Message\n\nThis is a simple test message.\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-15 13:25:03', NULL, '2025-07-15 13:23:47', '2025-07-15 13:25:03', '2025-07-15 13:25:03'),
(437, 3, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'New Message: Test Message', 'Hello Brian Correll,\n\nYou have received a message from Brian Correll:\n\nSubject: Test Message\n\nThis is a simple test message.\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-15 13:30:05', NULL, '2025-07-15 13:28:19', '2025-07-15 13:30:05', '2025-07-15 13:30:05'),
(438, 3, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'New Message: Test Message', 'Hello Brian Correll,\n\nYou have received a message from Brian Correll:\n\nSubject: Test Message\n\nThis is a simple test message.\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-15 13:30:06', NULL, '2025-07-15 13:29:12', '2025-07-15 13:30:06', '2025-07-15 13:30:06'),
(439, 3, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'New Message: Test Message', 'Hello Brian Correll,\n\nYou have received a message from Brian Correll:\n\nSubject: Test Message\n\nThis is a simple test message.\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-15 13:35:04', NULL, '2025-07-15 13:30:12', '2025-07-15 13:35:04', '2025-07-15 13:35:04'),
(440, 3, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'New Message: Test Message', 'Hello Brian Correll,\n\nYou have received a message from Brian Correll:\n\nSubject: Test Message\n\nThis is a simple test message.\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-15 13:35:04', NULL, '2025-07-15 13:30:48', '2025-07-15 13:35:04', '2025-07-15 13:35:04'),
(441, 3, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'New Message: Test Message', 'Hello Brian Correll,\n\nYou have received a message from Brian Correll:\n\nSubject: Test Message\n\nThis is a simple test message.\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-15 13:35:04', NULL, '2025-07-15 13:31:33', '2025-07-15 13:35:04', '2025-07-15 13:35:04'),
(442, 3, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'New Message: Test Message', 'Hello Brian Correll,\n\nYou have received a message from Brian Correll:\n\nSubject: Test Message\n\nThis is a simple test message.\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-15 13:35:05', NULL, '2025-07-15 13:32:04', '2025-07-15 13:35:05', '2025-07-15 13:35:05'),
(443, 3, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'New Message: Test Message', 'Hello Brian Correll,\n\nYou have received a message from Brian Correll:\n\nSubject: Test Message\n\nThis is a simple test message.\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-15 13:35:05', NULL, '2025-07-15 13:32:28', '2025-07-15 13:35:05', '2025-07-15 13:35:05'),
(444, 3, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'New Message: Test Message', 'Hello Brian Correll,\n\nYou have received a message from Brian Correll:\n\nSubject: Test Message\n\nThis is a simple test message.\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-15 13:40:04', NULL, '2025-07-15 13:35:29', '2025-07-15 13:40:04', '2025-07-15 13:40:04'),
(445, 3, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'New Message: Test Message', 'Hello Brian Correll,\n\nYou have received a message from Brian Correll:\n\nSubject: Test Message\n\nThis is a simple test message.\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-15 13:40:04', NULL, '2025-07-15 13:35:57', '2025-07-15 13:40:04', '2025-07-15 13:40:04'),
(446, 3, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'New Message: Test Message', 'Hello Brian Correll,\n\nYou have received a message from Brian Correll:\n\nSubject: Test Message\n\nThis is a simple test message.\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-15 13:40:04', NULL, '2025-07-15 13:36:10', '2025-07-15 13:40:04', '2025-07-15 13:40:04'),
(447, 3, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'New Message: Test Message', 'Hello Brian Correll,\n\nYou have received a message from Brian Correll:\n\nSubject: Test Message\n\nThis is a simple test message.\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-15 13:40:04', NULL, '2025-07-15 13:36:48', '2025-07-15 13:40:04', '2025-07-15 13:40:04'),
(448, 3, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'New Message: Test Message', 'Hello Brian Correll,\n\nYou have received a message from Brian Correll:\n\nSubject: Test Message\n\nThis is a simple test message.\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-15 13:40:04', NULL, '2025-07-15 13:37:51', '2025-07-15 13:40:04', '2025-07-15 13:40:04'),
(449, 3, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'New Message: Test Message', 'Hello Brian Correll,\n\nYou have received a message from Brian Correll:\n\nSubject: Test Message\n\nThis is a simple test message.\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-15 13:40:04', NULL, '2025-07-15 13:38:22', '2025-07-15 13:40:04', '2025-07-15 13:40:04'),
(450, 3, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'New Message: Test Message', 'Hello Brian Correll,\n\nYou have received a message from Brian Correll:\n\nSubject: Test Message\n\nThis is a simple test message.\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-15 14:05:04', NULL, '2025-07-15 14:04:52', '2025-07-15 14:05:04', '2025-07-15 14:05:04'),
(451, 3, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'New Message: Test Message', 'Hello Brian Correll,\n\nYou have received a message from Brian Correll:\n\nSubject: Test Message\n\nThis is a simple test message.\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-15 14:10:05', NULL, '2025-07-15 14:05:22', '2025-07-15 14:10:05', '2025-07-15 14:10:05'),
(452, 3, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'New Message: Test Message', 'Hello Brian Correll,\n\nYou have received a message from Brian Correll:\n\nSubject: Test Message\n\nThis is a simple test message.\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-15 14:10:06', NULL, '2025-07-15 14:05:56', '2025-07-15 14:10:06', '2025-07-15 14:10:06'),
(453, 3, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'New Message: Test Message', 'Hello Brian Correll,\n\nYou have received a message from Brian Correll:\n\nSubject: Test Message\n\nThis is a simple test message.\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-15 14:20:04', NULL, '2025-07-15 14:16:47', '2025-07-15 14:20:04', '2025-07-15 14:20:04'),
(454, 3, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'New Message: Test Message', 'Hello Brian Correll,\n\nYou have received a message from Brian Correll:\n\nSubject: Test Message\n\nThis is a simple test message.\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-15 14:25:05', NULL, '2025-07-15 14:24:33', '2025-07-15 14:25:05', '2025-07-15 14:25:05'),
(455, 3, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'New Message: Test Message', 'Hello Brian Correll,\n\nYou have received a message from Brian Correll:\n\nSubject: Test Message\n\nThis is a simple test message.\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-15 15:00:06', NULL, '2025-07-15 14:56:28', '2025-07-15 15:00:06', '2025-07-15 15:00:06'),
(456, 3, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'New Message: Test Message', 'Hello Brian Correll,\n\nYou have received a message from Brian Correll:\n\nSubject: Test Message\n\nThis is a simple test message.\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-15 15:10:06', NULL, '2025-07-15 15:06:13', '2025-07-15 15:10:06', '2025-07-15 15:10:06'),
(457, 3, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'New Message: Test Message', 'Hello Brian Correll,\n\nYou have received a message from Brian Correll:\n\nSubject: Test Message\n\nThis is a simple test message.\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-15 15:35:05', NULL, '2025-07-15 15:33:27', '2025-07-15 15:35:05', '2025-07-15 15:35:05'),
(458, 3, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'New Message: dfgfdgfeg', 'Hello Brian Correll,\n\nYou have received a message from Brian Correll:\n\nSubject: dfgfdgfeg\n\ndfgdfgdfgfdg\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-15 16:30:05', NULL, '2025-07-15 16:25:27', '2025-07-15 16:30:05', '2025-07-15 16:30:05'),
(459, 3, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'New Message: Test Message', 'Hello Brian Correll,\n\nYou have received a message from Brian Correll:\n\nSubject: Test Message\n\nThis is a simple test message.\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-15 16:50:04', NULL, '2025-07-15 16:49:01', '2025-07-15 16:50:04', '2025-07-15 16:50:04'),
(460, 3, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'New Message: Test Message', 'Hello Brian Correll,\n\nYou have received a message from Brian Correll:\n\nSubject: Test Message\n\nThis is a simple test message.\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-15 17:05:04', NULL, '2025-07-15 17:04:27', '2025-07-15 17:05:04', '2025-07-15 17:05:04'),
(461, 3, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'New Message: gdfgdf', 'Hello Brian Correll,\n\nYou have received a message from Admin:\n\nSubject: gdfgdf\n\ngdfgdfgdfg\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-15 20:25:05', NULL, '2025-07-15 20:24:56', '2025-07-15 20:25:05', '2025-07-15 20:25:05'),
(462, 3, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'New Message: testing', 'Hello Brian Correll,\n\nYou have received a message from Brian Correll:\n\nSubject: testing\n\nhello whats up\n1\n2\n3 [reply]\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-15 21:20:04', NULL, '2025-07-15 21:19:23', '2025-07-15 21:20:04', '2025-07-15 21:20:04'),
(463, 3, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'New Message: test', 'Hello Brian Correll,\n\nYou have received a message from Admin:\n\nSubject: test\n\ntest\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-15 21:35:04', NULL, '2025-07-15 21:31:32', '2025-07-15 21:35:04', '2025-07-15 21:35:04'),
(464, 3, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'New Message: fgjhgjhgjgh', 'Hello Brian Correll,\n\nYou have received a message from Admin:\n\nSubject: fgjhgjhgjgh\n\njghjghjgh\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-15 21:45:03', NULL, '2025-07-15 21:44:42', '2025-07-15 21:45:03', '2025-07-15 21:45:03'),
(465, 3, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'New Message: Contact Form: test', 'Hello Brian Correll,\n\nYou have received a message from System:\n\nSubject: Contact Form: test\n\nNew contact form submission:\n\nName: dffdfgfdgd\nEmail: <EMAIL>\nSubject: test\n\nMessage:\ntest\n\n---\nThis message was sent via the contact form on 2025-07-15 22:25:13\nReply directly to: <EMAIL>\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-15 22:30:05', NULL, '2025-07-15 22:25:13', '2025-07-15 22:30:05', '2025-07-15 22:30:05'),
(466, 211, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'New Message: Contact Form: test', 'Hello Michelle Schenk Correll,\n\nYou have received a message from System:\n\nSubject: Contact Form: test\n\nNew contact form submission:\n\nName: dffdfgfdgd\nEmail: <EMAIL>\nSubject: test\n\nMessage:\ntest\n\n---\nThis message was sent via the contact form on 2025-07-15 22:25:13\nReply directly to: <EMAIL>\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-15 22:30:05', NULL, '2025-07-15 22:25:14', '2025-07-15 22:30:05', '2025-07-15 22:30:05'),
(467, 3, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'New Message: Contact Form: fghfghfgh', 'Hello Brian Correll,\n\nYou have received a message from System:\n\nSubject: Contact Form: fghfghfgh\n\nNew contact form submission:\n\nName: brian\nEmail: <EMAIL>\nSubject: fghfghfgh\n\nMessage:\nfghfghfghfgh\n\n---\nThis message was sent via the contact form on 2025-07-16 00:37:37\nReply directly to: <EMAIL>\n\n[reply]\nCONTACT_EMAIL:<EMAIL>\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-16 00:40:04', NULL, '2025-07-16 00:37:37', '2025-07-16 00:40:04', '2025-07-16 00:40:04'),
(468, 211, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'New Message: Contact Form: fghfghfgh', 'Hello Michelle Schenk Correll,\n\nYou have received a message from System:\n\nSubject: Contact Form: fghfghfgh\n\nNew contact form submission:\n\nName: brian\nEmail: <EMAIL>\nSubject: fghfghfgh\n\nMessage:\nfghfghfghfgh\n\n---\nThis message was sent via the contact form on 2025-07-16 00:37:37\nReply directly to: <EMAIL>\n\n[reply]\nCONTACT_EMAIL:<EMAIL>\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-16 00:40:04', NULL, '2025-07-16 00:37:37', '2025-07-16 00:40:04', '2025-07-16 00:40:04'),
(469, 1, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'New Message: Re: Contact Form: fghfghfgh', 'Hello System,\n\nYou have received a message from Brian Correll:\n\nSubject: Re: Contact Form: fghfghfgh\n\nrepling to you\n\n[This reply was sent via email to: <EMAIL>]\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-16 00:40:05', NULL, '2025-07-16 00:38:19', '2025-07-16 00:40:05', '2025-07-16 00:40:05'),
(470, 3, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'New Message: Contact Form: kjflgjldfjgkldfjklg', 'Hello Brian Correll,\n\nYou have received a message from System:\n\nSubject: Contact Form: kjflgjldfjgkldfjklg\n\nNew contact form submission:\n\nName: dfghdfgdfg\nEmail: <EMAIL>\nSubject: kjflgjldfjgkldfjklg\n\nMessage:\ngfdgdfgdfgdfgdfg\n\n---\nThis message was sent via the contact form on 2025-07-16 01:01:00\nReply directly to: <EMAIL>\n\n[reply]\nCONTACT_EMAIL:<EMAIL>\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-16 01:05:04', NULL, '2025-07-16 01:01:00', '2025-07-16 01:05:04', '2025-07-16 01:05:04'),
(471, 211, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'New Message: Contact Form: kjflgjldfjgkldfjklg', 'Hello Michelle Schenk Correll,\n\nYou have received a message from System:\n\nSubject: Contact Form: kjflgjldfjgkldfjklg\n\nNew contact form submission:\n\nName: dfghdfgdfg\nEmail: <EMAIL>\nSubject: kjflgjldfjgkldfjklg\n\nMessage:\ngfdgdfgdfgdfgdfg\n\n---\nThis message was sent via the contact form on 2025-07-16 01:01:00\nReply directly to: <EMAIL>\n\n[reply]\nCONTACT_EMAIL:<EMAIL>\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-16 01:05:04', NULL, '2025-07-16 01:01:00', '2025-07-16 01:05:04', '2025-07-16 01:05:04'),
(472, 1, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'New Message: Re: Contact Form: kjflgjldfjgkldfjklg', 'Hello System,\n\nYou have received a message from Brian Correll:\n\nSubject: Re: Contact Form: kjflgjldfjgkldfjklg\n\nhello\n\n[This reply was sent via email to: <EMAIL>]\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-16 01:15:04', NULL, '2025-07-16 01:12:18', '2025-07-16 01:15:04', '2025-07-16 01:15:04'),
(473, 1, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'New Message: Re: Contact Form: kjflgjldfjgkldfjklg', 'Hello System,\n\nYou have received a message from Brian Correll:\n\nSubject: Re: Contact Form: kjflgjldfjgkldfjklg\n\nihgkjhjkhjkhjkhjkhkjhkjhjkhjkhjkhjk\n\n[This reply was sent via email to: <EMAIL>]\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-16 01:15:04', NULL, '2025-07-16 01:14:33', '2025-07-16 01:15:04', '2025-07-16 01:15:04'),
(474, 3, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'New Message: Contact Form: test email', 'Hello Brian Correll,\n\nYou have received a message from System:\n\nSubject: Contact Form: test email\n\nNew contact form submission:\n\nName: dgdfgdfgdf\nEmail: <EMAIL>\nSubject: test email\n\nMessage:\nemailing\n\n---\nThis message was sent via the contact form on 2025-07-16 12:00:15\nReply directly to: <EMAIL>\n\n[reply]\nCONTACT_EMAIL:<EMAIL>\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-16 12:05:04', NULL, '2025-07-16 12:00:15', '2025-07-16 12:05:04', '2025-07-16 12:05:04'),
(475, 211, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'New Message: Contact Form: test email', 'Hello Michelle Schenk Correll,\n\nYou have received a message from System:\n\nSubject: Contact Form: test email\n\nNew contact form submission:\n\nName: dgdfgdfgdf\nEmail: <EMAIL>\nSubject: test email\n\nMessage:\nemailing\n\n---\nThis message was sent via the contact form on 2025-07-16 12:00:15\nReply directly to: <EMAIL>\n\n[reply]\nCONTACT_EMAIL:<EMAIL>\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-16 12:05:04', NULL, '2025-07-16 12:00:16', '2025-07-16 12:05:04', '2025-07-16 12:05:04'),
(476, 1, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'New Message: Re: Contact Form: test email', 'Hello System,\n\nYou have received a message from Brian Correll:\n\nSubject: Re: Contact Form: test email\n\nfdgdfgfdgdfgd\n\n[This reply was sent via email to: <EMAIL>]\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-16 12:05:04', NULL, '2025-07-16 12:01:33', '2025-07-16 12:05:04', '2025-07-16 12:05:04'),
(477, 3, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'New Message: Contact Form: fsdgsdfsdf', 'Hello Brian Correll,\n\nYou have received a message from System:\n\nSubject: Contact Form: fsdgsdfsdf\n\nNew contact form submission:\n\nName: fgsdgsdfgsdf\nEmail: <EMAIL>\nSubject: fsdgsdfsdf\n\nMessage:\nfsdfsdfsdf\n\n---\nThis message was sent via the contact form on 2025-07-16 12:22:02\nReply directly to: <EMAIL>\n\n[reply]\nCONTACT_EMAIL:<EMAIL>\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-16 13:00:10', NULL, '2025-07-16 12:22:02', '2025-07-16 13:00:10', '2025-07-16 13:00:10'),
(478, 211, NULL, 'email', NULL, NULL, 'event_reminder', '0000-00-00 00:00:00', 'New Message: Contact Form: fsdgsdfsdf', 'Hello Michelle Schenk Correll,\n\nYou have received a message from System:\n\nSubject: Contact Form: fsdgsdfsdf\n\nNew contact form submission:\n\nName: fgsdgsdfgsdf\nEmail: <EMAIL>\nSubject: fsdgsdfsdf\n\nMessage:\nfsdfsdfsdf\n\n---\nThis message was sent via the contact form on 2025-07-16 12:22:02\nReply directly to: <EMAIL>\n\n[reply]\nCONTACT_EMAIL:<EMAIL>\n\nYou can view and reply to this message in your notification center.\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-16 13:00:11', NULL, '2025-07-16 12:22:02', '2025-07-16 13:00:11', '2025-07-16 13:00:11'),
(480, 3, NULL, 'email', NULL, NULL, '', '2025-07-16 14:02:47', 'New Message: Contact Form: test contact form', 'Hello Brian Correll,\n\nYou have received a message from System:\n\nSubject: Contact Form: test contact form\n\nNew contact form submission:\n\nName: ham burger\nEmail: <EMAIL>\nSubject: test contact form\n\nMessage:\nthis is\r\na \r\ntest\r\nof \r\nthe contact form \r\ntesting to see\r\nif its working correctly\r\n1\r\n2\r\n3\n\n---\nThis message was sent via the contact form on 2025-07-16 14:02:47\nReply directly to: <EMAIL>\n\n[reply]\nCONTACT_EMAIL:<EMAIL>\n\nYou can view and reply to this message in your notification center:\nhttps://events.rowaneliterides.com/notification-center\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-16 14:05:05', NULL, '2025-07-16 14:02:47', '2025-07-16 14:05:05', '2025-07-16 14:05:05'),
(481, 211, NULL, 'email', NULL, NULL, '', '2025-07-16 14:02:47', 'New Message: Contact Form: test contact form', 'Hello Michelle Schenk Correll,\n\nYou have received a message from System:\n\nSubject: Contact Form: test contact form\n\nNew contact form submission:\n\nName: ham burger\nEmail: <EMAIL>\nSubject: test contact form\n\nMessage:\nthis is\r\na \r\ntest\r\nof \r\nthe contact form \r\ntesting to see\r\nif its working correctly\r\n1\r\n2\r\n3\n\n---\nThis message was sent via the contact form on 2025-07-16 14:02:47\nReply directly to: <EMAIL>\n\n[reply]\nCONTACT_EMAIL:<EMAIL>\n\nYou can view and reply to this message in your notification center:\nhttps://events.rowaneliterides.com/notification-center\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-16 14:05:06', NULL, '2025-07-16 14:02:47', '2025-07-16 14:05:06', '2025-07-16 14:05:06'),
(483, 3, NULL, 'email', NULL, NULL, '', '2025-07-16 14:48:04', 'New Message: Contact Form: ofdgkldfjklk', 'Hello Brian Correll,\n\nYou have received a message from System:\n\nSubject: Contact Form: ofdgkldfjklk\n\nNew contact form submission:\n\nName: fdgdfgfdgdfg\nEmail: <EMAIL>\nSubject: ofdgkldfjklk\n\nMessage:\nlklklklkl\n\n---\nThis message was sent via the contact form on 2025-07-16 14:48:04\nReply directly to: <EMAIL>\n\n[reply]\nCONTACT_EMAIL:<EMAIL>\n\nYou can view and reply to this message in your notification center:\nhttps://events.rowaneliterides.com/notification-center\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-16 14:50:10', NULL, '2025-07-16 14:48:04', '2025-07-16 14:50:10', '2025-07-16 14:50:10'),
(485, 3, NULL, 'email', NULL, NULL, '', '2025-07-16 14:55:54', 'New Message: Contact Form: kdjfgljfdklgjkldfjkl', 'Hello Brian Correll,\n\nYou have received a message from System:\n\nSubject: Contact Form: kdjfgljfdklgjkldfjkl\n\nNew contact form submission:\n\nName: gfdgdfgdfg\nEmail: <EMAIL>\nSubject: kdjfgljfdklgjkldfjkl\n\nMessage:\nkjkljkljkljkl\n\n---\nThis message was sent via the contact form on 2025-07-16 14:55:54\nReply directly to: <EMAIL>\n\n[reply]\nCONTACT_EMAIL:<EMAIL>\n\nYou can view and reply to this message in your notification center:\nhttps://events.rowaneliterides.com/notification-center\n\nThank you!', NULL, NULL, 'sent', 0, '2025-07-16 15:00:07', NULL, '2025-07-16 14:55:54', '2025-07-16 15:00:07', '2025-07-16 15:00:07'),
(488, 1, NULL, 'email', NULL, NULL, 'event_reminder', '2025-07-17 17:50:03', 'New Message: Test Email Message 2025-07-17 16:49:47', 'Hello System,\n\nYou have received a message from System:\n\nSubject: Test Email Message 2025-07-17 16:49:47\n\nThis is a test email message to verify the system is working.\n\nYou can view and reply to this message in your notification center:\nhttps://events.rowaneliterides.com/notification_center\n\nThank you!', NULL, NULL, 'failed', 3, '2025-07-17 17:50:05', 'Failed to send notification', '2025-07-17 16:49:47', '2025-07-17 17:50:05', NULL);
