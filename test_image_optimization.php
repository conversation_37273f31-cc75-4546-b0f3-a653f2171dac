<?php
/**
 * Test Image Optimization Settings
 * 
 * This script shows the current image optimization settings and demonstrates
 * the improvements made for event image compression.
 */

// Start session and include necessary files
session_start();

// Define constants
define('APPROOT', dirname(__FILE__));
require_once APPROOT . '/config/config.php';
require_once APPROOT . '/core/Database.php';
require_once APPROOT . '/models/SettingsModel.php';

echo "<h1>🖼️ Image Optimization Test</h1>";

// Check current settings
echo "<h2>📊 Current Image Settings</h2>";
try {
    $settingsModel = new SettingsModel();
    
    $settings = [
        'image_image_quality' => $settingsModel->getSetting('image_image_quality', 80),
        'image_optimize_images' => $settingsModel->getSetting('image_optimize_images', '0'),
        'image_resize_large_images' => $settingsModel->getSetting('image_resize_large_images', '0'),
        'image_max_width' => $settingsModel->getSetting('image_max_width', 1920),
        'image_max_height' => $settingsModel->getSetting('image_max_height', 1080),
        'image_max_upload_size' => $settingsModel->getSetting('image_max_upload_size', 5),
        'image_allowed_extensions' => $settingsModel->getSetting('image_allowed_extensions', 'jpg,jpeg,png,gif,webp')
    ];
    
    echo "<table border='1' cellpadding='10' style='border-collapse: collapse;'>";
    echo "<tr><th>Setting</th><th>Current Value</th><th>Event Override</th></tr>";
    
    echo "<tr><td>Image Quality</td><td>{$settings['image_image_quality']}%</td><td><strong>75%</strong> (better compression)</td></tr>";
    echo "<tr><td>Optimize Images</td><td>" . ($settings['image_optimize_images'] === '1' ? 'Enabled' : 'Disabled') . "</td><td><strong>Always Enabled</strong> for events</td></tr>";
    echo "<tr><td>Resize Large Images</td><td>" . ($settings['image_resize_large_images'] === '1' ? 'Enabled' : 'Disabled') . "</td><td><strong>Always Enabled</strong> for events</td></tr>";
    echo "<tr><td>Max Width</td><td>{$settings['image_max_width']}px</td><td><strong>800px</strong> (smaller for web display)</td></tr>";
    echo "<tr><td>Max Height</td><td>{$settings['image_max_height']}px</td><td><strong>600px</strong> (smaller for web display)</td></tr>";
    echo "<tr><td>Max Upload Size</td><td>{$settings['image_max_upload_size']}MB</td><td>Same</td></tr>";
    echo "<tr><td>Allowed Extensions</td><td>{$settings['image_allowed_extensions']}</td><td>Same + <strong>PNG→JPEG conversion</strong></td></tr>";
    
    echo "</table>";
    
} catch (Exception $e) {
    echo "<p>❌ Error loading settings: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h2>🚀 Event Image Optimizations Applied</h2>";
echo "<div style='background: #e8f5e8; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>✅ Automatic Optimizations for Events:</h3>";
echo "<ul>";
echo "<li><strong>Quality Reduction:</strong> 80% → 75% (5% reduction for better compression)</li>";
echo "<li><strong>Size Limits:</strong> 1920×1080 → 800×600 (much smaller for web display)</li>";
echo "<li><strong>Always Optimize:</strong> Compression enabled regardless of global settings</li>";
echo "<li><strong>Always Resize:</strong> Large images automatically resized regardless of global settings</li>";
echo "<li><strong>PNG→JPEG Conversion:</strong> PNG images without transparency converted to JPEG for better compression</li>";
echo "<li><strong>Smart Transparency:</strong> PNG images with transparency preserved as PNG</li>";
echo "</ul>";
echo "</div>";

echo "<h2>📈 Expected File Size Improvements</h2>";
echo "<div style='background: #fff3cd; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>🎯 Size Reduction Estimates:</h3>";
echo "<ul>";
echo "<li><strong>500×600 image:</strong> 700KB → ~150-250KB (65-75% reduction)</li>";
echo "<li><strong>Large uploads:</strong> Automatically resized to 800×600 max</li>";
echo "<li><strong>PNG files:</strong> Converted to JPEG for 50-80% size reduction</li>";
echo "<li><strong>Quality impact:</strong> Minimal visual difference at 75% quality</li>";
echo "</ul>";
echo "</div>";

echo "<h2>🔧 Technical Details</h2>";
echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
echo "<h3>How the Optimization Works:</h3>";
echo "<ol>";
echo "<li><strong>Upload Detection:</strong> System detects entity_type='event'</li>";
echo "<li><strong>Settings Override:</strong> Event-specific settings applied automatically</li>";
echo "<li><strong>Size Check:</strong> Images larger than 800×600 are resized</li>";
echo "<li><strong>Format Optimization:</strong> PNG without transparency → JPEG</li>";
echo "<li><strong>Quality Compression:</strong> JPEG saved at 75% quality</li>";
echo "<li><strong>File Cleanup:</strong> Old images deleted when new ones uploaded</li>";
echo "</ol>";
echo "</div>";

echo "<h2>🧪 Test Upload</h2>";
echo "<p>Upload an event image to see the optimizations in action:</p>";
echo "<p><a href='" . BASE_URL . "/calendar/editEvent/21' style='background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>Test Event Image Upload</a></p>";

echo "<h2>⚙️ Admin Settings</h2>";
echo "<p>To adjust global image settings:</p>";
echo "<p><a href='" . BASE_URL . "/admin/imageSettings' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px;'>Image Settings</a></p>";

echo "<h2>📋 Summary</h2>";
echo "<p>The image optimization improvements will:</p>";
echo "<ul>";
echo "<li>✅ Reduce event image file sizes by 65-75%</li>";
echo "<li>✅ Maintain good visual quality for web display</li>";
echo "<li>✅ Automatically resize large images to appropriate dimensions</li>";
echo "<li>✅ Convert PNG to JPEG when possible for better compression</li>";
echo "<li>✅ Apply optimizations only to events (other entity types unchanged)</li>";
echo "<li>✅ Work regardless of global optimization settings</li>";
echo "</ul>";

echo "<div style='background: #d1ecf1; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
echo "<h3>💡 Pro Tip:</h3>";
echo "<p>For best results, upload images in JPEG format when possible. The system will still optimize PNG files, but JPEG typically provides better compression for photos.</p>";
echo "</div>";
?>
