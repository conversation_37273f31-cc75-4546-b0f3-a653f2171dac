
-- --------------------------------------------------------

--
-- Table structure for table `judge_total_scores`
--
-- Creation: Jun 23, 2025 at 08:30 PM
--

CREATE TABLE `judge_total_scores` (
  `id` int(11) NOT NULL,
  `show_id` int(11) NOT NULL,
  `vehicle_id` int(11) NOT NULL,
  `registration_id` int(11) NOT NULL,
  `judge_id` int(11) NOT NULL,
  `judge_name` varchar(255) DEFAULT NULL,
  `raw_score` decimal(10,2) DEFAULT NULL,
  `weighted_score` decimal(10,2) DEFAULT NULL,
  `age_weighted_score` decimal(10,2) DEFAULT NULL,
  `normalized_score` decimal(10,2) DEFAULT NULL,
  `final_score` decimal(10,2) DEFAULT NULL,
  `age_weight` decimal(10,4) DEFAULT 1.0000,
  `weight_multiplier` decimal(10,2) DEFAULT 100.00,
  `formula_id` int(11) DEFAULT NULL,
  `formula_name` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- RELATIONSHIPS FOR TABLE `judge_total_scores`:
--

--
-- Dumping data for table `judge_total_scores`
--

INSERT INTO `judge_total_scores` (`id`, `show_id`, `vehicle_id`, `registration_id`, `judge_id`, `judge_name`, `raw_score`, `weighted_score`, `age_weighted_score`, `normalized_score`, `final_score`, `age_weight`, `weight_multiplier`, `formula_id`, `formula_name`, `created_at`, `updated_at`) VALUES
(1, 5, 2, 37, 3, 'Brian Correll', 0.00, 0.00, 0.00, 0.00, 0.00, 1.2000, 100.00, NULL, 'Default', '2025-06-06 11:40:21', '2025-06-09 17:22:21'),
(2, 5, 2, 37, 12, 'jsfsdjkfksd', 0.00, 0.00, 0.00, 0.00, 0.00, 1.2000, 100.00, NULL, 'Default', '2025-06-06 11:40:21', '2025-06-09 17:22:21'),
(13, 9, 4, 39, 3, 'Brian Correll', 64.00, 404.00, 808.00, 61.21, 61.21, 2.0000, 100.00, 1, 'Standard Formula', '2025-06-08 19:18:11', '2025-06-08 23:43:15'),
(14, 9, 4, 39, 88, 'Frank Johnson', 80.00, 526.00, 1052.00, 79.70, 79.70, 2.0000, 100.00, 1, 'Standard Formula', '2025-06-08 19:18:11', '2025-06-08 23:43:15'),
(15, 9, 5, 40, 88, 'Frank Johnson', 31.00, 126.00, 220.50, 19.09, 19.09, 1.7500, 100.00, 1, 'Standard Formula', '2025-06-08 19:18:11', '2025-06-08 23:43:15'),
(16, 9, 6, 41, 12, 'jsfsdjkfksd', 0.00, 0.00, 0.00, 0.00, 0.00, 1.5000, 100.00, 1, 'Standard Formula', '2025-06-08 19:18:11', '2025-06-08 23:43:15'),
(17, 9, 7, 42, 88, 'Frank Johnson', 13.00, 26.00, 26.00, 3.94, 3.94, 1.0000, 100.00, 1, 'Standard Formula', '2025-06-08 19:18:11', '2025-06-08 23:43:15'),
(18, 9, 7, 42, 12, 'jsfsdjkfksd', 0.00, 0.00, 0.00, 0.00, 0.00, 1.0000, 100.00, 1, 'Standard Formula', '2025-06-08 19:18:11', '2025-06-08 23:43:15'),
(19, 9, 8, 43, 88, 'Frank Johnson', 17.00, 109.00, 190.75, 16.52, 16.52, 1.7500, 100.00, 1, 'Standard Formula', '2025-06-08 19:18:11', '2025-06-08 23:43:15'),
(20, 9, 8, 43, 12, 'jsfsdjkfksd', 0.00, 0.00, 0.00, 0.00, 0.00, 1.7500, 100.00, 1, 'Standard Formula', '2025-06-08 19:18:11', '2025-06-08 23:43:15'),
(21, 9, 9, 44, 12, 'jsfsdjkfksd', 0.00, 0.00, 0.00, 0.00, 0.00, 1.5000, 100.00, 1, 'Standard Formula', '2025-06-08 19:18:11', '2025-06-08 23:43:15'),
(22, 9, 10, 45, 3, 'Brian Correll', 31.00, 123.00, 215.25, 18.64, 18.64, 1.7500, 100.00, 1, 'Standard Formula', '2025-06-08 19:18:11', '2025-06-08 23:43:15'),
(23, 9, 10, 45, 88, 'Frank Johnson', 85.00, 571.00, 999.25, 86.52, 86.52, 1.7500, 100.00, 1, 'Standard Formula', '2025-06-08 19:18:11', '2025-06-08 23:43:15'),
(24, 9, 11, 46, 3, 'Brian Correll', 72.00, 469.00, 820.75, 71.06, 71.06, 1.7500, 100.00, 1, 'Standard Formula', '2025-06-08 19:18:11', '2025-06-08 23:43:15'),
(25, 9, 12, 47, 3, 'Brian Correll', 73.00, 496.00, 992.00, 75.15, 75.15, 2.0000, 100.00, 1, 'Standard Formula', '2025-06-08 19:18:11', '2025-06-08 23:43:15'),
(26, 9, 13, 48, 88, 'Frank Johnson', 53.00, 378.00, 756.00, 57.27, 57.27, 2.0000, 100.00, 1, 'Standard Formula', '2025-06-08 19:18:11', '2025-06-08 23:43:15'),
(29, 9, 20, 59, 3, 'Brian Correll', 0.00, 0.00, 0.00, 0.00, 0.00, 1.7500, 100.00, NULL, 'Default', '2025-06-09 00:18:40', '2025-06-09 17:22:21'),
(30, 5, 2, 528, 2302, 'Demo Judge 2302', 979.36, 979.36, NULL, NULL, 10.20, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:05', '2025-07-09 19:15:05'),
(31, 5, 2, 528, 2303, 'Demo Judge 2303', 966.26, 966.26, NULL, NULL, 10.07, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:05', '2025-07-09 19:15:05'),
(32, 5, 2, 528, 2304, 'Demo Judge 2304', 977.17, 977.17, NULL, NULL, 10.18, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:05', '2025-07-09 19:15:05'),
(33, 5, 19, 529, 2302, 'Demo Judge 2302', 964.38, 964.38, NULL, NULL, 10.05, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:05', '2025-07-09 19:15:05'),
(34, 5, 19, 529, 2303, 'Demo Judge 2303', 1010.13, 1010.13, NULL, NULL, 10.52, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:05', '2025-07-09 19:15:05'),
(35, 5, 24, 530, 2302, 'Demo Judge 2302', 971.81, 971.81, NULL, NULL, 10.12, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:05', '2025-07-09 19:15:05'),
(36, 5, 24, 530, 2303, 'Demo Judge 2303', 1003.62, 1003.62, NULL, NULL, 10.45, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:05', '2025-07-09 19:15:05'),
(37, 5, 24, 530, 2304, 'Demo Judge 2304', 1000.51, 1000.51, NULL, NULL, 10.42, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:05', '2025-07-09 19:15:05'),
(38, 5, 105, 531, 2302, 'Demo Judge 2302', 1005.41, 1005.41, NULL, NULL, 10.47, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:06', '2025-07-09 19:15:06'),
(39, 5, 105, 531, 2303, 'Demo Judge 2303', 1001.23, 1001.23, NULL, NULL, 10.43, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:06', '2025-07-09 19:15:06'),
(40, 5, 106, 532, 2302, 'Demo Judge 2302', 977.57, 977.57, NULL, NULL, 10.18, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:06', '2025-07-09 19:15:06'),
(41, 5, 106, 532, 2303, 'Demo Judge 2303', 967.66, 967.66, NULL, NULL, 10.08, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:06', '2025-07-09 19:15:06'),
(42, 5, 106, 532, 2304, 'Demo Judge 2304', 998.49, 998.49, NULL, NULL, 10.40, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:06', '2025-07-09 19:15:06'),
(43, 5, 107, 533, 2302, 'Demo Judge 2302', 991.78, 991.78, NULL, NULL, 10.33, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:06', '2025-07-09 19:15:06'),
(44, 5, 107, 533, 2303, 'Demo Judge 2303', 970.28, 970.28, NULL, NULL, 10.11, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:06', '2025-07-09 19:15:06'),
(45, 5, 108, 534, 2302, 'Demo Judge 2302', 980.31, 980.31, NULL, NULL, 10.21, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:06', '2025-07-09 19:15:06'),
(46, 5, 108, 534, 2303, 'Demo Judge 2303', 969.15, 969.15, NULL, NULL, 10.10, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:06', '2025-07-09 19:15:06'),
(47, 5, 108, 534, 2304, 'Demo Judge 2304', 996.93, 996.93, NULL, NULL, 10.38, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:06', '2025-07-09 19:15:06'),
(48, 5, 109, 535, 2302, 'Demo Judge 2302', 967.69, 967.69, NULL, NULL, 10.08, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:06', '2025-07-09 19:15:06'),
(49, 5, 109, 535, 2303, 'Demo Judge 2303', 993.93, 993.93, NULL, NULL, 10.35, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:06', '2025-07-09 19:15:06'),
(50, 5, 110, 536, 2302, 'Demo Judge 2302', 988.87, 988.87, NULL, NULL, 10.30, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:06', '2025-07-09 19:15:06'),
(51, 5, 110, 536, 2303, 'Demo Judge 2303', 998.15, 998.15, NULL, NULL, 10.40, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:06', '2025-07-09 19:15:06'),
(52, 5, 110, 536, 2304, 'Demo Judge 2304', 972.94, 972.94, NULL, NULL, 10.13, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:06', '2025-07-09 19:15:06'),
(53, 5, 111, 537, 2302, 'Demo Judge 2302', 977.19, 977.19, NULL, NULL, 10.18, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:06', '2025-07-09 19:15:06'),
(54, 5, 111, 537, 2303, 'Demo Judge 2303', 967.68, 967.68, NULL, NULL, 10.08, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:06', '2025-07-09 19:15:06'),
(55, 5, 111, 537, 2304, 'Demo Judge 2304', 962.59, 962.59, NULL, NULL, 10.03, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:06', '2025-07-09 19:15:06'),
(56, 9, 2, 538, 2302, 'Demo Judge 2302', 998.62, 998.62, NULL, NULL, 10.40, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:06', '2025-07-09 19:15:06'),
(57, 9, 2, 538, 2303, 'Demo Judge 2303', 973.58, 973.58, NULL, NULL, 10.14, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:06', '2025-07-09 19:15:06'),
(58, 9, 2, 538, 2304, 'Demo Judge 2304', 974.28, 974.28, NULL, NULL, 10.15, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:06', '2025-07-09 19:15:06'),
(59, 9, 19, 539, 2302, 'Demo Judge 2302', 974.54, 974.54, NULL, NULL, 10.15, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:06', '2025-07-09 19:15:06'),
(60, 9, 19, 539, 2303, 'Demo Judge 2303', 960.22, 960.22, NULL, NULL, 10.00, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:06', '2025-07-09 19:15:06'),
(61, 9, 24, 540, 2302, 'Demo Judge 2302', 968.08, 968.08, NULL, NULL, 10.08, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:06', '2025-07-09 19:15:06'),
(62, 9, 24, 540, 2303, 'Demo Judge 2303', 999.77, 999.77, NULL, NULL, 10.41, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:06', '2025-07-09 19:15:06'),
(63, 9, 24, 540, 2304, 'Demo Judge 2304', 973.96, 973.96, NULL, NULL, 10.15, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:06', '2025-07-09 19:15:06'),
(64, 9, 105, 541, 2302, 'Demo Judge 2302', 967.81, 967.81, NULL, NULL, 10.08, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:06', '2025-07-09 19:15:06'),
(65, 9, 105, 541, 2303, 'Demo Judge 2303', 1008.72, 1008.72, NULL, NULL, 10.51, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:06', '2025-07-09 19:15:06'),
(66, 9, 105, 541, 2304, 'Demo Judge 2304', 968.91, 968.91, NULL, NULL, 10.09, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:06', '2025-07-09 19:15:06'),
(67, 9, 106, 542, 2302, 'Demo Judge 2302', 1001.72, 1001.72, NULL, NULL, 10.43, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:06', '2025-07-09 19:15:06'),
(68, 9, 106, 542, 2303, 'Demo Judge 2303', 988.57, 988.57, NULL, NULL, 10.30, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:06', '2025-07-09 19:15:06'),
(69, 9, 106, 542, 2304, 'Demo Judge 2304', 1006.40, 1006.40, NULL, NULL, 10.48, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:06', '2025-07-09 19:15:06'),
(70, 9, 107, 543, 2302, 'Demo Judge 2302', 977.00, 977.00, NULL, NULL, 10.18, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:06', '2025-07-09 19:15:06'),
(71, 9, 107, 543, 2303, 'Demo Judge 2303', 1038.97, 1038.97, NULL, NULL, 10.82, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:06', '2025-07-09 19:15:06'),
(72, 9, 108, 544, 2302, 'Demo Judge 2302', 1016.84, 1016.84, NULL, NULL, 10.59, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:06', '2025-07-09 19:15:06'),
(73, 9, 108, 544, 2303, 'Demo Judge 2303', 999.42, 999.42, NULL, NULL, 10.41, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:06', '2025-07-09 19:15:06'),
(74, 9, 108, 544, 2304, 'Demo Judge 2304', 1006.86, 1006.86, NULL, NULL, 10.49, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:06', '2025-07-09 19:15:06'),
(75, 9, 109, 545, 2302, 'Demo Judge 2302', 1047.18, 1047.18, NULL, NULL, 10.91, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:06', '2025-07-09 19:15:06'),
(76, 9, 109, 545, 2303, 'Demo Judge 2303', 972.44, 972.44, NULL, NULL, 10.13, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:06', '2025-07-09 19:15:06'),
(77, 9, 110, 546, 2302, 'Demo Judge 2302', 996.52, 996.52, NULL, NULL, 10.38, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(78, 9, 110, 546, 2303, 'Demo Judge 2303', 982.75, 982.75, NULL, NULL, 10.24, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(79, 9, 110, 546, 2304, 'Demo Judge 2304', 1000.94, 1000.94, NULL, NULL, 10.43, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(80, 9, 111, 547, 2302, 'Demo Judge 2302', 980.13, 980.13, NULL, NULL, 10.21, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(81, 9, 111, 547, 2303, 'Demo Judge 2303', 1008.39, 1008.39, NULL, NULL, 10.50, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(82, 9, 111, 547, 2304, 'Demo Judge 2304', 1023.70, 1023.70, NULL, NULL, 10.66, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(83, 14, 2, 548, 2302, 'Demo Judge 2302', 982.80, 982.80, NULL, NULL, 10.24, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(84, 14, 2, 548, 2303, 'Demo Judge 2303', 978.80, 978.80, NULL, NULL, 10.20, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(85, 14, 19, 549, 2302, 'Demo Judge 2302', 988.29, 988.29, NULL, NULL, 10.29, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(86, 14, 19, 549, 2303, 'Demo Judge 2303', 991.20, 991.20, NULL, NULL, 10.33, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(87, 14, 19, 549, 2304, 'Demo Judge 2304', 981.08, 981.08, NULL, NULL, 10.22, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(88, 14, 24, 550, 2302, 'Demo Judge 2302', 986.98, 986.98, NULL, NULL, 10.28, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(89, 14, 24, 550, 2303, 'Demo Judge 2303', 983.41, 983.41, NULL, NULL, 10.24, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(90, 14, 24, 550, 2304, 'Demo Judge 2304', 978.61, 978.61, NULL, NULL, 10.19, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(91, 14, 105, 551, 2302, 'Demo Judge 2302', 995.91, 995.91, NULL, NULL, 10.37, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(92, 14, 105, 551, 2303, 'Demo Judge 2303', 993.39, 993.39, NULL, NULL, 10.35, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(93, 14, 105, 551, 2304, 'Demo Judge 2304', 1007.30, 1007.30, NULL, NULL, 10.49, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(94, 14, 106, 552, 2302, 'Demo Judge 2302', 964.97, 964.97, NULL, NULL, 10.05, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(95, 14, 106, 552, 2303, 'Demo Judge 2303', 966.26, 966.26, NULL, NULL, 10.07, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(96, 14, 106, 552, 2304, 'Demo Judge 2304', 994.14, 994.14, NULL, NULL, 10.36, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(97, 14, 107, 553, 2302, 'Demo Judge 2302', 967.39, 967.39, NULL, NULL, 10.08, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(98, 14, 107, 553, 2303, 'Demo Judge 2303', 993.96, 993.96, NULL, NULL, 10.35, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(99, 14, 107, 553, 2304, 'Demo Judge 2304', 956.08, 956.08, NULL, NULL, 9.96, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(100, 14, 108, 554, 2302, 'Demo Judge 2302', 970.66, 970.66, NULL, NULL, 10.11, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(101, 14, 108, 554, 2303, 'Demo Judge 2303', 962.94, 962.94, NULL, NULL, 10.03, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(102, 14, 108, 554, 2304, 'Demo Judge 2304', 982.12, 982.12, NULL, NULL, 10.23, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(103, 14, 109, 555, 2302, 'Demo Judge 2302', 994.62, 994.62, NULL, NULL, 10.36, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(104, 14, 109, 555, 2303, 'Demo Judge 2303', 1006.20, 1006.20, NULL, NULL, 10.48, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(105, 14, 109, 555, 2304, 'Demo Judge 2304', 1035.89, 1035.89, NULL, NULL, 10.79, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(106, 14, 110, 556, 2302, 'Demo Judge 2302', 1006.38, 1006.38, NULL, NULL, 10.48, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(107, 14, 110, 556, 2303, 'Demo Judge 2303', 985.94, 985.94, NULL, NULL, 10.27, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(108, 14, 111, 557, 2302, 'Demo Judge 2302', 988.70, 988.70, NULL, NULL, 10.30, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(109, 14, 111, 557, 2303, 'Demo Judge 2303', 994.71, 994.71, NULL, NULL, 10.36, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(110, 14, 112, 558, 2302, 'Demo Judge 2302', 981.46, 981.46, NULL, NULL, 10.22, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(111, 14, 112, 558, 2303, 'Demo Judge 2303', 989.07, 989.07, NULL, NULL, 10.30, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(112, 55, 2, 559, 2302, 'Demo Judge 2302', 86.04, 86.04, NULL, NULL, 14.34, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(113, 55, 2, 559, 2303, 'Demo Judge 2303', 81.68, 81.68, NULL, NULL, 13.61, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(114, 55, 19, 560, 2302, 'Demo Judge 2302', 86.87, 86.87, NULL, NULL, 14.48, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(115, 55, 19, 560, 2303, 'Demo Judge 2303', 88.54, 88.54, NULL, NULL, 14.76, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(116, 55, 24, 561, 2302, 'Demo Judge 2302', 83.11, 83.11, NULL, NULL, 13.85, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(117, 55, 24, 561, 2303, 'Demo Judge 2303', 82.57, 82.57, NULL, NULL, 13.76, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(118, 55, 24, 561, 2304, 'Demo Judge 2304', 84.45, 84.45, NULL, NULL, 14.08, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(119, 55, 105, 562, 2302, 'Demo Judge 2302', 85.98, 85.98, NULL, NULL, 14.33, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(120, 55, 105, 562, 2303, 'Demo Judge 2303', 86.07, 86.07, NULL, NULL, 14.35, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(121, 55, 106, 563, 2302, 'Demo Judge 2302', 81.65, 81.65, NULL, NULL, 13.61, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(122, 55, 106, 563, 2303, 'Demo Judge 2303', 81.86, 81.86, NULL, NULL, 13.64, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(123, 55, 106, 563, 2304, 'Demo Judge 2304', 88.93, 88.93, NULL, NULL, 14.82, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(124, 55, 107, 564, 2302, 'Demo Judge 2302', 85.22, 85.22, NULL, NULL, 14.20, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(125, 55, 107, 564, 2303, 'Demo Judge 2303', 83.28, 83.28, NULL, NULL, 13.88, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(126, 55, 107, 564, 2304, 'Demo Judge 2304', 85.90, 85.90, NULL, NULL, 14.32, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(127, 55, 108, 565, 2302, 'Demo Judge 2302', 88.51, 88.51, NULL, NULL, 14.75, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(128, 55, 108, 565, 2303, 'Demo Judge 2303', 83.03, 83.03, NULL, NULL, 13.84, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(129, 55, 109, 566, 2302, 'Demo Judge 2302', 82.98, 82.98, NULL, NULL, 13.83, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(130, 55, 109, 566, 2303, 'Demo Judge 2303', 76.73, 76.73, NULL, NULL, 12.79, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(131, 55, 110, 567, 2302, 'Demo Judge 2302', 87.90, 87.90, NULL, NULL, 14.65, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(132, 55, 110, 567, 2303, 'Demo Judge 2303', 95.16, 95.16, NULL, NULL, 15.86, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(133, 55, 110, 567, 2304, 'Demo Judge 2304', 85.29, 85.29, NULL, NULL, 14.22, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(134, 55, 111, 568, 2302, 'Demo Judge 2302', 79.62, 79.62, NULL, NULL, 13.27, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(135, 55, 111, 568, 2303, 'Demo Judge 2303', 84.54, 84.54, NULL, NULL, 14.09, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(136, 56, 2, 569, 2302, 'Demo Judge 2302', 74.17, 74.17, NULL, NULL, 12.36, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(137, 56, 2, 569, 2303, 'Demo Judge 2303', 80.86, 80.86, NULL, NULL, 13.48, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(138, 56, 2, 569, 2304, 'Demo Judge 2304', 80.95, 80.95, NULL, NULL, 13.49, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(139, 56, 19, 570, 2302, 'Demo Judge 2302', 79.93, 79.93, NULL, NULL, 13.32, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(140, 56, 19, 570, 2303, 'Demo Judge 2303', 81.02, 81.02, NULL, NULL, 13.50, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(141, 56, 24, 571, 2302, 'Demo Judge 2302', 75.86, 75.86, NULL, NULL, 12.64, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(142, 56, 24, 571, 2303, 'Demo Judge 2303', 86.50, 86.50, NULL, NULL, 14.42, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(143, 56, 105, 572, 2302, 'Demo Judge 2302', 85.57, 85.57, NULL, NULL, 14.26, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(144, 56, 105, 572, 2303, 'Demo Judge 2303', 79.82, 79.82, NULL, NULL, 13.30, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(145, 56, 106, 573, 2302, 'Demo Judge 2302', 90.84, 90.84, NULL, NULL, 15.14, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(146, 56, 106, 573, 2303, 'Demo Judge 2303', 89.40, 89.40, NULL, NULL, 14.90, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(147, 56, 107, 574, 2302, 'Demo Judge 2302', 81.37, 81.37, NULL, NULL, 13.56, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(148, 56, 107, 574, 2303, 'Demo Judge 2303', 84.40, 84.40, NULL, NULL, 14.07, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(149, 56, 107, 574, 2304, 'Demo Judge 2304', 78.79, 78.79, NULL, NULL, 13.13, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(150, 56, 108, 575, 2302, 'Demo Judge 2302', 80.99, 80.99, NULL, NULL, 13.50, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(151, 56, 108, 575, 2303, 'Demo Judge 2303', 79.16, 79.16, NULL, NULL, 13.19, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(152, 56, 108, 575, 2304, 'Demo Judge 2304', 90.08, 90.08, NULL, NULL, 15.01, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(153, 56, 109, 576, 2302, 'Demo Judge 2302', 84.04, 84.04, NULL, NULL, 14.01, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(154, 56, 109, 576, 2303, 'Demo Judge 2303', 82.94, 82.94, NULL, NULL, 13.82, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(155, 56, 109, 576, 2304, 'Demo Judge 2304', 84.12, 84.12, NULL, NULL, 14.02, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(156, 56, 110, 577, 2302, 'Demo Judge 2302', 82.16, 82.16, NULL, NULL, 13.69, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(157, 56, 110, 577, 2303, 'Demo Judge 2303', 87.39, 87.39, NULL, NULL, 14.57, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(158, 56, 111, 578, 2302, 'Demo Judge 2302', 93.43, 93.43, NULL, NULL, 15.57, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(159, 56, 111, 578, 2303, 'Demo Judge 2303', 88.10, 88.10, NULL, NULL, 14.68, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(160, 56, 112, 579, 2302, 'Demo Judge 2302', 84.24, 84.24, NULL, NULL, 14.04, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(161, 56, 112, 579, 2303, 'Demo Judge 2303', 82.41, 82.41, NULL, NULL, 13.74, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(162, 57, 2, 580, 2302, 'Demo Judge 2302', 83.13, 83.13, NULL, NULL, 13.86, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(163, 57, 2, 580, 2303, 'Demo Judge 2303', 87.55, 87.55, NULL, NULL, 14.59, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(164, 57, 2, 580, 2304, 'Demo Judge 2304', 87.10, 87.10, NULL, NULL, 14.52, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(165, 57, 19, 581, 2302, 'Demo Judge 2302', 82.20, 82.20, NULL, NULL, 13.70, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(166, 57, 19, 581, 2303, 'Demo Judge 2303', 84.34, 84.34, NULL, NULL, 14.06, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(167, 57, 19, 581, 2304, 'Demo Judge 2304', 80.22, 80.22, NULL, NULL, 13.37, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(168, 57, 24, 582, 2302, 'Demo Judge 2302', 80.78, 80.78, NULL, NULL, 13.46, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:07', '2025-07-09 19:15:07'),
(169, 57, 24, 582, 2303, 'Demo Judge 2303', 85.98, 85.98, NULL, NULL, 14.33, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(170, 57, 105, 583, 2302, 'Demo Judge 2302', 84.08, 84.08, NULL, NULL, 14.01, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(171, 57, 105, 583, 2303, 'Demo Judge 2303', 86.91, 86.91, NULL, NULL, 14.49, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(172, 57, 106, 584, 2302, 'Demo Judge 2302', 78.20, 78.20, NULL, NULL, 13.03, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(173, 57, 106, 584, 2303, 'Demo Judge 2303', 88.36, 88.36, NULL, NULL, 14.73, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(174, 57, 106, 584, 2304, 'Demo Judge 2304', 82.26, 82.26, NULL, NULL, 13.71, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(175, 57, 107, 585, 2302, 'Demo Judge 2302', 81.63, 81.63, NULL, NULL, 13.61, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(176, 57, 107, 585, 2303, 'Demo Judge 2303', 88.74, 88.74, NULL, NULL, 14.79, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(177, 57, 108, 586, 2302, 'Demo Judge 2302', 82.55, 82.55, NULL, NULL, 13.76, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(178, 57, 108, 586, 2303, 'Demo Judge 2303', 85.37, 85.37, NULL, NULL, 14.23, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(179, 57, 108, 586, 2304, 'Demo Judge 2304', 83.47, 83.47, NULL, NULL, 13.91, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(180, 57, 109, 587, 2302, 'Demo Judge 2302', 89.35, 89.35, NULL, NULL, 14.89, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(181, 57, 109, 587, 2303, 'Demo Judge 2303', 89.52, 89.52, NULL, NULL, 14.92, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(182, 57, 109, 587, 2304, 'Demo Judge 2304', 74.27, 74.27, NULL, NULL, 12.38, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(183, 58, 2, 588, 2302, 'Demo Judge 2302', 87.87, 87.87, NULL, NULL, 14.65, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(184, 58, 2, 588, 2303, 'Demo Judge 2303', 92.48, 92.48, NULL, NULL, 15.41, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(185, 58, 19, 589, 2302, 'Demo Judge 2302', 79.47, 79.47, NULL, NULL, 13.25, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(186, 58, 19, 589, 2303, 'Demo Judge 2303', 76.97, 76.97, NULL, NULL, 12.83, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(187, 58, 24, 590, 2302, 'Demo Judge 2302', 84.43, 84.43, NULL, NULL, 14.07, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(188, 58, 24, 590, 2303, 'Demo Judge 2303', 86.77, 86.77, NULL, NULL, 14.46, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(189, 58, 105, 591, 2302, 'Demo Judge 2302', 87.31, 87.31, NULL, NULL, 14.55, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(190, 58, 105, 591, 2303, 'Demo Judge 2303', 80.31, 80.31, NULL, NULL, 13.39, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(191, 58, 106, 592, 2302, 'Demo Judge 2302', 88.11, 88.11, NULL, NULL, 14.69, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(192, 58, 106, 592, 2303, 'Demo Judge 2303', 87.49, 87.49, NULL, NULL, 14.58, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(193, 58, 107, 593, 2302, 'Demo Judge 2302', 87.49, 87.49, NULL, NULL, 14.58, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(194, 58, 107, 593, 2303, 'Demo Judge 2303', 84.45, 84.45, NULL, NULL, 14.08, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(195, 58, 107, 593, 2304, 'Demo Judge 2304', 84.28, 84.28, NULL, NULL, 14.05, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(196, 58, 108, 594, 2302, 'Demo Judge 2302', 86.21, 86.21, NULL, NULL, 14.37, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(197, 58, 108, 594, 2303, 'Demo Judge 2303', 83.61, 83.61, NULL, NULL, 13.94, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(198, 58, 109, 595, 2302, 'Demo Judge 2302', 84.78, 84.78, NULL, NULL, 14.13, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(199, 58, 109, 595, 2303, 'Demo Judge 2303', 87.66, 87.66, NULL, NULL, 14.61, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(200, 58, 109, 595, 2304, 'Demo Judge 2304', 83.54, 83.54, NULL, NULL, 13.92, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(201, 58, 110, 596, 2302, 'Demo Judge 2302', 88.38, 88.38, NULL, NULL, 14.73, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(202, 58, 110, 596, 2303, 'Demo Judge 2303', 81.71, 81.71, NULL, NULL, 13.62, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(203, 58, 111, 597, 2302, 'Demo Judge 2302', 92.39, 92.39, NULL, NULL, 15.40, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(204, 58, 111, 597, 2303, 'Demo Judge 2303', 83.84, 83.84, NULL, NULL, 13.97, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(205, 58, 111, 597, 2304, 'Demo Judge 2304', 81.22, 81.22, NULL, NULL, 13.54, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(206, 59, 2, 598, 2302, 'Demo Judge 2302', 81.55, 81.55, NULL, NULL, 13.59, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(207, 59, 2, 598, 2303, 'Demo Judge 2303', 79.03, 79.03, NULL, NULL, 13.17, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(208, 59, 19, 599, 2302, 'Demo Judge 2302', 83.59, 83.59, NULL, NULL, 13.93, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(209, 59, 19, 599, 2303, 'Demo Judge 2303', 87.61, 87.61, NULL, NULL, 14.60, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(210, 59, 19, 599, 2304, 'Demo Judge 2304', 78.17, 78.17, NULL, NULL, 13.03, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(211, 59, 24, 600, 2302, 'Demo Judge 2302', 83.13, 83.13, NULL, NULL, 13.86, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(212, 59, 24, 600, 2303, 'Demo Judge 2303', 82.94, 82.94, NULL, NULL, 13.82, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(213, 59, 24, 600, 2304, 'Demo Judge 2304', 88.85, 88.85, NULL, NULL, 14.81, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(214, 59, 105, 601, 2302, 'Demo Judge 2302', 80.69, 80.69, NULL, NULL, 13.45, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(215, 59, 105, 601, 2303, 'Demo Judge 2303', 89.52, 89.52, NULL, NULL, 14.92, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(216, 59, 106, 602, 2302, 'Demo Judge 2302', 90.07, 90.07, NULL, NULL, 15.01, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(217, 59, 106, 602, 2303, 'Demo Judge 2303', 87.26, 87.26, NULL, NULL, 14.54, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(218, 59, 106, 602, 2304, 'Demo Judge 2304', 81.44, 81.44, NULL, NULL, 13.57, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(219, 59, 107, 603, 2302, 'Demo Judge 2302', 87.23, 87.23, NULL, NULL, 14.54, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(220, 59, 107, 603, 2303, 'Demo Judge 2303', 82.66, 82.66, NULL, NULL, 13.78, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(221, 59, 107, 603, 2304, 'Demo Judge 2304', 83.07, 83.07, NULL, NULL, 13.85, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(222, 59, 108, 604, 2302, 'Demo Judge 2302', 81.54, 81.54, NULL, NULL, 13.59, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(223, 59, 108, 604, 2303, 'Demo Judge 2303', 90.47, 90.47, NULL, NULL, 15.08, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(224, 59, 109, 605, 2302, 'Demo Judge 2302', 87.78, 87.78, NULL, NULL, 14.63, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(225, 59, 109, 605, 2303, 'Demo Judge 2303', 90.47, 90.47, NULL, NULL, 15.08, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(226, 59, 110, 606, 2302, 'Demo Judge 2302', 87.08, 87.08, NULL, NULL, 14.51, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(227, 59, 110, 606, 2303, 'Demo Judge 2303', 83.66, 83.66, NULL, NULL, 13.94, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(228, 59, 110, 606, 2304, 'Demo Judge 2304', 82.02, 82.02, NULL, NULL, 13.67, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(229, 59, 111, 607, 2302, 'Demo Judge 2302', 83.09, 83.09, NULL, NULL, 13.85, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(230, 59, 111, 607, 2303, 'Demo Judge 2303', 77.26, 77.26, NULL, NULL, 12.88, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(231, 59, 111, 607, 2304, 'Demo Judge 2304', 86.92, 86.92, NULL, NULL, 14.49, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(232, 59, 112, 608, 2302, 'Demo Judge 2302', 87.48, 87.48, NULL, NULL, 14.58, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(233, 59, 112, 608, 2303, 'Demo Judge 2303', 79.36, 79.36, NULL, NULL, 13.23, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(234, 59, 113, 609, 2302, 'Demo Judge 2302', 79.86, 79.86, NULL, NULL, 13.31, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(235, 59, 113, 609, 2303, 'Demo Judge 2303', 84.00, 84.00, NULL, NULL, 14.00, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(236, 59, 113, 609, 2304, 'Demo Judge 2304', 86.22, 86.22, NULL, NULL, 14.37, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(237, 60, 2, 610, 2302, 'Demo Judge 2302', 83.06, 83.06, NULL, NULL, 13.84, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(238, 60, 2, 610, 2303, 'Demo Judge 2303', 86.16, 86.16, NULL, NULL, 14.36, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(239, 60, 2, 610, 2304, 'Demo Judge 2304', 90.63, 90.63, NULL, NULL, 15.11, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(240, 60, 19, 611, 2302, 'Demo Judge 2302', 86.14, 86.14, NULL, NULL, 14.36, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(241, 60, 19, 611, 2303, 'Demo Judge 2303', 90.46, 90.46, NULL, NULL, 15.08, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(242, 60, 24, 612, 2302, 'Demo Judge 2302', 88.32, 88.32, NULL, NULL, 14.72, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(243, 60, 24, 612, 2303, 'Demo Judge 2303', 83.67, 83.67, NULL, NULL, 13.95, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(244, 60, 24, 612, 2304, 'Demo Judge 2304', 82.25, 82.25, NULL, NULL, 13.71, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(245, 60, 105, 613, 2302, 'Demo Judge 2302', 81.12, 81.12, NULL, NULL, 13.52, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(246, 60, 105, 613, 2303, 'Demo Judge 2303', 78.04, 78.04, NULL, NULL, 13.01, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(247, 60, 106, 614, 2302, 'Demo Judge 2302', 83.95, 83.95, NULL, NULL, 13.99, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(248, 60, 106, 614, 2303, 'Demo Judge 2303', 91.99, 91.99, NULL, NULL, 15.33, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(249, 60, 106, 614, 2304, 'Demo Judge 2304', 86.11, 86.11, NULL, NULL, 14.35, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(250, 60, 107, 615, 2302, 'Demo Judge 2302', 82.59, 82.59, NULL, NULL, 13.77, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(251, 60, 107, 615, 2303, 'Demo Judge 2303', 90.10, 90.10, NULL, NULL, 15.02, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(252, 60, 108, 616, 2302, 'Demo Judge 2302', 83.68, 83.68, NULL, NULL, 13.95, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(253, 60, 108, 616, 2303, 'Demo Judge 2303', 86.19, 86.19, NULL, NULL, 14.37, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(254, 60, 109, 617, 2302, 'Demo Judge 2302', 89.54, 89.54, NULL, NULL, 14.92, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(255, 60, 109, 617, 2303, 'Demo Judge 2303', 84.31, 84.31, NULL, NULL, 14.05, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(256, 60, 109, 617, 2304, 'Demo Judge 2304', 83.75, 83.75, NULL, NULL, 13.96, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(257, 60, 110, 618, 2302, 'Demo Judge 2302', 87.27, 87.27, NULL, NULL, 14.55, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(258, 60, 110, 618, 2303, 'Demo Judge 2303', 81.30, 81.30, NULL, NULL, 13.55, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(259, 60, 111, 619, 2302, 'Demo Judge 2302', 87.03, 87.03, NULL, NULL, 14.51, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(260, 60, 111, 619, 2303, 'Demo Judge 2303', 87.41, 87.41, NULL, NULL, 14.57, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(261, 60, 111, 619, 2304, 'Demo Judge 2304', 78.21, 78.21, NULL, NULL, 13.04, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(262, 61, 2, 620, 2302, 'Demo Judge 2302', 84.17, 84.17, NULL, NULL, 14.03, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(263, 61, 2, 620, 2303, 'Demo Judge 2303', 85.05, 85.05, NULL, NULL, 14.18, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(264, 61, 19, 621, 2302, 'Demo Judge 2302', 81.71, 81.71, NULL, NULL, 13.62, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(265, 61, 19, 621, 2303, 'Demo Judge 2303', 90.61, 90.61, NULL, NULL, 15.10, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(266, 61, 19, 621, 2304, 'Demo Judge 2304', 83.86, 83.86, NULL, NULL, 13.98, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(267, 61, 24, 622, 2302, 'Demo Judge 2302', 84.05, 84.05, NULL, NULL, 14.01, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(268, 61, 24, 622, 2303, 'Demo Judge 2303', 90.84, 90.84, NULL, NULL, 15.14, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(269, 61, 105, 623, 2302, 'Demo Judge 2302', 83.56, 83.56, NULL, NULL, 13.93, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(270, 61, 105, 623, 2303, 'Demo Judge 2303', 91.48, 91.48, NULL, NULL, 15.25, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(271, 61, 105, 623, 2304, 'Demo Judge 2304', 81.73, 81.73, NULL, NULL, 13.62, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(272, 61, 106, 624, 2302, 'Demo Judge 2302', 87.69, 87.69, NULL, NULL, 14.62, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(273, 61, 106, 624, 2303, 'Demo Judge 2303', 83.96, 83.96, NULL, NULL, 13.99, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(274, 61, 107, 625, 2302, 'Demo Judge 2302', 87.47, 87.47, NULL, NULL, 14.58, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(275, 61, 107, 625, 2303, 'Demo Judge 2303', 82.75, 82.75, NULL, NULL, 13.79, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(276, 61, 107, 625, 2304, 'Demo Judge 2304', 87.25, 87.25, NULL, NULL, 14.54, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(277, 61, 108, 626, 2302, 'Demo Judge 2302', 83.20, 83.20, NULL, NULL, 13.87, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(278, 61, 108, 626, 2303, 'Demo Judge 2303', 90.23, 90.23, NULL, NULL, 15.04, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(279, 61, 108, 626, 2304, 'Demo Judge 2304', 86.50, 86.50, NULL, NULL, 14.42, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(280, 61, 109, 627, 2302, 'Demo Judge 2302', 88.93, 88.93, NULL, NULL, 14.82, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(281, 61, 109, 627, 2303, 'Demo Judge 2303', 79.23, 79.23, NULL, NULL, 13.21, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(282, 61, 110, 628, 2302, 'Demo Judge 2302', 87.82, 87.82, NULL, NULL, 14.64, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(283, 61, 110, 628, 2303, 'Demo Judge 2303', 85.20, 85.20, NULL, NULL, 14.20, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(284, 61, 111, 629, 2302, 'Demo Judge 2302', 86.81, 86.81, NULL, NULL, 14.47, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(285, 61, 111, 629, 2303, 'Demo Judge 2303', 78.84, 78.84, NULL, NULL, 13.14, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(286, 61, 112, 630, 2302, 'Demo Judge 2302', 84.46, 84.46, NULL, NULL, 14.08, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(287, 61, 112, 630, 2303, 'Demo Judge 2303', 84.38, 84.38, NULL, NULL, 14.06, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(288, 61, 112, 630, 2304, 'Demo Judge 2304', 87.07, 87.07, NULL, NULL, 14.51, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(289, 62, 2, 631, 2302, 'Demo Judge 2302', 87.53, 87.53, NULL, NULL, 14.59, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(290, 62, 2, 631, 2303, 'Demo Judge 2303', 81.36, 81.36, NULL, NULL, 13.56, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(291, 62, 2, 631, 2304, 'Demo Judge 2304', 84.89, 84.89, NULL, NULL, 14.15, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(292, 62, 19, 632, 2302, 'Demo Judge 2302', 83.96, 83.96, NULL, NULL, 13.99, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(293, 62, 19, 632, 2303, 'Demo Judge 2303', 86.46, 86.46, NULL, NULL, 14.41, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(294, 62, 24, 633, 2302, 'Demo Judge 2302', 85.71, 85.71, NULL, NULL, 14.29, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(295, 62, 24, 633, 2303, 'Demo Judge 2303', 82.69, 82.69, NULL, NULL, 13.78, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(296, 62, 105, 634, 2302, 'Demo Judge 2302', 85.56, 85.56, NULL, NULL, 14.26, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(297, 62, 105, 634, 2303, 'Demo Judge 2303', 88.68, 88.68, NULL, NULL, 14.78, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(298, 62, 105, 634, 2304, 'Demo Judge 2304', 81.38, 81.38, NULL, NULL, 13.56, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(299, 62, 106, 635, 2302, 'Demo Judge 2302', 84.99, 84.99, NULL, NULL, 14.17, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(300, 62, 106, 635, 2303, 'Demo Judge 2303', 85.76, 85.76, NULL, NULL, 14.29, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(301, 62, 107, 636, 2302, 'Demo Judge 2302', 89.21, 89.21, NULL, NULL, 14.87, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(302, 62, 107, 636, 2303, 'Demo Judge 2303', 80.12, 80.12, NULL, NULL, 13.35, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(303, 62, 107, 636, 2304, 'Demo Judge 2304', 89.27, 89.27, NULL, NULL, 14.88, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(304, 62, 108, 637, 2302, 'Demo Judge 2302', 84.96, 84.96, NULL, NULL, 14.16, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(305, 62, 108, 637, 2303, 'Demo Judge 2303', 82.83, 82.83, NULL, NULL, 13.81, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(306, 62, 108, 637, 2304, 'Demo Judge 2304', 82.52, 82.52, NULL, NULL, 13.75, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(307, 62, 109, 638, 2302, 'Demo Judge 2302', 83.69, 83.69, NULL, NULL, 13.95, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(308, 62, 109, 638, 2303, 'Demo Judge 2303', 81.64, 81.64, NULL, NULL, 13.61, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(309, 62, 109, 638, 2304, 'Demo Judge 2304', 84.35, 84.35, NULL, NULL, 14.06, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(310, 62, 110, 639, 2302, 'Demo Judge 2302', 88.03, 88.03, NULL, NULL, 14.67, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(311, 62, 110, 639, 2303, 'Demo Judge 2303', 85.98, 85.98, NULL, NULL, 14.33, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(312, 62, 110, 639, 2304, 'Demo Judge 2304', 79.21, 79.21, NULL, NULL, 13.20, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(313, 62, 111, 640, 2302, 'Demo Judge 2302', 88.91, 88.91, NULL, NULL, 14.82, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(314, 62, 111, 640, 2303, 'Demo Judge 2303', 75.15, 75.15, NULL, NULL, 12.53, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(315, 62, 112, 641, 2302, 'Demo Judge 2302', 87.21, 87.21, NULL, NULL, 14.54, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(316, 62, 112, 641, 2303, 'Demo Judge 2303', 92.41, 92.41, NULL, NULL, 15.40, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(317, 62, 112, 641, 2304, 'Demo Judge 2304', 82.23, 82.23, NULL, NULL, 13.71, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(318, 63, 2, 642, 2302, 'Demo Judge 2302', 84.31, 84.31, NULL, NULL, 14.05, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(319, 63, 2, 642, 2303, 'Demo Judge 2303', 85.77, 85.77, NULL, NULL, 14.30, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(320, 63, 19, 643, 2302, 'Demo Judge 2302', 83.64, 83.64, NULL, NULL, 13.94, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(321, 63, 19, 643, 2303, 'Demo Judge 2303', 83.06, 83.06, NULL, NULL, 13.84, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(322, 63, 24, 644, 2302, 'Demo Judge 2302', 82.76, 82.76, NULL, NULL, 13.79, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(323, 63, 24, 644, 2303, 'Demo Judge 2303', 89.91, 89.91, NULL, NULL, 14.99, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(324, 63, 105, 645, 2302, 'Demo Judge 2302', 84.79, 84.79, NULL, NULL, 14.13, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(325, 63, 105, 645, 2303, 'Demo Judge 2303', 82.18, 82.18, NULL, NULL, 13.70, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(326, 63, 105, 645, 2304, 'Demo Judge 2304', 89.05, 89.05, NULL, NULL, 14.84, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(327, 63, 106, 646, 2302, 'Demo Judge 2302', 91.56, 91.56, NULL, NULL, 15.26, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(328, 63, 106, 646, 2303, 'Demo Judge 2303', 80.85, 80.85, NULL, NULL, 13.48, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(329, 63, 107, 647, 2302, 'Demo Judge 2302', 85.75, 85.75, NULL, NULL, 14.29, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(330, 63, 107, 647, 2303, 'Demo Judge 2303', 82.97, 82.97, NULL, NULL, 13.83, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(331, 63, 108, 648, 2302, 'Demo Judge 2302', 87.10, 87.10, NULL, NULL, 14.52, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(332, 63, 108, 648, 2303, 'Demo Judge 2303', 89.97, 89.97, NULL, NULL, 15.00, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(333, 63, 109, 649, 2302, 'Demo Judge 2302', 77.58, 77.58, NULL, NULL, 12.93, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(334, 63, 109, 649, 2303, 'Demo Judge 2303', 85.08, 85.08, NULL, NULL, 14.18, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(335, 63, 110, 650, 2302, 'Demo Judge 2302', 93.90, 93.90, NULL, NULL, 15.65, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(336, 63, 110, 650, 2303, 'Demo Judge 2303', 77.96, 77.96, NULL, NULL, 12.99, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(337, 63, 110, 650, 2304, 'Demo Judge 2304', 84.89, 84.89, NULL, NULL, 14.15, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(338, 64, 2, 651, 2302, 'Demo Judge 2302', 89.24, 89.24, NULL, NULL, 14.87, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(339, 64, 2, 651, 2303, 'Demo Judge 2303', 84.91, 84.91, NULL, NULL, 14.15, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(340, 64, 19, 652, 2302, 'Demo Judge 2302', 78.37, 78.37, NULL, NULL, 13.06, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(341, 64, 19, 652, 2303, 'Demo Judge 2303', 88.94, 88.94, NULL, NULL, 14.82, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08');
INSERT INTO `judge_total_scores` (`id`, `show_id`, `vehicle_id`, `registration_id`, `judge_id`, `judge_name`, `raw_score`, `weighted_score`, `age_weighted_score`, `normalized_score`, `final_score`, `age_weight`, `weight_multiplier`, `formula_id`, `formula_name`, `created_at`, `updated_at`) VALUES
(342, 64, 24, 653, 2302, 'Demo Judge 2302', 89.97, 89.97, NULL, NULL, 15.00, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(343, 64, 24, 653, 2303, 'Demo Judge 2303', 80.33, 80.33, NULL, NULL, 13.39, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(344, 64, 105, 654, 2302, 'Demo Judge 2302', 85.20, 85.20, NULL, NULL, 14.20, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(345, 64, 105, 654, 2303, 'Demo Judge 2303', 82.26, 82.26, NULL, NULL, 13.71, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(346, 64, 105, 654, 2304, 'Demo Judge 2304', 84.72, 84.72, NULL, NULL, 14.12, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(347, 64, 106, 655, 2302, 'Demo Judge 2302', 84.27, 84.27, NULL, NULL, 14.05, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(348, 64, 106, 655, 2303, 'Demo Judge 2303', 79.59, 79.59, NULL, NULL, 13.27, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(349, 64, 107, 656, 2302, 'Demo Judge 2302', 85.65, 85.65, NULL, NULL, 14.28, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(350, 64, 107, 656, 2303, 'Demo Judge 2303', 84.84, 84.84, NULL, NULL, 14.14, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(351, 64, 107, 656, 2304, 'Demo Judge 2304', 84.75, 84.75, NULL, NULL, 14.13, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(352, 64, 108, 657, 2302, 'Demo Judge 2302', 78.13, 78.13, NULL, NULL, 13.02, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(353, 64, 108, 657, 2303, 'Demo Judge 2303', 77.55, 77.55, NULL, NULL, 12.93, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(354, 64, 108, 657, 2304, 'Demo Judge 2304', 93.74, 93.74, NULL, NULL, 15.62, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(355, 64, 109, 658, 2302, 'Demo Judge 2302', 92.71, 92.71, NULL, NULL, 15.45, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(356, 64, 109, 658, 2303, 'Demo Judge 2303', 84.09, 84.09, NULL, NULL, 14.02, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(357, 64, 109, 658, 2304, 'Demo Judge 2304', 78.37, 78.37, NULL, NULL, 13.06, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(358, 64, 110, 659, 2302, 'Demo Judge 2302', 84.48, 84.48, NULL, NULL, 14.08, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(359, 64, 110, 659, 2303, 'Demo Judge 2303', 79.57, 79.57, NULL, NULL, 13.26, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(360, 64, 111, 660, 2302, 'Demo Judge 2302', 85.54, 85.54, NULL, NULL, 14.26, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(361, 64, 111, 660, 2303, 'Demo Judge 2303', 85.97, 85.97, NULL, NULL, 14.33, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(362, 64, 112, 661, 2302, 'Demo Judge 2302', 88.60, 88.60, NULL, NULL, 14.77, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(363, 64, 112, 661, 2303, 'Demo Judge 2303', 79.79, 79.79, NULL, NULL, 13.30, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(364, 64, 112, 661, 2304, 'Demo Judge 2304', 90.35, 90.35, NULL, NULL, 15.06, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(365, 64, 113, 662, 2302, 'Demo Judge 2302', 90.64, 90.64, NULL, NULL, 15.11, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08'),
(366, 64, 113, 662, 2303, 'Demo Judge 2303', 88.12, 88.12, NULL, NULL, 14.69, 1.0000, 100.00, NULL, NULL, '2025-07-09 19:15:08', '2025-07-09 19:15:08');
