
-- --------------------------------------------------------

--
-- Table structure for table `user_notification_preferences`
--
-- Creation: Jul 08, 2025 at 01:43 PM
--

CREATE TABLE `user_notification_preferences` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `email_notifications` tinyint(1) DEFAULT 1,
  `sms_notifications` tinyint(1) DEFAULT 0,
  `push_notifications` tinyint(1) DEFAULT 1,
  `toast_notifications` tinyint(1) DEFAULT 1,
  `event_reminders` tinyint(1) DEFAULT 1,
  `registration_updates` tinyint(1) DEFAULT 1,
  `judging_updates` tinyint(1) DEFAULT 1,
  `award_notifications` tinyint(1) DEFAULT 1,
  `system_announcements` tinyint(1) DEFAULT 1,
  `reminder_times` varchar(255) DEFAULT '[1440, 60]',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- RELATIONSHIPS FOR TABLE `user_notification_preferences`:
--

--
-- Dumping data for table `user_notification_preferences`
--

INSERT INTO `user_notification_preferences` (`id`, `user_id`, `email_notifications`, `sms_notifications`, `push_notifications`, `toast_notifications`, `event_reminders`, `registration_updates`, `judging_updates`, `award_notifications`, `system_announcements`, `reminder_times`, `created_at`, `updated_at`) VALUES
(1, 4, 1, 0, 1, 1, 1, 1, 1, 1, 1, '[1440, 60]', '2025-06-22 02:14:27', '2025-06-22 02:14:27'),
(2, 5, 1, 0, 1, 1, 1, 1, 1, 1, 1, '[1440, 60]', '2025-06-22 02:14:27', '2025-06-22 02:14:27'),
(3, 12, 1, 0, 1, 1, 1, 1, 1, 1, 1, '[1440, 60]', '2025-06-22 02:14:27', '2025-06-22 02:14:27'),
(4, 13, 1, 0, 1, 1, 1, 1, 1, 1, 1, '[1440, 60]', '2025-06-22 02:14:27', '2025-06-22 02:14:27'),
(5, 53, 1, 0, 1, 1, 1, 1, 1, 1, 1, '[1440, 60]', '2025-06-22 02:14:27', '2025-06-22 02:14:27'),
(6, 210, 1, 0, 1, 1, 1, 1, 1, 1, 1, '[1440, 60]', '2025-06-22 02:14:27', '2025-06-22 02:14:27'),
(7, 211, 1, 0, 1, 1, 1, 1, 1, 1, 1, '[1440, 60]', '2025-06-22 02:14:27', '2025-06-22 02:14:27'),
(8, 3, 1, 0, 1, 1, 1, 1, 1, 1, 1, '[1440, 60]', '2025-06-22 02:14:27', '2025-07-15 14:12:47'),
(9, 1, 1, 0, 1, 1, 1, 1, 1, 1, 1, '[1440, 60]', '2025-07-15 13:06:53', '2025-07-15 13:06:53'),
(10, 2425, 1, 0, 1, 1, 1, 1, 1, 1, 1, '[1440, 60]', '2025-07-27 23:24:55', '2025-07-27 23:24:55');
