<?php
/**
 * Test Event Image Upload
 * 
 * This script tests the event image upload functionality to help debug the issue.
 */

// Start session and include necessary files
session_start();

// Define constants
define('APPROOT', dirname(__FILE__));
require_once APPROOT . '/config/config.php';
require_once APPROOT . '/helpers/csrf_helper.php';
require_once APPROOT . '/core/Database.php';
require_once APPROOT . '/models/ImageEditorModel.php';
require_once APPROOT . '/models/SettingsModel.php';

echo "<h1>🖼️ Event Image Upload Test</h1>";

// Check if DEBUG_MODE is enabled
echo "<h2>🔧 Debug Configuration</h2>";
echo "<p><strong>DEBUG_MODE:</strong> " . (defined('DEBUG_MODE') && DEBUG_MODE ? '✅ Enabled' : '❌ Disabled') . "</p>";
echo "<p><strong>CSRF_TOKEN_NAME:</strong> " . CSRF_TOKEN_NAME . "</p>";

// Check session
echo "<h2>👤 Session Status</h2>";
echo "<p><strong>Session ID:</strong> " . session_id() . "</p>";
echo "<p><strong>User ID:</strong> " . (isset($_SESSION['user_id']) ? $_SESSION['user_id'] : 'Not set') . "</p>";

// Generate CSRF token
$csrfToken = generateCsrfToken();
echo "<p><strong>CSRF Token:</strong> " . htmlspecialchars($csrfToken) . "</p>";

// Check database connection
echo "<h2>🗄️ Database Connection</h2>";
try {
    $db = new Database();
    echo "<p>✅ Database connection successful</p>";
    
    // Check if images table exists
    $db->query("SHOW TABLES LIKE 'images'");
    $result = $db->single();
    if ($result) {
        echo "<p>✅ Images table exists</p>";
        
        // Check table structure
        $db->query("DESCRIBE images");
        $columns = $db->resultSet();
        echo "<p><strong>Images table columns:</strong></p>";
        echo "<ul>";
        foreach ($columns as $column) {
            echo "<li>" . htmlspecialchars($column->Field) . " (" . htmlspecialchars($column->Type) . ")</li>";
        }
        echo "</ul>";
    } else {
        echo "<p>❌ Images table does not exist</p>";
    }
} catch (Exception $e) {
    echo "<p>❌ Database connection failed: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// Check upload directories
echo "<h2>📁 Upload Directory Status</h2>";
$uploadDirs = [
    'uploads/events/',
    'uploads/events/thumbnails/',
    'uploads/images/',
    'uploads/images/thumbnails/'
];

foreach ($uploadDirs as $dir) {
    if (file_exists($dir)) {
        echo "<p>✅ <strong>$dir</strong> exists";
        if (is_writable($dir)) {
            echo " and is writable</p>";
        } else {
            echo " but is NOT writable</p>";
        }
    } else {
        echo "<p>❌ <strong>$dir</strong> does not exist</p>";
    }
}

// Test ImageEditorModel instantiation
echo "<h2>🔧 Model Testing</h2>";
try {
    $imageModel = new ImageEditorModel();
    echo "<p>✅ ImageEditorModel instantiated successfully</p>";
    
    // Test settings retrieval
    $settingsModel = new SettingsModel();
    $maxUploadSize = $settingsModel->getSetting('image_max_upload_size', 5);
    $allowedExtensions = $settingsModel->getSetting('image_allowed_extensions', 'jpg,jpeg,png,gif,webp');
    
    echo "<p><strong>Max upload size:</strong> {$maxUploadSize}MB</p>";
    echo "<p><strong>Allowed extensions:</strong> {$allowedExtensions}</p>";
    
} catch (Exception $e) {
    echo "<p>❌ Model instantiation failed: " . htmlspecialchars($e->getMessage()) . "</p>";
}

// Simulate upload test (without actual file)
echo "<h2>🧪 Upload Simulation Test</h2>";
echo "<p>Testing upload parameters for event ID 21:</p>";

$entityType = 'event';
$entityId = 21;
$uploadDir = 'uploads/' . $entityType . 's/';

echo "<p><strong>Entity Type:</strong> $entityType</p>";
echo "<p><strong>Entity ID:</strong> $entityId</p>";
echo "<p><strong>Upload Directory:</strong> $uploadDir</p>";

// Check if the specific upload directory exists
if (file_exists($uploadDir)) {
    echo "<p>✅ Upload directory exists and is " . (is_writable($uploadDir) ? 'writable' : 'NOT writable') . "</p>";
} else {
    echo "<p>⚠️ Upload directory does not exist, but would be created automatically</p>";
}

echo "<h2>📝 Test Form</h2>";
echo "<p>Use this form to test the actual upload:</p>";
?>

<form action="<?php echo URLROOT; ?>/image_editor/upload_ajax" method="post" enctype="multipart/form-data" style="border: 1px solid #ccc; padding: 20px; margin: 20px 0;">
    <div style="margin-bottom: 10px;">
        <label for="image">Select Image:</label>
        <input type="file" name="image" id="image" accept="image/*" required>
    </div>
    
    <input type="hidden" name="entity_type" value="event">
    <input type="hidden" name="entity_id" value="21">
    <input type="hidden" name="title" value="Test Event Image">
    <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrfToken; ?>">
    
    <div style="margin-bottom: 10px;">
        <button type="submit" style="background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px;">
            Test Upload
        </button>
    </div>
    
    <p style="font-size: 12px; color: #666;">
        This form will submit to the same endpoint that the edit event page uses.
    </p>
</form>

<?php
echo "<h2>📋 Summary</h2>";
echo "<p>This test page helps identify potential issues with:</p>";
echo "<ul>";
echo "<li>Database connectivity and table structure</li>";
echo "<li>Upload directory permissions</li>";
echo "<li>CSRF token generation</li>";
echo "<li>Model instantiation</li>";
echo "<li>Settings configuration</li>";
echo "</ul>";
echo "<p><strong>Next steps:</strong> Try uploading an image using the form above and check the server logs for detailed error messages.</p>";
?>
