# Changelog

All notable changes to the Events and Shows Management System will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [3.72.0] - 2024-12-19

### Added - Judging Conflicts & Dispute Management System
- **Comprehensive Conflict Management**
  - Complete judging conflicts and dispute resolution system
  - Admin/coordinator dashboard for managing conflicts with filtering and statistics
  - Vehicle owner conflict reporting system with guided forms
  - Automatic score discrepancy detection when scores are finalized
  - Priority-based conflict handling (urgent, high, normal, low)
  - Status tracking (open, under review, resolved, dismissed, escalated)

- **Conflict Detection & Analytics**
  - Automatic detection of score discrepancies above configurable thresholds
  - Statistical dashboard with conflict metrics and resolution times
  - Related scores and judges tracking for comprehensive conflict analysis
  - Configurable detection criteria and notification settings

- **User Experience Features**
  - Mobile-responsive conflict interfaces for all user types
  - Comment system for conflict resolution communication
  - Integration with existing navigation and user dashboards
  - "Report Issue" button on user score viewing pages
  - Personal conflict reports tracking for vehicle owners

- **Database & Infrastructure**
  - Four new database tables for comprehensive conflict management
  - Database views for statistical reporting
  - Automatic conflict detection triggers in judging workflow
  - Migration script for easy installation

### Files Added (9)
- `controllers/JudgingConflictController.php` - Main conflict management controller
- `models/JudgingConflictModel.php` - Conflict data operations and detection algorithms
- `views/judging_conflict/dashboard.php` - Admin/coordinator conflict management interface
- `views/judging_conflict/view.php` - Individual conflict details and resolution
- `views/judging_conflict/report.php` - Conflict reporting form for vehicle owners
- `views/judging_conflict/my_reports.php` - User's personal conflict reports tracking
- `database/migrations/add_judging_conflicts_tables.sql` - Database schema migration
- `install_judging_conflicts.php` - Installation script for the conflict system

### Files Modified (5)
- `models/JudgingModel.php` - Added automatic conflict detection trigger
- `views/user/view_scores.php` - Added "Report Issue" button for vehicle owners
- `views/admin/dashboard.php` - Added judging conflicts quick link
- `views/includes/header.php` - Added navigation links for conflict management
- `structure.md` - Updated project structure documentation
- `features.md` - Added judging conflicts feature documentation

## [3.71.2] - 2025-01-29

### Enhanced - Event Photo Gallery Performance & User Experience
- **Performance Optimization**
  - Removed complex infinite scroll system that was causing performance issues and photo disappearing
  - Implemented simple, reliable Bootstrap pagination system
  - Fixed engagement data loading for proper display of likes, comments, and favorites
  - Eliminated SQL parameter binding issues that caused 500 errors
  - Simplified JavaScript to remove complex state management

- **Enhanced Search Functionality**
  - Added magnifying glass search button for users who don't know to press Enter
  - Enhanced search input with both Enter key and button click support
  - Expanded search field width for better user experience
  - Added clear search button that removes both search and category filters

- **Category Filtering System**
  - Made category badges on photos clickable for instant filtering
  - Added visual feedback with green highlighting for active category filters
  - Implemented toggle behavior - click same category to remove filter
  - Added category filter indicators in the filter info section
  - Combined category filtering with search and sort functionality

- **User Interface Improvements**
  - Removed problematic list view, kept clean grid-only layout
  - Enhanced filter info section with visual badges for active filters
  - Improved category badge styling with hover effects and tooltips
  - Better responsive design with optimized column layouts

### Files Modified (2)
- `controllers/ImageEditorController.php` - Added category filtering, fixed engagement data loading, simplified pagination
- `views/image_editor/event_gallery.php` - Enhanced search UI, added category filtering, removed list view

## [3.71.1] - 2025-01-29

### Fixed - Mobile Pinch-to-Zoom Issue
- **Pinch-to-Zoom Fix**
  - Fixed issue where pinch-to-zoom would automatically zoom back out when fingers were lifted
  - Improved touch event handling to properly maintain zoom state
  - Enhanced pinch gesture calculation using initial distance and scale ratios
  - Added proper state management for pinch operations
  - Fixed conflicts between double-tap and pinch gestures
  - Added debug logging for touch events when DEBUG_MODE is enabled

- **Touch Event Improvements**
  - Better separation of single-touch drag and two-finger pinch operations
  - Improved transition from pinch to drag when one finger is lifted
  - Enhanced double-tap detection to avoid conflicts with pinch gestures
  - More reliable touch state management across different mobile devices

### Files Modified (2)
- `views/calendar/event.php` - Fixed pinch-to-zoom implementation
- `test_image_modal_zoom.html` - Updated test file with same fixes

## [3.71.0] - 2025-01-29

### Added - Enhanced Image Modal with Zoom
- **Advanced Image Viewer**
  - Full-screen image modal with zoom capabilities
  - Pinch-to-zoom support for mobile devices
  - Mouse wheel zoom for desktop users
  - Drag-to-pan functionality when zoomed in
  - Double-tap to zoom on mobile devices

- **Mobile-First Design**
  - Touch-optimized controls and gestures
  - Full-screen viewing mode
  - Responsive button sizing for mobile
  - Improved close button accessibility
  - Smooth animations and transitions

- **Zoom Controls**
  - Zoom in/out buttons with visual feedback
  - Reset zoom to fit screen
  - Zoom level indicator (percentage display)
  - Maximum zoom of 500% and minimum of 50%
  - Fullscreen toggle button

- **Enhanced User Experience**
  - Keyboard support (ESC to close)
  - Click outside modal to close
  - Smooth zoom transitions
  - Visual feedback for all interactions
  - Optimized for both desktop and mobile

### Files Modified (1)
- `views/calendar/event.php` - Enhanced image modal with zoom functionality

## [3.70.0] - 2025-01-29

### Added - Event Main Image Feature
- **Event Main Image Support**
  - Added ability to set a main image for calendar events
  - Main images are displayed prominently on event detail pages
  - Images are automatically optimized for web performance
  - Supports JPG and PNG formats up to 10MB
  - Uses existing image management system for consistency

- **Database Changes**
  - Added `main_image_id` column to `calendar_events` table
  - Foreign key relationship to existing `images` table
  - Automatic cleanup when events are deleted

- **User Interface Enhancements**
  - Image selector modal with browse and upload tabs
  - Drag-and-drop upload functionality
  - Image preview with metadata display
  - Click-to-view full-size image modal
  - Mobile-responsive design

- **Integration Features**
  - Leverages existing ImageEditorController and ImageEditorModel
  - Uses existing image optimization and thumbnail generation
  - Maintains consistency with site's image management system
  - Automatic social media meta image integration

### Files Modified (6)
- `controllers/CalendarController.php` - Added main image handling
- `models/CalendarModel.php` - Updated event CRUD operations
- `controllers/ImageEditorController.php` - Added getEventImages method
- `views/calendar/edit_event.php` - Added image selector interface
- `views/calendar/event.php` - Added main image display
- `database/add_event_main_image_column.sql` - Database schema update

## [3.69.0] - 2025-01-29

### Added - Event Photo Sharing System (Integrated Approach)
- **Event Photo Sharing System - Integrated with Existing Image System**
  - Location-based photo sharing with GPS verification within configurable radius
  - Smart photo categories: Vehicle Spotlight, Event Atmosphere, Awards & Judging, Food & Vendors, People & Friends
  - Privacy controls: Public, Event Attendees Only, Friends Only, Private
  - Seamless integration with existing image upload and editor system
  - Event-specific photo galleries with category filtering
  - Mobile-first PWA camera integration with event detection

- **Integration Architecture**
  - Uses existing `images` table with `entity_type = 'event_photo'`
  - Leverages existing `ImageEditorController.php` and `ImageEditorModel.php`
  - Entity ID format: `event_123` or `show_456` for proper categorization
  - Event photos open in existing image editor for management
  - Maintains all existing image features: thumbnails, optimization, management

- **Database Changes (Minimal)**
  - Added `event_photo_metadata` table for event-specific data only
  - No changes to existing `images`, `users`, `events`, or `shows` tables
  - Foreign key relationship to existing images table
  - Minimal impact on existing system architecture

- **Location Verification System**
  - GPS-based location verification ensures users are at event venues
  - Manual event check-in system for GPS failures or user preference
  - Nearby events discovery with distance calculation
  - Configurable time windows (before/after event dates)

- **PWA Integration (Enhanced Existing)**
  - Enhanced existing FAB camera with event photo detection
  - Uses existing `uploadToImageEditor()` method
  - Maintains existing `data-camera-capture` system
  - Location detection and automatic event verification

- **Navigation Integration**
  - Added "Event Photos" buttons to show and event pages
  - Links to integrated gallery: `/image_editor/eventGallery/show/123`
  - Seamless navigation between event pages and photo galleries
  - Consistent with existing site navigation patterns
### Files Modified (5)
- `public/js/pwa-features.js` - Enhanced FAB camera with event photo detection
- `controllers/ImageEditorController.php` - Added event photo support and gallery method
- `models/ImageEditorModel.php` - Added event photo metadata handling
- `views/show/view.php` - Added "Event Photos" button for show galleries
- `views/calendar/event.php` - Added "Event Photos" button for event galleries

### Files Added (2)
- `sql/event_photos_tables.sql` - Creates event_photo_metadata table only
- `views/image_editor/event_gallery.php` - Event photo gallery view
- `docs/event_photo_sharing_integrated.md` - Integration documentation

### Updated
- `features.md` - Updated Event Photo Sharing system documentation (integrated approach)
- `CHANGELOG.md` - Updated v3.69.0 documentation to reflect integrated implementation

## [3.69.0] - 2025-01-28

### Changed
- **Navigation Menu Consolidation** - Merged EVENTS and SHOWS menus into single CALENDAR menu
- Replaced separate "Events" and "Shows" dropdown menus with unified "Calendar" menu
- Consolidated mobile drawer navigation from two buttons to single CALENDAR button
- **Icon Cleanup** - Removed duplicate emoji icons from CALENDAR menu to match other menus
- Improved mobile-first responsive design with cleaner menu structure
- Reduced visitor confusion by eliminating duplicate calendar links
- Enhanced user experience with clear distinction between simple events and full car shows
- **Calendar Page Title Update** - Changed page title from "Monthly Event Chart" to "Monthly Calendar"
- **Header Cleanup** - Completely removed redundant role badge and switch view from header
- **Enhanced Breadcrumb Bar** - Added racing header styling with carbon fiber pattern and chrome effects
- **Role Indicator Enhancement** - Added role icons and restored hover tooltips with role capabilities descriptions
- **Racing Switch Dropdown** - Styled admin switch view dropdown to match racing header theme
- **Home Page Breadcrumb** - Display breadcrumb bar on home page for logged-in users to show role context
- **Mobile Role Context** - Added compact mobile role bar to preserve role visibility and admin switch access on mobile
- **PWA Menu Update** - Updated bottom navigation to match new consolidated CALENDAR structure
- **Progressive Registration Redirects** - Guests accessing /user/createShow, /calendar/createEvent, or /calendar/map now redirect to progressive registration instead of login page
- **Guest Conversion Page** - Added compelling "Host Your Show" conversion page that explains benefits and features before registration
- **Car Instrument Cluster PWA Menu** - Added speedometer emoji center button in PWA menu resembling car dashboard
- **Streamlined Navigation** - Removed redundant Member Area and Dashboard buttons from mobile menu since speedometer provides dashboard access

## [3.67.10] - 2024-12-19

### Changed
- Removed green shadow from "Get Directions" button in calendar map popups
- Added box-shadow: none to all info window buttons for cleaner appearance
- Enhanced button styling consistency across hover and focus states

## [3.67.9] - 2024-12-19

### Fixed
- Fixed JavaScript ReferenceError for getDirectionsToEvent function
- Moved getDirectionsToEvent function to global scope to resolve onclick handler issues
- Removed duplicate function definition that was causing conflicts

## [3.67.8] - 2024-12-19

### Added
- Get Directions button in calendar map event popups
- User address integration for calendar map directions
- Enhanced popup styling for better button layout

### Changed
- Calendar map popups now include directions functionality similar to show view
- Improved info window styling with better button spacing and hover effects
