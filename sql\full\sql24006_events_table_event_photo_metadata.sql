
-- --------------------------------------------------------

--
-- Table structure for table `event_photo_metadata`
--
-- Creation: Aug 01, 2025 at 09:08 PM
--

CREATE TABLE `event_photo_metadata` (
  `id` int(10) UNSIGNED NOT NULL,
  `image_id` int(10) UNSIGNED NOT NULL,
  `category` varchar(50) NOT NULL DEFAULT 'atmosphere',
  `caption` text DEFAULT NULL,
  `privacy_level` enum('public','attendees','friends','private') DEFAULT 'public',
  `latitude` decimal(10,8) DEFAULT NULL,
  `longitude` decimal(11,8) DEFAULT NULL,
  `event_type` enum('event','show') NOT NULL,
  `event_id` int(10) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- RELATIONSHIPS FOR TABLE `event_photo_metadata`:
--   `image_id`
--       `images` -> `id`
--

--
-- Dumping data for table `event_photo_metadata`
--

INSERT INTO `event_photo_metadata` (`id`, `image_id`, `category`, `caption`, `privacy_level`, `latitude`, `longitude`, `event_type`, `event_id`, `created_at`, `updated_at`) VALUES
(2, 576, 'atmosphere', 'this \r\nis a flag\r\nyes', 'public', NULL, NULL, 'show', 5, '2025-07-30 13:48:49', '2025-07-30 18:38:49'),
(3, 578, 'vehicle', 'this is a test caption\r\nfdgfdg\r\ngdfgdfgfdgdfgfd\r\ngfdgfdgfdg\r\ngfdgfdgfdgfdgfdgdf', 'public', NULL, NULL, 'show', 5, '2025-07-30 18:06:35', '2025-08-01 13:39:53'),
(4, 582, 'atmosphere', 'test', 'public', NULL, NULL, 'show', 9, '2025-07-30 23:32:34', '2025-07-30 23:32:58'),
(9, 587, 'people', 'vkkjcvjjvc', 'public', NULL, NULL, 'show', 9, '2025-07-31 00:27:23', '2025-07-31 00:28:01'),
(10, 589, 'people', '', 'public', NULL, NULL, 'show', 5, '2025-07-31 19:45:10', '2025-08-01 13:39:33');
