
-- --------------------------------------------------------

--
-- Table structure for table `offline_sync_queue`
--
-- Creation: Jul 02, 2025 at 09:07 PM
--

CREATE TABLE `offline_sync_queue` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `sync_type` varchar(50) NOT NULL,
  `sync_data` text NOT NULL,
  `status` varchar(20) DEFAULT 'pending',
  `attempts` int(11) DEFAULT 0,
  `max_attempts` int(11) DEFAULT 3,
  `error_message` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `processed_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_uca1400_ai_ci;

--
-- RELATIONSHIPS FOR TABLE `offline_sync_queue`:
--
