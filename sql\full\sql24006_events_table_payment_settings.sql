
-- --------------------------------------------------------

--
-- Table structure for table `payment_settings`
--
-- Creation: Jun 23, 2025 at 08:30 PM
--

CREATE TABLE `payment_settings` (
  `id` int(10) UNSIGNED NOT NULL,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `is_admin` tinyint(1) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- RELATIONSHIPS FOR TABLE `payment_settings`:
--

--
-- Dumping data for table `payment_settings`
--

INSERT INTO `payment_settings` (`id`, `setting_key`, `setting_value`, `created_at`, `updated_at`, `is_admin`) VALUES
(1, 'paypal_client_id', '<EMAIL>', '2025-05-20 08:39:55', '2025-06-11 17:35:46', 1),
(2, 'paypal_secret', '242Bman$12181977', '2025-05-20 08:39:55', '2025-06-11 17:35:46', 1),
(3, 'paypal_sandbox', 'true', '2025-05-20 08:39:55', '2025-05-20 08:39:55', 1),
(4, 'cashapp_id', 'gurusonwheels', '2025-05-20 08:39:55', '2025-06-12 19:52:52', 1),
(5, 'venmo_id', 'gurusonwheels', '2025-05-20 08:39:55', '2025-06-12 19:52:52', 1),
(6, 'currency', 'USD', '2025-05-20 08:39:55', '2025-05-20 08:39:55', 1),
(7, 'show_listing_fee_enabled', 'true', '2025-05-20 08:39:55', '2025-05-20 08:39:55', 1),
(8, 'default_registration_fee', '0.00', '2025-05-20 08:39:55', '2025-05-20 08:39:55', 1),
(9, 'default_show_listing_fee', '0', '2025-05-20 08:39:55', '2025-06-11 17:35:46', 1),
(98, 'paypal_client_id', 'dfgdfgd', '2025-06-12 08:46:00', '2025-06-12 09:37:21', 0),
(99, 'paypal_secret', 'dfgdfgdfgdf', '2025-06-12 08:46:00', '2025-06-12 09:37:21', 0),
(100, 'paypal_sandbox', 'false', '2025-06-12 08:46:00', '2025-06-12 09:11:36', 0),
(101, 'cashapp_id', 'sdfsdfs', '2025-06-12 08:46:00', '2025-06-12 09:37:44', 0),
(102, 'venmo_id', 'fsdfsdf', '2025-06-12 08:46:00', '2025-06-12 09:37:44', 0),
(103, 'currency', 'USD', '2025-06-12 08:46:00', '2025-06-12 19:52:52', 0),
(104, 'default_registration_fee', '', '2025-06-12 08:46:00', '2025-06-12 08:46:00', 0);
