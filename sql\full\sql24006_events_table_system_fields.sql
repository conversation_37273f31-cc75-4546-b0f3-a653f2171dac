
-- --------------------------------------------------------

--
-- Table structure for table `system_fields`
--
-- Creation: Jun 23, 2025 at 08:30 PM
--

CREATE TABLE `system_fields` (
  `id` int(11) NOT NULL,
  `field_id` varchar(100) NOT NULL,
  `display_name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `entity_type` varchar(100) NOT NULL,
  `is_required` tinyint(1) DEFAULT 1,
  `can_edit_properties` tinyint(1) DEFAULT 0,
  `is_critical` tinyint(1) DEFAULT 0,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- RELATIONSHIPS FOR TABLE `system_fields`:
--

--
-- Dumping data for table `system_fields`
--

INSERT INTO `system_fields` (`id`, `field_id`, `display_name`, `description`, `entity_type`, `is_required`, `can_edit_properties`, `is_critical`, `created_at`, `updated_at`) VALUES
(973, 'vehicle_id', 'Select Vehicle', '', 'registration', 1, 0, 1, '2025-05-27 17:00:54', '2025-05-27 17:21:11'),
(974, 'category_id', 'Vehicle Category', '', 'registration', 1, 0, 1, '2025-05-27 17:00:54', '2025-05-27 17:21:11'),
(975, 'special_features', 'Special Features', '', 'registration', 0, 0, 1, '2025-05-27 17:00:54', '2025-05-27 17:21:11'),
(976, 'vehicle_condition', 'Vehicle Condition', '', 'registration', 1, 0, 1, '2025-05-27 17:00:54', '2025-05-27 17:21:11'),
(977, 'year_of_manufacture', 'Year of Manufacture', '', 'registration', 1, 0, 1, '2025-05-27 17:00:54', '2025-05-27 17:21:11'),
(978, 'payment_method_id', 'Payment Method', '', 'registration', 1, 0, 1, '2025-05-27 17:00:54', '2025-05-27 17:21:11'),
(979, 'additional_notes', 'Additional Notes', '', 'registration', 0, 0, 1, '2025-05-27 17:00:54', '2025-05-27 17:21:11'),
(980, 'agree_terms', 'I agree to the show rules and terms', '', 'registration', 1, 0, 1, '2025-05-27 17:00:54', '2025-05-27 17:21:11'),
(981, 'vehiclename', 'Vehicle Name', '', 'vehicle', 0, 0, 1, '2025-05-27 17:00:54', '2025-05-27 17:21:11'),
(982, 'plate', 'License Plate', '', 'vehicle', 0, 0, 1, '2025-05-27 17:00:54', '2025-05-27 17:21:11'),
(983, 'make', 'Make', '', 'vehicle', 0, 0, 1, '2025-05-27 17:00:54', '2025-05-27 17:21:11'),
(984, 'model', 'Model', '', 'vehicle', 0, 0, 1, '2025-05-27 17:00:54', '2025-05-27 17:21:11'),
(985, 'year', 'Year', '', 'vehicle', 0, 0, 1, '2025-05-27 17:00:55', '2025-05-27 17:21:11'),
(986, 'name', 'Show Name', '', 'event', 1, 0, 1, '2025-05-27 17:00:55', '2025-05-27 17:21:11'),
(987, 'location', 'Location', '', 'event', 0, 0, 1, '2025-05-27 17:00:55', '2025-06-24 13:04:57'),
(988, 'description', 'Description', '', 'event', 1, 0, 1, '2025-05-27 17:00:55', '2025-05-27 17:21:11'),
(989, 'start_date', 'Start Date', '', 'event', 1, 0, 1, '2025-05-27 17:00:55', '2025-05-27 17:21:11'),
(990, 'end_date', 'End Date', '', 'event', 1, 0, 1, '2025-05-27 17:00:55', '2025-05-27 17:21:11'),
(991, 'registration_start', 'Registration Start', '', 'event', 0, 0, 1, '2025-05-27 17:00:55', '2025-05-27 17:21:11'),
(992, 'registration_end', 'Registration End', '', 'event', 0, 0, 1, '2025-05-27 17:00:55', '2025-05-27 17:21:11'),
(993, 'coordinator_id', 'Coordinator', '', 'event', 0, 0, 1, '2025-05-27 17:00:55', '2025-05-27 17:21:11'),
(994, 'status', 'Status', '', 'event', 1, 0, 1, '2025-05-27 17:00:55', '2025-05-27 17:21:11'),
(995, 'fan_voting_enabled', 'Fan Favorite Voting', '', 'event', 0, 0, 1, '2025-05-27 17:00:55', '2025-05-27 17:21:11'),
(996, 'color', 'Color', NULL, 'vehicle', 0, 0, 1, '2025-05-27 17:21:43', '2025-05-27 17:21:43'),
(997, 'vin', 'VIN', NULL, 'vehicle', 0, 0, 1, '2025-05-27 17:21:43', '2025-05-27 17:21:43'),
(1023, 'registration_fee', 'Registration Fee ($)', '', 'event', 0, 1, 1, '2025-06-03 19:08:29', '2025-06-03 19:22:25'),
(1024, 'is_free', 'This is a free show (no registration fees)', NULL, 'event', 0, 0, 1, '2025-06-03 19:10:59', '2025-06-03 19:22:50'),
(1025, 'listing_fee', 'Listing Fee ($)	', NULL, 'event', 0, 0, 1, '2025-06-03 19:10:59', '2025-06-03 19:22:56'),
(1043, 'address1', 'Address 1', '', 'event', 1, 0, 1, '2025-06-16 14:37:09', '2025-06-16 14:38:44'),
(1044, 'address2', 'Address2', '', 'event', 0, 0, 1, '2025-06-16 14:37:09', '2025-06-16 14:38:59'),
(1045, 'city', 'City', '', 'event', 1, 0, 1, '2025-06-16 14:37:09', '2025-06-16 14:38:56'),
(1046, 'state', 'State', '', 'event', 1, 0, 1, '2025-06-16 14:37:09', '2025-06-16 14:38:52'),
(1047, 'zipcode', 'Zip Code', '', 'event', 1, 0, 1, '2025-06-16 14:37:09', '2025-06-16 14:38:49'),
(1098, 'club', 'Club', '', 'event', 0, 0, 1, '2025-06-24 13:01:11', '2025-06-24 13:04:19'),
(1141, 'Judge_time', 'Judging Start Time', '', 'event', 0, 0, 0, '2025-07-19 19:45:00', '2025-07-19 19:45:00'),
(1142, 'awards_time', 'Awards Start Time', '', 'event', 0, 0, 0, '2025-07-19 19:45:00', '2025-07-19 19:45:00');
