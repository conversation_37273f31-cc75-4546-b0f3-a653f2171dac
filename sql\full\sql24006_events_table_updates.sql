
-- --------------------------------------------------------

--
-- Table structure for table `updates`
--
-- Creation: Jun 23, 2025 at 08:30 PM
--

CREATE TABLE `updates` (
  `id` int(11) NOT NULL,
  `version` varchar(20) NOT NULL,
  `description` text DEFAULT NULL,
  `applied_at` datetime NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- RELATIONSHIPS FOR TABLE `updates`:
--

--
-- Dumping data for table `updates`
--

INSERT INTO `updates` (`id`, `version`, `description`, `applied_at`) VALUES
(1, '2.2.0', 'Added registration edit functionality for users', '2025-05-21 15:10:47'),
(2, '2.2.0', 'Added registration edit functionality for users', '2025-05-21 15:10:48'),
(3, '2.2.0', 'Added registration edit functionality for users', '2025-05-21 15:11:18'),
(4, '2.2.0', 'Added registration edit functionality for users', '2025-05-21 15:12:54'),
(5, '2.2.0', 'Added registration edit functionality for users', '2025-05-21 15:13:08'),
(6, '2.2.0', 'Added registration edit functionality for users', '2025-05-21 15:13:09'),
(7, '2.2.0', 'Added registration edit functionality for users', '2025-05-21 15:13:39'),
(8, '2.2.0', 'Added registration edit functionality for users', '2025-05-21 15:22:30'),
(9, '2.2.0', 'Added registration edit functionality for users', '2025-05-21 15:39:53'),
(10, '3.34.6', 'Fixed database table references in scoring scripts', '2025-06-08 19:58:49');
