<?php
/**
 * Simple Judging Conflicts Installation Script
 * 
 * This script installs the judging conflicts system by creating tables one by one.
 */

// Include configuration
require_once 'config/config.php';
require_once 'core/Database.php';

echo "<h1>Judging Conflicts System Installation (Simple)</h1>\n";

try {
    // Create database connection
    $db = new Database();
    
    echo "<p>Creating judging conflicts tables...</p>\n";
    
    $successCount = 0;
    $errorCount = 0;
    
    // 1. Create judging_conflicts table
    echo "<h3>1. Creating judging_conflicts table</h3>\n";
    try {
        $sql = "CREATE TABLE IF NOT EXISTS `judging_conflicts` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `show_id` int(11) NOT NULL,
            `registration_id` int(11) DEFAULT NULL,
            `title` varchar(255) NOT NULL,
            `description` text NOT NULL,
            `conflict_type` enum('score_discrepancy','assignment_conflict','scoring_dispute','technical_error','owner_complaint','judge_concern') NOT NULL,
            `priority` enum('low','normal','high','urgent') NOT NULL DEFAULT 'normal',
            `status` enum('open','under_review','resolved','dismissed','escalated') NOT NULL DEFAULT 'open',
            `reported_by_user_id` int(11) NOT NULL,
            `reported_by_role` enum('user','judge','coordinator','admin') NOT NULL,
            `assigned_to_admin_id` int(11) DEFAULT NULL,
            `resolved_by_admin_id` int(11) DEFAULT NULL,
            `resolution_notes` text DEFAULT NULL,
            `resolution_action` text DEFAULT NULL,
            `auto_detected` tinyint(1) NOT NULL DEFAULT 0,
            `detection_criteria` text DEFAULT NULL,
            `related_data` json DEFAULT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            `resolved_at` timestamp NULL DEFAULT NULL,
            `escalated_at` timestamp NULL DEFAULT NULL,
            PRIMARY KEY (`id`),
            KEY `idx_show_id` (`show_id`),
            KEY `idx_registration_id` (`registration_id`),
            KEY `idx_status` (`status`),
            KEY `idx_priority` (`priority`),
            KEY `idx_reported_by` (`reported_by_user_id`),
            KEY `idx_assigned_to` (`assigned_to_admin_id`),
            KEY `idx_created_at` (`created_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $db->query($sql);
        $db->execute();
        echo "<p style='color: green;'>✓ judging_conflicts table created successfully</p>\n";
        $successCount++;
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Error creating judging_conflicts table: " . htmlspecialchars($e->getMessage()) . "</p>\n";
        $errorCount++;
    }
    
    // 2. Create judging_conflict_comments table
    echo "<h3>2. Creating judging_conflict_comments table</h3>\n";
    try {
        $sql = "CREATE TABLE IF NOT EXISTS `judging_conflict_comments` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `conflict_id` int(11) NOT NULL,
            `user_id` int(11) NOT NULL,
            `comment` text NOT NULL,
            `is_internal` tinyint(1) NOT NULL DEFAULT 0,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            KEY `idx_conflict_id` (`conflict_id`),
            KEY `idx_user_id` (`user_id`),
            KEY `idx_created_at` (`created_at`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $db->query($sql);
        $db->execute();
        echo "<p style='color: green;'>✓ judging_conflict_comments table created successfully</p>\n";
        $successCount++;
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Error creating judging_conflict_comments table: " . htmlspecialchars($e->getMessage()) . "</p>\n";
        $errorCount++;
    }
    
    // 3. Create judging_conflict_related_scores table
    echo "<h3>3. Creating judging_conflict_related_scores table</h3>\n";
    try {
        $sql = "CREATE TABLE IF NOT EXISTS `judging_conflict_related_scores` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `conflict_id` int(11) NOT NULL,
            `score_id` int(11) NOT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `unique_conflict_score` (`conflict_id`, `score_id`),
            KEY `idx_conflict_id` (`conflict_id`),
            KEY `idx_score_id` (`score_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $db->query($sql);
        $db->execute();
        echo "<p style='color: green;'>✓ judging_conflict_related_scores table created successfully</p>\n";
        $successCount++;
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Error creating judging_conflict_related_scores table: " . htmlspecialchars($e->getMessage()) . "</p>\n";
        $errorCount++;
    }
    
    // 4. Create judging_conflict_related_judges table
    echo "<h3>4. Creating judging_conflict_related_judges table</h3>\n";
    try {
        $sql = "CREATE TABLE IF NOT EXISTS `judging_conflict_related_judges` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `conflict_id` int(11) NOT NULL,
            `judge_id` int(11) NOT NULL,
            `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`),
            UNIQUE KEY `unique_conflict_judge` (`conflict_id`, `judge_id`),
            KEY `idx_conflict_id` (`conflict_id`),
            KEY `idx_judge_id` (`judge_id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $db->query($sql);
        $db->execute();
        echo "<p style='color: green;'>✓ judging_conflict_related_judges table created successfully</p>\n";
        $successCount++;
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Error creating judging_conflict_related_judges table: " . htmlspecialchars($e->getMessage()) . "</p>\n";
        $errorCount++;
    }
    
    // 5. Add foreign key constraints (if tables exist)
    echo "<h3>5. Adding foreign key constraints</h3>\n";
    
    // Check if we can add foreign keys (tables must exist first)
    if ($successCount >= 4) {
        $constraints = [
            "ALTER TABLE `judging_conflicts` ADD CONSTRAINT `fk_conflicts_show` FOREIGN KEY (`show_id`) REFERENCES `shows` (`id`) ON DELETE CASCADE",
            "ALTER TABLE `judging_conflicts` ADD CONSTRAINT `fk_conflicts_registration` FOREIGN KEY (`registration_id`) REFERENCES `registrations` (`id`) ON DELETE SET NULL",
            "ALTER TABLE `judging_conflicts` ADD CONSTRAINT `fk_conflicts_reported_by` FOREIGN KEY (`reported_by_user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE",
            "ALTER TABLE `judging_conflicts` ADD CONSTRAINT `fk_conflicts_assigned_to` FOREIGN KEY (`assigned_to_admin_id`) REFERENCES `users` (`id`) ON DELETE SET NULL",
            "ALTER TABLE `judging_conflicts` ADD CONSTRAINT `fk_conflicts_resolved_by` FOREIGN KEY (`resolved_by_admin_id`) REFERENCES `users` (`id`) ON DELETE SET NULL",
            "ALTER TABLE `judging_conflict_comments` ADD CONSTRAINT `fk_comments_conflict` FOREIGN KEY (`conflict_id`) REFERENCES `judging_conflicts` (`id`) ON DELETE CASCADE",
            "ALTER TABLE `judging_conflict_comments` ADD CONSTRAINT `fk_comments_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE",
            "ALTER TABLE `judging_conflict_related_scores` ADD CONSTRAINT `fk_related_scores_conflict` FOREIGN KEY (`conflict_id`) REFERENCES `judging_conflicts` (`id`) ON DELETE CASCADE",
            "ALTER TABLE `judging_conflict_related_scores` ADD CONSTRAINT `fk_related_scores_score` FOREIGN KEY (`score_id`) REFERENCES `scores` (`id`) ON DELETE CASCADE",
            "ALTER TABLE `judging_conflict_related_judges` ADD CONSTRAINT `fk_related_judges_conflict` FOREIGN KEY (`conflict_id`) REFERENCES `judging_conflicts` (`id`) ON DELETE CASCADE",
            "ALTER TABLE `judging_conflict_related_judges` ADD CONSTRAINT `fk_related_judges_judge` FOREIGN KEY (`judge_id`) REFERENCES `users` (`id`) ON DELETE CASCADE"
        ];
        
        foreach ($constraints as $constraint) {
            try {
                $db->query($constraint);
                $db->execute();
                echo "<p style='color: green;'>✓ Added foreign key constraint</p>\n";
                $successCount++;
            } catch (Exception $e) {
                // Foreign key constraints might fail if they already exist or if referenced tables don't exist
                echo "<p style='color: orange;'>⚠ Foreign key constraint skipped: " . htmlspecialchars($e->getMessage()) . "</p>\n";
            }
        }
    }
    
    // 6. Add settings
    echo "<h3>6. Adding configuration settings</h3>\n";
    $settings = [
        ['conflict_auto_detection_enabled', '1', 'Enable automatic conflict detection when scores are finalized'],
        ['conflict_score_discrepancy_threshold', '15', 'Percentage threshold for score discrepancy detection'],
        ['conflict_notification_enabled', '1', 'Send notifications when conflicts are created or updated'],
        ['conflict_time_limit_hours', '72', 'Time limit in hours for reporting conflicts after results are posted'],
        ['conflict_escalation_threshold_hours', '48', 'Hours after which unresolved conflicts are escalated']
    ];
    
    foreach ($settings as $setting) {
        try {
            $db->query("INSERT IGNORE INTO `settings` (`name`, `value`, `description`) VALUES (?, ?, ?)");
            $db->bind(1, $setting[0]);
            $db->bind(2, $setting[1]);
            $db->bind(3, $setting[2]);
            $db->execute();
            echo "<p style='color: blue;'>✓ Added setting: {$setting[0]}</p>\n";
            $successCount++;
        } catch (Exception $e) {
            echo "<p style='color: red;'>✗ Error adding setting {$setting[0]}: " . htmlspecialchars($e->getMessage()) . "</p>\n";
            $errorCount++;
        }
    }
    
    // 7. Create additional indexes
    echo "<h3>7. Creating additional indexes</h3>\n";
    $indexes = [
        "CREATE INDEX IF NOT EXISTS `idx_conflicts_status_priority` ON `judging_conflicts` (`status`, `priority`)",
        "CREATE INDEX IF NOT EXISTS `idx_conflicts_show_status` ON `judging_conflicts` (`show_id`, `status`)",
        "CREATE INDEX IF NOT EXISTS `idx_conflicts_auto_detected` ON `judging_conflicts` (`auto_detected`)"
    ];
    
    foreach ($indexes as $index) {
        try {
            $db->query($index);
            $db->execute();
            echo "<p style='color: orange;'>✓ Created index</p>\n";
            $successCount++;
        } catch (Exception $e) {
            echo "<p style='color: red;'>✗ Error creating index: " . htmlspecialchars($e->getMessage()) . "</p>\n";
            $errorCount++;
        }
    }
    
    // 8. Create statistics view
    echo "<h3>8. Creating statistics view</h3>\n";
    try {
        $sql = "CREATE OR REPLACE VIEW `judging_conflict_stats` AS
        SELECT 
            COUNT(*) as total_conflicts,
            SUM(CASE WHEN status = 'open' THEN 1 ELSE 0 END) as open_conflicts,
            SUM(CASE WHEN status = 'under_review' THEN 1 ELSE 0 END) as under_review_conflicts,
            SUM(CASE WHEN status = 'resolved' THEN 1 ELSE 0 END) as resolved_conflicts,
            SUM(CASE WHEN status = 'dismissed' THEN 1 ELSE 0 END) as dismissed_conflicts,
            SUM(CASE WHEN status = 'escalated' THEN 1 ELSE 0 END) as escalated_conflicts,
            SUM(CASE WHEN priority = 'urgent' THEN 1 ELSE 0 END) as urgent_conflicts,
            SUM(CASE WHEN priority = 'high' THEN 1 ELSE 0 END) as high_conflicts,
            SUM(CASE WHEN auto_detected = 1 THEN 1 ELSE 0 END) as auto_detected_conflicts,
            AVG(CASE 
                WHEN resolved_at IS NOT NULL 
                THEN TIMESTAMPDIFF(HOUR, created_at, resolved_at) 
                ELSE NULL 
            END) as avg_resolution_hours,
            COUNT(DISTINCT show_id) as shows_with_conflicts,
            COUNT(DISTINCT reported_by_user_id) as users_reporting_conflicts
        FROM judging_conflicts";
        
        $db->query($sql);
        $db->execute();
        echo "<p style='color: green;'>✓ Created judging_conflict_stats view</p>\n";
        $successCount++;
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Error creating statistics view: " . htmlspecialchars($e->getMessage()) . "</p>\n";
        $errorCount++;
    }
    
    echo "<hr>\n";
    echo "<h2>Installation Summary</h2>\n";
    echo "<p><strong>Successful operations:</strong> $successCount</p>\n";
    echo "<p><strong>Failed operations:</strong> $errorCount</p>\n";
    
    if ($errorCount === 0) {
        echo "<p style='color: green; font-weight: bold;'>✓ Judging Conflicts system installed successfully!</p>\n";
    } else {
        echo "<p style='color: orange; font-weight: bold;'>⚠ Installation completed with some errors. The system should still be functional.</p>\n";
    }
    
    // Test database connectivity
    echo "<h3>System Test</h3>\n";
    
    try {
        $db->query("SELECT COUNT(*) as count FROM judging_conflicts");
        $result = $db->single();
        echo "<p style='color: green;'>✓ judging_conflicts table accessible (count: " . $result->count . ")</p>\n";
        
        $db->query("SELECT COUNT(*) as count FROM settings WHERE name LIKE 'conflict_%'");
        $result = $db->single();
        echo "<p style='color: green;'>✓ Configuration settings installed: " . $result->count . " settings</p>\n";
        
        echo "<h3>Next Steps:</h3>\n";
        echo "<ul>\n";
        echo "<li>Access the admin dashboard to configure conflict settings</li>\n";
        echo "<li>Visit <a href='" . BASE_URL . "/judging_conflict/dashboard'>Judging Conflicts Dashboard</a> to manage conflicts</li>\n";
        echo "<li>Users can report conflicts from their score pages</li>\n";
        echo "<li>Automatic conflict detection will run when scores are finalized</li>\n";
        echo "</ul>\n";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Database test failed: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>Installation failed:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<p>Please check your database configuration and try again.</p>\n";
}

echo "<hr>\n";
echo "<p><a href='" . BASE_URL . "'>← Return to Home</a></p>\n";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
}

p {
    margin: 5px 0;
}

hr {
    margin: 20px 0;
    border: none;
    border-top: 1px solid #ccc;
}

ul {
    padding-left: 20px;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>