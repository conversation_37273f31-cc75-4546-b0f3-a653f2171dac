-- Create judging conflicts table for conflict resolution system
-- This table tracks all types of judging conflicts and disputes

CREATE TABLE IF NOT EXISTS `judging_conflicts` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `show_id` int(10) UNSIGNED NOT NULL,
  `registration_id` int(10) UNSIGNED DEFAULT NULL COMMENT 'Related registration if applicable',
  `conflict_type` enum('score_discrepancy','assignment_conflict','scoring_dispute','technical_error','owner_complaint','judge_concern','system_detected') NOT NULL,
  `reported_by_user_id` int(10) UNSIGNED NOT NULL,
  `reported_by_role` enum('judge','admin','coordinator','owner','system','staff') NOT NULL,
  `title` varchar(255) NOT NULL COMMENT 'Brief title of the conflict',
  `description` text NOT NULL COMMENT 'Detailed description of the conflict',
  `related_score_ids` text DEFAULT NULL COMMENT 'JSON array of score IDs involved',
  `related_judge_ids` text DEFAULT NULL COMMENT 'JSON array of judge IDs involved',
  `related_data` text DEFAULT NULL COMMENT 'JSON object with additional conflict data',
  `status` enum('open','under_review','resolved','dismissed','escalated') NOT NULL DEFAULT 'open',
  `priority` enum('low','normal','high','urgent') NOT NULL DEFAULT 'normal',
  `assigned_to_admin_id` int(10) UNSIGNED DEFAULT NULL,
  `resolution_notes` text DEFAULT NULL,
  `resolution_action` text DEFAULT NULL COMMENT 'Actions taken to resolve the conflict',
  `resolved_by_user_id` int(10) UNSIGNED DEFAULT NULL,
  `resolved_at` timestamp NULL DEFAULT NULL,
  `escalated_at` timestamp NULL DEFAULT NULL,
  `auto_detected` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Whether this was automatically detected',
  `detection_criteria` text DEFAULT NULL COMMENT 'Criteria used for automatic detection',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_show_id` (`show_id`),
  KEY `idx_registration_id` (`registration_id`),
  KEY `idx_reported_by_user_id` (`reported_by_user_id`),
  KEY `idx_assigned_to_admin_id` (`assigned_to_admin_id`),
  KEY `idx_resolved_by_user_id` (`resolved_by_user_id`),
  KEY `idx_status` (`status`),
  KEY `idx_priority` (`priority`),
  KEY `idx_conflict_type` (`conflict_type`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_judging_conflicts_show_id` FOREIGN KEY (`show_id`) REFERENCES `shows` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_judging_conflicts_registration_id` FOREIGN KEY (`registration_id`) REFERENCES `registrations` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_judging_conflicts_reported_by_user_id` FOREIGN KEY (`reported_by_user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_judging_conflicts_assigned_to_admin_id` FOREIGN KEY (`assigned_to_admin_id`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_judging_conflicts_resolved_by_user_id` FOREIGN KEY (`resolved_by_user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create conflict comments table for tracking communication about conflicts
CREATE TABLE IF NOT EXISTS `judging_conflict_comments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `conflict_id` int(11) NOT NULL,
  `user_id` int(10) UNSIGNED NOT NULL,
  `comment` text NOT NULL,
  `is_internal` tinyint(1) NOT NULL DEFAULT 0 COMMENT 'Whether this comment is internal (admin/coordinator only)',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_conflict_id` (`conflict_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_created_at` (`created_at`),
  CONSTRAINT `fk_judging_conflict_comments_conflict_id` FOREIGN KEY (`conflict_id`) REFERENCES `judging_conflicts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_judging_conflict_comments_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Create conflict attachments table for supporting documents/images
CREATE TABLE IF NOT EXISTS `judging_conflict_attachments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `conflict_id` int(11) NOT NULL,
  `user_id` int(10) UNSIGNED NOT NULL,
  `filename` varchar(255) NOT NULL,
  `original_filename` varchar(255) NOT NULL,
  `file_path` varchar(500) NOT NULL,
  `file_size` int(11) NOT NULL,
  `mime_type` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_conflict_id` (`conflict_id`),
  KEY `idx_user_id` (`user_id`),
  CONSTRAINT `fk_judging_conflict_attachments_conflict_id` FOREIGN KEY (`conflict_id`) REFERENCES `judging_conflicts` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_judging_conflict_attachments_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert initial conflict resolution settings
INSERT IGNORE INTO `settings` (`name`, `value`, `description`) VALUES
('conflict_auto_detection_enabled', '1', 'Enable automatic conflict detection'),
('conflict_score_variance_threshold', '20', 'Percentage variance threshold for score discrepancy detection'),
('conflict_notification_enabled', '1', 'Enable notifications for new conflicts'),
('conflict_escalation_hours', '48', 'Hours before unresolved conflicts are escalated'),
('conflict_owner_reporting_enabled', '1', 'Allow vehicle owners to report conflicts'),
('conflict_owner_reporting_time_limit', '72', 'Hours after results posting that owners can report conflicts');