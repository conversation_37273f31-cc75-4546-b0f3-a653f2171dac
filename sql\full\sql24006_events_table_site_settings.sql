
-- --------------------------------------------------------

--
-- Table structure for table `site_settings`
--
-- Creation: Jun 23, 2025 at 08:30 PM
--

CREATE TABLE `site_settings` (
  `id` int(10) UNSIGNED NOT NULL,
  `setting_key` varchar(255) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `setting_type` enum('text','html','json','image','color','boolean') NOT NULL DEFAULT 'text',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- RELATIONSHIPS FOR TABLE `site_settings`:
--

--
-- Dumping data for table `site_settings`
--

INSERT INTO `site_settings` (`id`, `setting_key`, `setting_value`, `setting_type`, `created_at`, `updated_at`) VALUES
(1, 'footer_content', '<div class=\"container\"><div class=\"row\"><div class=\"col-md-4\"><h5>About Us</h5><p>Events and Shows Management System helps car enthusiasts organize and participate in shows and events.</p></div><div class=\"col-md-4\"><h5>Quick Links</h5><ul class=\"list-unstyled\"><li><a href=\"#\">Home</a></li><li><a href=\"#\">Shows</a></li><li><a href=\"#\">Register</a></li><li><a href=\"#\">Contact</a></li></ul></div><div class=\"col-md-4\"><h5>Contact</h5><p>Email: <EMAIL><br>Phone: (*************</p></div></div><div class=\"row\"><div class=\"col-12 text-center mt-3\"><p>&copy; 2025 Events and Shows Management System. All rights reserved.</p></div></div></div>', 'html', '2025-05-19 20:44:12', '2025-05-19 20:44:12'),
(2, 'footer_enabled', '1', 'boolean', '2025-05-19 20:44:12', '2025-05-19 20:44:12'),
(3, 'footer_custom_css', '', 'text', '2025-05-19 20:44:12', '2025-05-19 20:44:12');
