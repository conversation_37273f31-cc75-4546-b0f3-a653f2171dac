
-- --------------------------------------------------------

--
-- Table structure for table `form_submissions`
--
-- Creation: Jun 23, 2025 at 08:30 PM
--

CREATE TABLE `form_submissions` (
  `id` int(10) UNSIGNED NOT NULL,
  `template_id` int(11) NOT NULL,
  `user_id` int(10) UNSIGNED NOT NULL,
  `entity_type` enum('show','vehicle','user','other') NOT NULL,
  `entity_id` int(11) NOT NULL,
  `data` text NOT NULL,
  `status` enum('draft','submitted','approved','rejected') NOT NULL DEFAULT 'submitted',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- RELATIONSHIPS FOR TABLE `form_submissions`:
--
