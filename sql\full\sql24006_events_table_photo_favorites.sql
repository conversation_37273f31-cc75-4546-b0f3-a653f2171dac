
-- --------------------------------------------------------

--
-- Table structure for table `photo_favorites`
--
-- Creation: Aug 01, 2025 at 06:04 PM
-- Last update: Aug 01, 2025 at 01:13 PM
--

CREATE TABLE `photo_favorites` (
  `id` int(10) UNSIGNED NOT NULL,
  `photo_id` int(10) UNSIGNED NOT NULL,
  `user_id` int(10) UNSIGNED NOT NULL,
  `collection_name` varchar(100) DEFAULT 'My Favorites',
  `created_at` timestamp NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_uca1400_ai_ci;

--
-- RELATIONSHIPS FOR TABLE `photo_favorites`:
--   `photo_id`
--       `images` -> `id`
--   `user_id`
--       `users` -> `id`
--

--
-- Dumping data for table `photo_favorites`
--

INSERT INTO `photo_favorites` (`id`, `photo_id`, `user_id`, `collection_name`, `created_at`) VALUES
(1, 589, 3, 'My Favorites', '2025-08-01 13:13:31');
