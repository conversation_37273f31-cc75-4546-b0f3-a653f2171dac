<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tire Progress Bar Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
        }

        .demo-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        /* Tire Progress Bar Styles */
        .tire-progress-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
        }

        .tire-progress {
            position: relative;
            width: 120px;
            height: 120px;
            margin: 10px auto;
        }

        .tire-progress svg {
            transform: rotate(-90deg);
            width: 100%;
            height: 100%;
        }

        .tire-progress .tire-outer {
            fill: none;
            stroke: #2c3e50;
            stroke-width: 8;
            opacity: 0.3;
        }

        .tire-progress .tire-inner {
            fill: none;
            stroke: #34495e;
            stroke-width: 4;
            opacity: 0.2;
        }

        .tire-progress .tire-fill {
            fill: none;
            stroke: #e74c3c;
            stroke-width: 8;
            stroke-linecap: round;
            stroke-dasharray: 314.16; /* 2 * PI * 50 (radius) */
            stroke-dashoffset: 314.16;
            transition: stroke-dashoffset 0.3s ease;
        }

        .tire-progress .tire-center {
            fill: #34495e;
        }

        .tire-progress .tire-spokes {
            stroke: #7f8c8d;
            stroke-width: 2;
            opacity: 0.6;
        }

        .tire-progress-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 16px;
            font-weight: bold;
            color: #FFFFFF !important; /* White text for better visibility on black center */
            text-align: center;
            text-shadow: none !important; /* No shadow for clean white text */
        }

        .tire-progress-label {
            margin-top: 10px;
            font-size: 14px;
            color: #7f8c8d;
            text-align: center;
        }

        /* Animation for the tire rotation */
        @keyframes tire-spin {
            from { transform: rotate(-90deg); }
            to { transform: rotate(270deg); }
        }

        .tire-progress.spinning svg {
            animation: tire-spin 2s linear infinite;
        }

        /* Company name text around the tire */
        .tire-company-text {
            font-family: 'Arial Black', Arial, sans-serif;
            font-size: 12px; /* Made even larger for better readability */
            font-weight: bold;
            fill: #000000; /* Black as requested */
            opacity: 1; /* Full opacity for black text */
            letter-spacing: 0.5px; /* Keep reduced letter spacing for better fit */
        }

        /* Animation for the company text rotation */
        @keyframes company-text-spin {
            from { transform: rotate(-90deg); }
            to { transform: rotate(270deg); }
        }

        .tire-progress.spinning .tire-company-text {
            animation: company-text-spin 8s linear infinite; /* Much slower for better readability */
            transform-origin: 60px 60px;
        }

        .controls {
            margin: 20px 0;
        }

        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }

        .btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1>🏎️ Tire Progress Bar Demo</h1>
        <p>This is how the tire progress bar will look during image uploads!</p>
        
        <div class="tire-progress-container">
            <div class="tire-progress" id="tire-progress">
                <svg viewBox="0 0 120 120">
                    <!-- Define circular path for company name (between the rings) -->
                    <defs>
                        <path id="circle-path" d="M 60, 60 m -37, 0 a 37,37 0 1,1 74,0 a 37,37 0 1,1 -74,0" />
                    </defs>

                    <!-- Tire outer rim -->
                    <circle cx="60" cy="60" r="50" class="tire-outer"></circle>
                    <!-- Tire inner rim -->
                    <circle cx="60" cy="60" r="35" class="tire-inner"></circle>

                    <!-- Company name spinning around the tire -->
                    <text class="tire-company-text">
                        <textPath href="#circle-path" startOffset="0%">
                            • ROWAN ELITE RIDES •
                        </textPath>
                    </text>

                    <!-- Tire spokes -->
                    <g class="tire-spokes">
                        <line x1="60" y1="20" x2="60" y2="35" stroke="#7f8c8d" stroke-width="2"></line>
                        <line x1="60" y1="85" x2="60" y2="100" stroke="#7f8c8d" stroke-width="2"></line>
                        <line x1="20" y1="60" x2="35" y2="60" stroke="#7f8c8d" stroke-width="2"></line>
                        <line x1="85" y1="60" x2="100" y2="60" stroke="#7f8c8d" stroke-width="2"></line>
                        <line x1="35.86" y1="35.86" x2="46.46" y2="46.46" stroke="#7f8c8d" stroke-width="2"></line>
                        <line x1="73.54" y1="73.54" x2="84.14" y2="84.14" stroke="#7f8c8d" stroke-width="2"></line>
                        <line x1="84.14" y1="35.86" x2="73.54" y2="46.46" stroke="#7f8c8d" stroke-width="2"></line>
                        <line x1="46.46" y1="73.54" x2="35.86" y2="84.14" stroke="#7f8c8d" stroke-width="2"></line>
                    </g>
                    <!-- Progress fill -->
                    <circle cx="60" cy="60" r="50" class="tire-fill" id="tire-fill"></circle>
                    <!-- Center hub (increased to 20px for much better visibility) -->
                    <circle cx="60" cy="60" r="20" class="tire-center"></circle>
                </svg>
                <div class="tire-progress-text" id="tire-progress-text">0%</div>
            </div>
            <div class="tire-progress-label">Uploading Image...</div>
        </div>

        <div class="controls">
            <button class="btn" onclick="simulateUpload()">🚀 Simulate Upload</button>
            <button class="btn" onclick="simulateOverlay()">📱 Mobile Overlay</button>
            <button class="btn" onclick="resetProgress()">🔄 Reset</button>
            <button class="btn" onclick="toggleSpin()">⚙️ Toggle Spin</button>
        </div>

        <p><strong>Features:</strong></p>
        <ul style="text-align: left; max-width: 400px; margin: 0 auto;">
            <li>🎯 Real upload progress tracking</li>
            <li>🎨 Color changes: Red → Orange → Green</li>
            <li>⚙️ Spinning animation during upload</li>
            <li>🏁 Smooth tire filling animation</li>
            <li>🚗 Car enthusiast themed design</li>
        </ul>
    </div>

    <script>
        // Function to update tire progress
        function updateTireProgress(percent) {
            const tireFill = document.getElementById('tire-fill');
            const progressText = document.getElementById('tire-progress-text');
            
            // Calculate the stroke-dashoffset based on percentage
            // Full circumference is 314.16 (2 * PI * 50)
            const circumference = 314.16;
            const offset = circumference - (percent / 100) * circumference;
            
            // Update the tire fill
            tireFill.style.strokeDashoffset = offset;
            
            // Update the percentage text and ensure proper styling
            progressText.textContent = percent + '%';
            progressText.style.setProperty('color', '#FFFFFF', 'important'); // Force white color for better visibility
            progressText.style.setProperty('text-shadow', 'none', 'important'); // Remove any shadow for clean white text
            
            // Change tire color based on progress
            if (percent < 30) {
                tireFill.style.stroke = '#e74c3c'; // Red for low progress
            } else if (percent < 70) {
                tireFill.style.stroke = '#f39c12'; // Orange for medium progress
            } else {
                tireFill.style.stroke = '#27ae60'; // Green for high progress
            }
        }

        function simulateUpload() {
            const tireProgress = document.getElementById('tire-progress');
            tireProgress.classList.add('spinning');
            
            let progress = 0;
            const interval = setInterval(() => {
                progress += Math.random() * 15; // Random progress increments
                if (progress > 100) progress = 100;
                
                updateTireProgress(Math.round(progress));
                
                if (progress >= 100) {
                    clearInterval(interval);
                    setTimeout(() => {
                        tireProgress.classList.remove('spinning');
                    }, 500);
                }
            }, 200);
        }

        function resetProgress() {
            const tireProgress = document.getElementById('tire-progress');
            tireProgress.classList.remove('spinning');
            updateTireProgress(0);
        }

        function toggleSpin() {
            const tireProgress = document.getElementById('tire-progress');
            tireProgress.classList.toggle('spinning');
        }

        // Initialize with 0%
        updateTireProgress(0);
    </script>
</body>
</html>
