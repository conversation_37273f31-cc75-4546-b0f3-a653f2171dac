
-- --------------------------------------------------------

--
-- Structure for view `popular_event_photos`
--
DROP TABLE IF EXISTS `popular_event_photos`;

CREATE ALGORITHM=UNDEFINED DEFINER=`sql24006_forward-limpet`@`108.70.196.156` SQL SECURITY DEFINER VIEW `popular_event_photos`  AS SELECT `i`.`id` AS `id`, `i`.`entity_type` AS `entity_type`, `i`.`entity_id` AS `entity_id`, `i`.`user_id` AS `user_id`, `i`.`file_path` AS `file_path`, `i`.`thumbnail_path` AS `thumbnail_path`, `i`.`created_at` AS `created_at`, `epm`.`category` AS `category`, `epm`.`caption` AS `caption`, `u`.`name` AS `uploader_name`, coalesce(`pes`.`likes_count`,0) AS `likes_count`, coalesce(`pes`.`comments_count`,0) AS `comments_count`, coalesce(`pes`.`shares_count`,0) AS `shares_count`, coalesce(`pes`.`favorites_count`,0) AS `favorites_count`, coalesce(`pes`.`likes_count`,0) * 1.0 + coalesce(`pes`.`comments_count`,0) * 2.0 + coalesce(`pes`.`shares_count`,0) * 3.0 + coalesce(`pes`.`favorites_count`,0) * 1.5 AS `popularity_score` FROM (((`images` `i` left join `event_photo_metadata` `epm` on(`i`.`id` = `epm`.`image_id`)) left join `users` `u` on(`i`.`user_id` = `u`.`id`)) left join `photo_engagement_stats` `pes` on(`i`.`id` = `pes`.`photo_id`)) WHERE `i`.`entity_type` = 'event_photo' ORDER BY coalesce(`pes`.`likes_count`,0) * 1.0 + coalesce(`pes`.`comments_count`,0) * 2.0 + coalesce(`pes`.`shares_count`,0) * 3.0 + coalesce(`pes`.`favorites_count`,0) * 1.5 DESC, `i`.`created_at` DESC ;
