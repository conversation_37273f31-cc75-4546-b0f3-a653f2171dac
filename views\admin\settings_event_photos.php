<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid">
    <!-- <PERSON> Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-camera me-2"></i>
                        Event Photo Sharing Settings
                    </h1>
                    <p class="text-muted">Configure event photo sharing system settings and policies</p>
                </div>
                <div>
                    <a href="<?php echo BASE_URL; ?>/admin/settings" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i> Back to Settings
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Settings Navigation -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col">
                            <a href="<?php echo BASE_URL; ?>/admin/event_photo_admin_basic" class="btn btn-outline-primary w-100">
                                <i class="fas fa-cog me-2"></i>
                                Basic
                            </a>
                        </div>
                        <div class="col">
                            <a href="<?php echo BASE_URL; ?>/admin/event_photo_admin_location" class="btn btn-outline-info w-100">
                                <i class="fas fa-map-marker-alt me-2"></i>
                                Location
                            </a>
                        </div>
                        <div class="col">
                            <a href="<?php echo BASE_URL; ?>/admin/event_photo_categories_advanced" class="btn btn-outline-success w-100">
                                <i class="fas fa-tags me-2"></i>
                                Categories
                                <small class="d-block">Advanced</small>
                            </a>
                        </div>
                        <div class="col">
                            <a href="<?php echo BASE_URL; ?>/admin/event_photo_admin_storage" class="btn btn-outline-warning w-100">
                                <i class="fas fa-database me-2"></i>
                                Storage
                            </a>
                        </div>
                        <div class="col">
                            <a href="<?php echo BASE_URL; ?>/admin/event_photo_admin_moderation" class="btn btn-outline-danger w-100">
                                <i class="fas fa-shield-alt me-2"></i>
                                Moderation
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- System Status -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>
                        System Status
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-6">
                            <div class="text-center">
                                <div class="h4 mb-1 text-success">
                                    <?php echo ($data['system_enabled'] ?? true) ? 'Enabled' : 'Disabled'; ?>
                                </div>
                                <small class="text-muted">System Status</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <div class="h4 mb-1 text-info">
                                    <?php echo number_format($data['total_photos'] ?? 0); ?>
                                </div>
                                <small class="text-muted">Total Photos</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <div class="h4 mb-1 text-warning">
                                    <?php echo $data['storage_used'] ?? '0 MB'; ?>
                                </div>
                                <small class="text-muted">Storage Used</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <div class="h4 mb-1 text-primary">
                                    <?php echo number_format($data['active_events'] ?? 0); ?>
                                </div>
                                <small class="text-muted">Active Events</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line me-2"></i>
                        Recent Activity
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-6">
                            <div class="text-center">
                                <div class="h4 mb-1 text-success">
                                    <?php echo number_format($data['photos_today'] ?? 0); ?>
                                </div>
                                <small class="text-muted">Photos Today</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <div class="h4 mb-1 text-info">
                                    <?php echo number_format($data['photos_this_week'] ?? 0); ?>
                                </div>
                                <small class="text-muted">This Week</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <div class="h4 mb-1 text-warning">
                                    <?php echo number_format($data['pending_moderation'] ?? 0); ?>
                                </div>
                                <small class="text-muted">Pending Review</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center">
                                <div class="h4 mb-1 text-danger">
                                    <?php echo number_format($data['reported_photos'] ?? 0); ?>
                                </div>
                                <small class="text-muted">Reported</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Settings -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-sliders-h me-2"></i>
                        Quick Settings
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="<?php echo BASE_URL; ?>/admin/event_photo_admin_basic">
                        <input type="hidden" name="csrf_token" value="<?php echo $data['csrf_token'] ?? ''; ?>">
                        
                        <div class="row g-3">
                            <div class="col-md-3">
                                <div class="form-check form-switch mb-3" style="padding: 1rem; border: 1px solid #e9ecef; border-radius: 8px; background-color: #f8f9fa;">
                                    <input class="form-check-input" type="checkbox" id="systemEnabled" name="system_enabled"
                                           style="margin-left: 0 !important; margin-right: 0.75rem !important; margin-top: 0.25rem !important;"
                                           <?php echo ($data['settings']['enabled'] ?? true) ? 'checked' : ''; ?>>
                                    <label class="form-check-label" for="systemEnabled">
                                        <strong>Enable System</strong>
                                        <small class="d-block text-muted">Master switch for event photo sharing</small>
                                    </label>
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <label for="locationRadius" class="form-label">Location Radius</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="locationRadius" name="location_radius" 
                                           value="<?php echo $data['settings']['location_radius_miles'] ?? 1.0; ?>" 
                                           min="0.1" max="10" step="0.1">
                                    <span class="input-group-text">miles</span>
                                </div>
                            </div>
                            
                            <div class="col-md-3">
                                <label for="maxPhotos" class="form-label">Max Photos per User</label>
                                <input type="number" class="form-control" id="maxPhotos" name="max_photos_per_user" 
                                       value="<?php echo $data['settings']['max_photos_per_user_per_event'] ?? 50; ?>" 
                                       min="1" max="500">
                            </div>
                            
                            <div class="col-md-3">
                                <label for="maxFileSize" class="form-label">Max File Size</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="maxFileSize" name="max_file_size" 
                                           value="<?php echo $data['settings']['max_file_size_mb'] ?? 10; ?>" 
                                           min="1" max="50">
                                    <span class="input-group-text">MB</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-2"></i>
                                    Save Quick Settings
                                </button>
                            </div>
                            <div class="col-md-6 text-end">
                                <a href="<?php echo BASE_URL; ?>/admin/runEventPhotoStats" class="btn btn-outline-info">
                                    <i class="fas fa-sync me-2"></i>
                                    Refresh Statistics
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Photos -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-images me-2"></i>
                        Recent Event Photos
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($data['recent_photos'])): ?>
                        <div class="row g-3">
                            <?php foreach (array_slice($data['recent_photos'], 0, 8) as $photo): ?>
                                <div class="col-md-3">
                                    <div class="card">
                                        <img src="<?php echo BASE_URL . '/' . ($photo->thumbnail_path ?? $photo->file_path); ?>" 
                                             class="card-img-top" style="height: 150px; object-fit: cover;" 
                                             alt="Event photo">
                                        <div class="card-body p-2">
                                            <small>
                                                <?php if ($photo->event_type && $photo->event_id && $photo->event_name !== 'Unknown Event'): ?>
                                                    <a href="<?php echo BASE_URL; ?>/<?php echo $photo->event_type; ?>/view/<?php echo $photo->event_id; ?>"
                                                       class="text-decoration-none" title="View <?php echo ucfirst($photo->event_type); ?>">
                                                        <i class="fas fa-<?php echo $photo->event_type === 'event' ? 'calendar' : 'trophy'; ?> me-1"></i>
                                                        <?php echo htmlspecialchars($photo->event_name); ?>
                                                    </a>
                                                <?php else: ?>
                                                    <span class="text-muted">
                                                        <i class="fas fa-question-circle me-1"></i>
                                                        <?php echo $photo->event_name ?? 'Unknown Event'; ?>
                                                    </span>
                                                <?php endif; ?>
                                            </small>
                                            <br>
                                            <small class="text-muted">
                                                <i class="fas fa-clock me-1"></i>
                                                <?php echo timeAgo($photo->created_at); ?>
                                            </small>
                                            <div class="mt-1">
                                                <a href="<?php echo BASE_URL; ?>/image_editor/edit/<?php echo $photo->id; ?>"
                                                   class="btn btn-sm btn-outline-primary" title="Edit Photo">
                                                    <i class="fas fa-edit"></i> Edit
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        
                        <div class="text-center mt-3">
                            <a href="<?php echo BASE_URL; ?>/admin/viewAllEventPhotos" class="btn btn-outline-primary">
                                <i class="fas fa-images me-2"></i>
                                View All Event Photos
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-camera fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No event photos yet</h5>
                            <p class="text-muted">Event photos will appear here once users start sharing them.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.form-check-input:checked {
    background-color: #28a745;
    border-color: #28a745;
}
</style>

<?php require APPROOT . '/views/includes/footer.php'; ?>
