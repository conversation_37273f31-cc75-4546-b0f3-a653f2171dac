<?php
/**
 * Check Table Structures for Judging Conflicts Integration
 */

require_once 'config/config.php';
require_once 'core/Database.php';

echo "<h1>Table Structure Analysis</h1>\n";

try {
    $db = new Database();
    
    // Check settings table structure
    echo "<h2>Settings Table Structure</h2>\n";
    try {
        $db->query("DESCRIBE settings");
        $columns = $db->resultSet();
        
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>\n";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>\n";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>{$column->Field}</td>";
            echo "<td>{$column->Type}</td>";
            echo "<td>{$column->Null}</td>";
            echo "<td>{$column->Key}</td>";
            echo "<td>{$column->Default}</td>";
            echo "<td>{$column->Extra}</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
    } catch (Exception $e) {
        echo "<p style='color: red;'>Error checking settings table: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    }
    
    // Check shows table structure
    echo "<h2>Shows Table Structure</h2>\n";
    try {
        $db->query("DESCRIBE shows");
        $columns = $db->resultSet();
        
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>\n";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>\n";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>{$column->Field}</td>";
            echo "<td>{$column->Type}</td>";
            echo "<td>{$column->Null}</td>";
            echo "<td>{$column->Key}</td>";
            echo "<td>{$column->Default}</td>";
            echo "<td>{$column->Extra}</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
    } catch (Exception $e) {
        echo "<p style='color: red;'>Error checking shows table: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    }
    
    // Check registrations table structure
    echo "<h2>Registrations Table Structure</h2>\n";
    try {
        $db->query("DESCRIBE registrations");
        $columns = $db->resultSet();
        
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>\n";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>\n";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>{$column->Field}</td>";
            echo "<td>{$column->Type}</td>";
            echo "<td>{$column->Null}</td>";
            echo "<td>{$column->Key}</td>";
            echo "<td>{$column->Default}</td>";
            echo "<td>{$column->Extra}</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
    } catch (Exception $e) {
        echo "<p style='color: red;'>Error checking registrations table: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    }
    
    // Check users table structure
    echo "<h2>Users Table Structure</h2>\n";
    try {
        $db->query("DESCRIBE users");
        $columns = $db->resultSet();
        
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>\n";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>\n";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>{$column->Field}</td>";
            echo "<td>{$column->Type}</td>";
            echo "<td>{$column->Null}</td>";
            echo "<td>{$column->Key}</td>";
            echo "<td>{$column->Default}</td>";
            echo "<td>{$column->Extra}</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
    } catch (Exception $e) {
        echo "<p style='color: red;'>Error checking users table: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    }
    
    // Check scores table structure
    echo "<h2>Scores Table Structure</h2>\n";
    try {
        $db->query("DESCRIBE scores");
        $columns = $db->resultSet();
        
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>\n";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>\n";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>{$column->Field}</td>";
            echo "<td>{$column->Type}</td>";
            echo "<td>{$column->Null}</td>";
            echo "<td>{$column->Key}</td>";
            echo "<td>{$column->Default}</td>";
            echo "<td>{$column->Extra}</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";
    } catch (Exception $e) {
        echo "<p style='color: red;'>Error checking scores table: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    }
    
    // Check existing settings
    echo "<h2>Current Settings</h2>\n";
    try {
        $db->query("SELECT * FROM settings ORDER BY name");
        $settings = $db->resultSet();
        
        if (empty($settings)) {
            echo "<p>No settings found in the database.</p>\n";
        } else {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>\n";
            echo "<tr><th>Name</th><th>Value</th></tr>\n";
            foreach ($settings as $setting) {
                echo "<tr>";
                echo "<td>{$setting->name}</td>";
                echo "<td>" . htmlspecialchars($setting->value) . "</td>";
                echo "</tr>\n";
            }
            echo "</table>\n";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>Error checking settings: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>Database connection failed:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

echo "<hr>\n";
echo "<p><a href='" . BASE_URL . "'>← Return to Home</a></p>\n";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 1000px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2 {
    color: #333;
}

table {
    width: 100%;
    margin: 10px 0;
}

th, td {
    padding: 8px;
    text-align: left;
    border: 1px solid #ddd;
}

th {
    background-color: #f2f2f2;
    font-weight: bold;
}

tr:nth-child(even) {
    background-color: #f9f9f9;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>