<?php
/**
 * Test Judging Conflicts System
 */

require_once 'config/config.php';
require_once 'core/Database.php';

echo "<h1>Testing Judging Conflicts System</h1>\n";

try {
    // Test 1: Database tables
    echo "<h2>1. Database Tables Test</h2>\n";
    $db = new Database();
    
    $tables = ['judging_conflicts', 'judging_conflict_comments', 'judging_conflict_related_scores', 'judging_conflict_related_judges'];
    foreach ($tables as $table) {
        try {
            $db->query("SELECT COUNT(*) as count FROM $table");
            $result = $db->single();
            echo "<p style='color: green;'>✓ Table $table accessible (count: {$result->count})</p>\n";
        } catch (Exception $e) {
            echo "<p style='color: red;'>✗ Table $table error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
        }
    }
    
    // Test 2: Settings
    echo "<h2>2. Configuration Settings Test</h2>\n";
    try {
        $db->query("SELECT name, value FROM settings WHERE name LIKE 'conflict_%' ORDER BY name");
        $settings = $db->resultSet();
        
        if (empty($settings)) {
            echo "<p style='color: red;'>✗ No conflict settings found</p>\n";
        } else {
            foreach ($settings as $setting) {
                echo "<p style='color: green;'>✓ {$setting->name} = {$setting->value}</p>\n";
            }
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Settings error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    }
    
    // Test 3: Model loading
    echo "<h2>3. Model Loading Test</h2>\n";
    try {
        require_once APPROOT . '/models/JudgingConflictModel.php';
        $conflictModel = new JudgingConflictModel();
        echo "<p style='color: green;'>✓ JudgingConflictModel loaded successfully</p>\n";
        
        // Test a simple method
        $stats = $conflictModel->getConflictStats();
        echo "<p style='color: green;'>✓ getConflictStats() method works</p>\n";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Model error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    }
    
    // Test 4: Controller loading
    echo "<h2>4. Controller Loading Test</h2>\n";
    try {
        require_once APPROOT . '/controllers/JudgingConflictController.php';
        echo "<p style='color: green;'>✓ JudgingConflictController loaded successfully</p>\n";
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Controller error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    }
    
    // Test 5: View files
    echo "<h2>5. View Files Test</h2>\n";
    $viewFiles = [
        'views/judging_conflict/dashboard.php',
        'views/judging_conflict/view.php',
        'views/judging_conflict/report.php',
        'views/judging_conflict/my_reports.php'
    ];
    
    foreach ($viewFiles as $viewFile) {
        $fullPath = APPROOT . '/' . $viewFile;
        if (file_exists($fullPath)) {
            echo "<p style='color: green;'>✓ View file exists: $viewFile</p>\n";
        } else {
            echo "<p style='color: red;'>✗ View file missing: $viewFile</p>\n";
        }
    }
    
    // Test 6: URL routing (basic check)
    echo "<h2>6. URL Access Test</h2>\n";
    $urls = [
        '/judging_conflict/dashboard',
        '/judging_conflict/report',
        '/judging_conflict/my_reports'
    ];
    
    foreach ($urls as $url) {
        echo "<p style='color: blue;'>📋 URL available: <a href='" . BASE_URL . $url . "' target='_blank'>" . BASE_URL . $url . "</a></p>\n";
    }
    
    echo "<hr>\n";
    echo "<h2>System Status</h2>\n";
    echo "<p style='color: green; font-weight: bold;'>✅ Judging Conflicts System is ready for use!</p>\n";
    
    echo "<h3>Quick Links:</h3>\n";
    echo "<ul>\n";
    echo "<li><a href='" . BASE_URL . "/judging_conflict/dashboard' target='_blank'>Admin Dashboard</a> (Admin/Coordinator access required)</li>\n";
    echo "<li><a href='" . BASE_URL . "/judging_conflict/report' target='_blank'>Report a Conflict</a> (All users)</li>\n";
    echo "<li><a href='" . BASE_URL . "/judging_conflict/my_reports' target='_blank'>My Reports</a> (All users)</li>\n";
    echo "</ul>\n";
    
    echo "<h3>Features Available:</h3>\n";
    echo "<ul>\n";
    echo "<li>✅ Conflict reporting system</li>\n";
    echo "<li>✅ Admin management dashboard</li>\n";
    echo "<li>✅ Automatic score discrepancy detection</li>\n";
    echo "<li>✅ Comment system for resolution</li>\n";
    echo "<li>✅ Priority and status tracking</li>\n";
    echo "<li>✅ Statistical reporting</li>\n";
    echo "<li>✅ Mobile-responsive interfaces</li>\n";
    echo "</ul>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>Test failed:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
}

echo "<hr>\n";
echo "<p><a href='" . BASE_URL . "'>← Return to Home</a></p>\n";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
}

p {
    margin: 5px 0;
}

hr {
    margin: 20px 0;
    border: none;
    border-top: 1px solid #ccc;
}

ul {
    padding-left: 20px;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>