
-- --------------------------------------------------------

--
-- Table structure for table `message_attachments`
--
-- Creation: Jul 16, 2025 at 07:00 PM
--

CREATE TABLE `message_attachments` (
  `id` int(11) NOT NULL,
  `message_id` int(11) NOT NULL,
  `filename` varchar(255) NOT NULL COMMENT 'Original filename',
  `stored_filename` varchar(255) NOT NULL COMMENT 'Filename on disk',
  `file_path` varchar(500) NOT NULL COMMENT 'Full path to file',
  `file_size` int(11) NOT NULL COMMENT 'File size in bytes',
  `mime_type` varchar(100) NOT NULL COMMENT 'MIME type',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- RELATIONSHIPS FOR TABLE `message_attachments`:
--
