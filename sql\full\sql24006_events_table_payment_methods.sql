
-- --------------------------------------------------------

--
-- Table structure for table `payment_methods`
--
-- Creation: Jun 23, 2025 at 08:30 PM
--

CREATE TABLE `payment_methods` (
  `id` int(10) UNSIGNED NOT NULL,
  `name` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `instructions` text DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `requires_approval` tinyint(1) DEFAULT 1,
  `is_online` tinyint(1) DEFAULT 0,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `is_admin_only` tinyint(1) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- RELATIONSHIPS FOR TABLE `payment_methods`:
--

--
-- Dumping data for table `payment_methods`
--

INSERT INTO `payment_methods` (`id`, `name`, `description`, `instructions`, `is_active`, `requires_approval`, `is_online`, `created_at`, `updated_at`, `is_admin_only`) VALUES
(1, 'PayPal', 'Pay online with PayPal', NULL, 1, 0, 1, '2025-05-19 15:24:53', '2025-06-11 17:16:44', 0),
(2, 'Credit Card', 'Pay with credit card', NULL, 1, 0, 1, '2025-05-19 15:24:53', '2025-05-19 15:24:53', 0),
(3, 'Cash', 'Pay with cash at the event', NULL, 1, 1, 0, '2025-05-19 15:24:53', '2025-05-19 15:24:53', 0),
(4, 'Check', 'Pay with check', NULL, 1, 1, 0, '2025-05-19 15:24:53', '2025-05-19 15:24:53', 0),
(5, 'CashApp', 'Pay with Cash App', 'Send payment to our Cash App ID. Include your registration ID in the notes.', 1, 1, 0, '2025-05-20 09:21:09', '2025-06-11 17:16:44', 0),
(6, 'Venmo', 'Pay with Venmo', 'Send payment to our Venmo ID. Include your registration ID in the notes.', 1, 1, 0, '2025-05-20 09:21:09', '2025-06-11 17:16:44', 0),
(7, 'Free', 'No payment required', 'This registration is free of charge.', 1, 0, 0, '2025-06-11 16:56:41', '2025-06-11 17:51:32', 1);
