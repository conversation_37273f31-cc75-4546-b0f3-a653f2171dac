
-- --------------------------------------------------------

--
-- Table structure for table `vehicle_metric_scores`
--
-- Creation: Jun 23, 2025 at 08:30 PM
--

CREATE TABLE `vehicle_metric_scores` (
  `id` int(11) NOT NULL,
  `vehicle_total_score_id` int(11) NOT NULL,
  `show_id` int(11) NOT NULL,
  `vehicle_id` int(11) NOT NULL,
  `registration_id` int(11) NOT NULL,
  `metric_id` int(11) NOT NULL,
  `metric_name` varchar(255) DEFAULT NULL,
  `raw_score` decimal(10,2) DEFAULT NULL,
  `max_score` decimal(10,2) DEFAULT NULL,
  `weight` decimal(10,4) DEFAULT NULL,
  `weighted_score` decimal(10,2) DEFAULT NULL,
  `age_weighted_score` decimal(10,2) DEFAULT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- RELATIONSHIPS FOR TABLE `vehicle_metric_scores`:
--

--
-- Dumping data for table `vehicle_metric_scores`
--

INSERT INTO `vehicle_metric_scores` (`id`, `vehicle_total_score_id`, `show_id`, `vehicle_id`, `registration_id`, `metric_id`, `metric_name`, `raw_score`, `max_score`, `weight`, `weighted_score`, `age_weighted_score`, `created_at`, `updated_at`) VALUES
(41, 140, 5, 2, 37, 218, 'Body Condition', 2.00, 10.00, 10.0000, 20.00, 24.00, '2025-06-05 17:47:38', '2025-06-05 17:47:38'),
(42, 140, 5, 2, 37, 219, 'Craftsmanship', 1.00, 10.00, 2.0000, 2.00, 2.40, '2025-06-05 17:47:38', '2025-06-05 17:47:38'),
(43, 140, 5, 2, 37, 220, 'Detail', 1.00, 10.00, 7.0000, 7.00, 8.40, '2025-06-05 17:47:38', '2025-06-05 17:47:38'),
(44, 140, 5, 2, 37, 221, 'Difficulty', 0.00, 10.00, 5.0000, 0.00, 0.00, '2025-06-05 17:47:38', '2025-06-05 17:47:38'),
(45, 140, 5, 2, 37, 222, 'Engine Bay', 0.00, 10.00, 8.0000, 0.00, 0.00, '2025-06-05 17:47:38', '2025-06-05 17:47:38'),
(46, 140, 5, 2, 37, 223, 'Interior', 0.00, 10.00, 8.0000, 0.00, 0.00, '2025-06-05 17:47:38', '2025-06-05 17:47:38'),
(47, 140, 5, 2, 37, 224, 'Originality', 0.00, 10.00, 2.0000, 0.00, 0.00, '2025-06-05 17:47:38', '2025-06-05 17:47:38'),
(48, 140, 5, 2, 37, 225, 'Paint Quality', 0.00, 10.00, 10.0000, 0.00, 0.00, '2025-06-05 17:47:38', '2025-06-05 17:47:38'),
(49, 140, 5, 2, 37, 226, 'Undercarriage', 0.00, 10.00, 7.0000, 0.00, 0.00, '2025-06-05 17:47:38', '2025-06-05 17:47:38'),
(50, 140, 5, 2, 37, 227, 'Wheels & Tires', 0.00, 10.00, 7.0000, 0.00, 0.00, '2025-06-05 17:47:38', '2025-06-05 17:47:38'),
(61, 250, 5, 2, 37, 218, 'Body Condition', 6.00, 10.00, 10.0000, 60.00, 72.00, '2025-06-05 20:31:52', '2025-06-05 20:31:52'),
(62, 250, 5, 2, 37, 219, 'Craftsmanship', 7.00, 10.00, 2.0000, 14.00, 16.80, '2025-06-05 20:31:52', '2025-06-05 20:31:52'),
(63, 250, 5, 2, 37, 220, 'Detail', 7.00, 10.00, 7.0000, 49.00, 58.80, '2025-06-05 20:31:52', '2025-06-05 20:31:52'),
(64, 250, 5, 2, 37, 221, 'Difficulty', 4.00, 10.00, 5.0000, 20.00, 24.00, '2025-06-05 20:31:52', '2025-06-05 20:31:52'),
(65, 250, 5, 2, 37, 222, 'Engine Bay', 3.00, 10.00, 8.0000, 24.00, 28.80, '2025-06-05 20:31:52', '2025-06-05 20:31:52'),
(66, 250, 5, 2, 37, 223, 'Interior', 8.00, 10.00, 8.0000, 64.00, 76.80, '2025-06-05 20:31:52', '2025-06-05 20:31:52'),
(67, 250, 5, 2, 37, 224, 'Originality', 7.00, 10.00, 2.0000, 14.00, 16.80, '2025-06-05 20:31:52', '2025-06-05 20:31:52'),
(68, 250, 5, 2, 37, 225, 'Paint Quality', 7.00, 10.00, 10.0000, 70.00, 84.00, '2025-06-05 20:31:52', '2025-06-05 20:31:52'),
(69, 250, 5, 2, 37, 226, 'Undercarriage', 9.00, 10.00, 7.0000, 63.00, 75.60, '2025-06-05 20:31:52', '2025-06-05 20:31:52'),
(70, 250, 5, 2, 37, 227, 'Wheels & Tires', 9.00, 10.00, 7.0000, 63.00, 75.60, '2025-06-05 20:31:52', '2025-06-05 20:31:52'),
(151, 251, 5, 2, 37, 218, 'Body Condition', 6.00, 10.00, 10.0000, 60.00, 72.00, '2025-06-08 08:14:55', '2025-06-08 08:14:55'),
(152, 251, 5, 2, 37, 219, 'Craftsmanship', 7.00, 10.00, 2.0000, 14.00, 16.80, '2025-06-08 08:14:55', '2025-06-08 08:14:55'),
(153, 251, 5, 2, 37, 220, 'Detail', 7.00, 10.00, 7.0000, 49.00, 58.80, '2025-06-08 08:14:55', '2025-06-08 08:14:55'),
(154, 251, 5, 2, 37, 221, 'Difficulty', 4.00, 10.00, 5.0000, 20.00, 24.00, '2025-06-08 08:14:55', '2025-06-08 08:14:55'),
(155, 251, 5, 2, 37, 222, 'Engine Bay', 3.00, 10.00, 8.0000, 24.00, 28.80, '2025-06-08 08:14:55', '2025-06-08 08:14:55'),
(156, 251, 5, 2, 37, 223, 'Interior', 8.00, 10.00, 8.0000, 64.00, 76.80, '2025-06-08 08:14:55', '2025-06-08 08:14:55'),
(157, 251, 5, 2, 37, 224, 'Originality', 7.00, 10.00, 2.0000, 14.00, 16.80, '2025-06-08 08:14:55', '2025-06-08 08:14:55'),
(158, 251, 5, 2, 37, 225, 'Paint Quality', 7.00, 10.00, 10.0000, 70.00, 84.00, '2025-06-08 08:14:55', '2025-06-08 08:14:55'),
(159, 251, 5, 2, 37, 226, 'Undercarriage', 9.00, 10.00, 7.0000, 63.00, 75.60, '2025-06-08 08:14:55', '2025-06-08 08:14:55'),
(160, 251, 5, 2, 37, 227, 'Wheels & Tires', 9.00, 10.00, 7.0000, 63.00, 75.60, '2025-06-08 08:14:55', '2025-06-08 08:14:55');
