
-- --------------------------------------------------------

--
-- Table structure for table `calendar_imports`
--
-- Creation: Jun 23, 2025 at 08:30 PM
--

CREATE TABLE `calendar_imports` (
  `id` int(10) UNSIGNED NOT NULL,
  `calendar_id` int(10) UNSIGNED NOT NULL,
  `source_type` enum('ical','facebook','csv') NOT NULL,
  `source_url` varchar(255) DEFAULT NULL,
  `source_file` varchar(255) DEFAULT NULL,
  `last_import` datetime DEFAULT NULL,
  `auto_update` tinyint(1) NOT NULL DEFAULT 0,
  `update_frequency` int(11) DEFAULT NULL COMMENT 'Update frequency in hours',
  `created_by` int(10) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- RELATIONSHIPS FOR TABLE `calendar_imports`:
--   `calendar_id`
--       `calendars` -> `id`
--   `created_by`
--       `users` -> `id`
--

--
-- Dumping data for table `calendar_imports`
--

INSERT INTO `calendar_imports` (`id`, `calendar_id`, `source_type`, `source_url`, `source_file`, `last_import`, `auto_update`, `update_frequency`, `created_by`, `created_at`, `updated_at`) VALUES
(1, 1, 'facebook', 'https://facebook.com/events/s/11th-annual-rowan-chamber-drag/1939431833554057/', NULL, '2025-06-16 21:58:02', 0, NULL, 3, '2025-06-17 01:58:02', '2025-06-17 01:58:02'),
(2, 1, 'facebook', 'https://facebook.com/events/s/11th-annual-rowan-chamber-drag/1939431833554057/', NULL, '2025-06-16 21:59:26', 0, NULL, 3, '2025-06-17 01:59:26', '2025-06-17 01:59:26'),
(3, 1, 'facebook', 'https://facebook.com/events/s/11th-annual-rowan-chamber-drag/1939431833554057/', NULL, '2025-06-16 22:00:09', 0, NULL, 3, '2025-06-17 02:00:09', '2025-06-17 02:00:09');
