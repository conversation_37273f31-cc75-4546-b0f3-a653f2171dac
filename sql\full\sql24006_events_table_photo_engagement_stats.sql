
-- --------------------------------------------------------

--
-- Table structure for table `photo_engagement_stats`
--
-- Creation: Aug 01, 2025 at 06:04 PM
-- Last update: Aug 01, 2025 at 08:24 PM
--

CREATE TABLE `photo_engagement_stats` (
  `photo_id` int(10) UNSIGNED NOT NULL,
  `likes_count` int(10) UNSIGNED DEFAULT 0,
  `comments_count` int(10) UNSIGNED DEFAULT 0,
  `shares_count` int(10) UNSIGNED DEFAULT 0,
  `favorites_count` int(10) UNSIGNED DEFAULT 0,
  `tags_count` int(10) UNSIGNED DEFAULT 0,
  `last_updated` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_uca1400_ai_ci;

--
-- RELATIONSHIPS FOR TABLE `photo_engagement_stats`:
--   `photo_id`
--       `images` -> `id`
--

--
-- Dumping data for table `photo_engagement_stats`
--

INSERT INTO `photo_engagement_stats` (`photo_id`, `likes_count`, `comments_count`, `shares_count`, `favorites_count`, `tags_count`, `last_updated`) VALUES
(576, 1, 0, 0, 0, 0, '2025-08-01 20:24:25'),
(578, 0, 0, 0, 0, 0, '2025-08-01 13:54:10'),
(582, 0, 0, 0, 0, 0, '2025-08-01 13:07:46'),
(587, 0, 0, 0, 0, 0, '2025-08-01 13:07:46'),
(589, 1, 3, 7, 1, 0, '2025-08-01 18:47:21');
