
-- --------------------------------------------------------

--
-- Table structure for table `calendar_club_members`
--
-- Creation: Jul 09, 2025 at 11:09 AM
--

CREATE TABLE `calendar_club_members` (
  `id` int(10) UNSIGNED NOT NULL,
  `club_id` int(10) UNSIGNED NOT NULL,
  `user_id` int(10) UNSIGNED NOT NULL,
  `role` enum('member','admin','owner') NOT NULL DEFAULT 'member',
  `joined_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- RELATIONSHIPS FOR TABLE `calendar_club_members`:
--   `club_id`
--       `calendar_clubs` -> `id`
--   `user_id`
--       `users` -> `id`
--

--
-- Dumping data for table `calendar_club_members`
--

INSERT INTO `calendar_club_members` (`id`, `club_id`, `user_id`, `role`, `joined_at`, `created_at`, `updated_at`) VALUES
(1, 1, 3, 'owner', '2025-06-21 19:53:35', '2025-06-21 19:53:35', '2025-06-21 19:53:35');
