
-- --------------------------------------------------------

--
-- Table structure for table `default_age_weights`
--
-- Creation: Jun 23, 2025 at 08:30 PM
--

CREATE TABLE `default_age_weights` (
  `id` int(11) NOT NULL,
  `min_year` int(11) NOT NULL,
  `max_year` int(11) NOT NULL,
  `weight` decimal(10,2) NOT NULL DEFAULT 1.00,
  `description` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- RELATIONSHIPS FOR TABLE `default_age_weights`:
--

--
-- Dumping data for table `default_age_weights`
--

INSERT INTO `default_age_weights` (`id`, `min_year`, `max_year`, `weight`, `description`, `created_at`, `updated_at`) VALUES
(1, 1900, 1939, 2.25, 'Antique', '2025-05-27 19:41:46', '2025-06-04 23:45:32'),
(2, 1940, 1959, 2.00, 'Vintage', '2025-05-27 19:41:46', '2025-06-04 23:45:23'),
(3, 1960, 1979, 1.75, 'Classic', '2025-05-27 19:41:47', '2025-06-04 23:45:14'),
(4, 1980, 1999, 1.50, 'Modern Classic', '2025-05-27 19:41:47', '2025-06-04 23:44:56'),
(5, 2000, 2015, 1.25, 'Contemporary', '2025-05-27 19:41:47', '2025-06-04 23:44:48');
