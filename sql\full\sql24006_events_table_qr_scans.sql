
-- --------------------------------------------------------

--
-- Table structure for table `qr_scans`
--
-- Creation: Jun 23, 2025 at 08:30 PM
--

CREATE TABLE `qr_scans` (
  `id` int(11) NOT NULL,
  `entity_type` varchar(50) NOT NULL COMMENT 'Type of entity (show, vehicle, registration, category)',
  `entity_id` int(11) NOT NULL COMMENT 'ID of the entity',
  `user_id` int(11) DEFAULT NULL COMMENT 'ID of the user who scanned (if logged in)',
  `user_role` varchar(50) DEFAULT 'guest' COMMENT 'Role of the user who scanned',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP address of the scanner',
  `user_agent` text DEFAULT NULL COMMENT 'User agent of the scanner',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp() COMMENT 'When the scan occurred'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- RELATIONSHIPS FOR TABLE `qr_scans`:
--

--
-- Dumping data for table `qr_scans`
--

INSERT INTO `qr_scans` (`id`, `entity_type`, `entity_id`, `user_id`, `user_role`, `ip_address`, `user_agent`, `created_at`) VALUES
(1, 'registration', 32, NULL, 'guest', '**************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36', '2025-05-30 16:09:41'),
(2, 'registration', 32, 3, 'admin', '**************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36', '2025-05-30 16:10:32'),
(3, 'registration', 32, 4, 'judge', '**************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36', '2025-05-30 16:11:06'),
(4, 'registration', 32, 3, 'admin', '**************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36', '2025-05-30 16:12:33'),
(5, 'registration', 32, 3, 'admin', '**************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-31 20:31:24'),
(6, 'registration', 32, 3, 'admin', '**************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-31 20:53:04'),
(7, 'registration', 32, NULL, 'guest', '**************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36', '2025-05-31 20:54:25'),
(8, 'registration', 32, 3, 'admin', '**************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-05-31 20:54:55'),
(9, 'registration', 32, NULL, 'guest', '**************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36', '2025-05-31 20:56:15'),
(10, 'registration', 32, NULL, 'guest', '**************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36', '2025-05-31 22:25:40'),
(11, 'registration', 34, 3, 'admin', '**************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36', '2025-06-01 11:08:55'),
(12, 'registration', 32, 3, 'admin', '**************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-02 17:08:17'),
(13, 'registration', 36, 3, 'admin', '**************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-02 17:41:38'),
(14, 'registration', 36, 3, 'admin', '**************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-02 17:46:42'),
(15, 'registration', 36, 3, 'admin', '**************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-02 17:51:07'),
(16, 'registration', 36, 3, 'admin', '**************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-02 18:10:30'),
(17, 'registration', 37, 3, 'admin', '**************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-02 18:11:27'),
(18, 'registration', 37, NULL, 'guest', '**************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36', '2025-06-02 19:57:15'),
(19, 'registration', 37, 3, 'admin', '**************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-06-02 20:03:25'),
(20, 'registration', 37, NULL, 'guest', '**************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36', '2025-06-04 00:24:40'),
(21, 'registration', 37, NULL, 'guest', '**************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36', '2025-06-04 00:27:39'),
(22, 'registration', 37, 4, 'judge', '**************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36', '2025-06-04 00:28:13'),
(23, 'registration', 37, NULL, 'guest', '**************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36', '2025-06-04 11:24:43'),
(24, 'show', 5, 3, 'admin', '**************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Mobile Safari/537.36', '2025-07-07 19:40:41'),
(25, 'show', 5, NULL, 'guest', '**************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Mobile Safari/537.36', '2025-07-07 19:41:10'),
(26, 'registration', 37, NULL, 'guest', '**************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Mobile Safari/537.36', '2025-07-07 19:41:51'),
(27, 'registration', 37, NULL, 'guest', '**************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Mobile Safari/537.36', '2025-07-07 19:43:15'),
(28, 'registration', 37, NULL, 'guest', '**************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Mobile Safari/537.36', '2025-07-07 19:43:50'),
(29, 'registration', 37, 3, 'admin', '**************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-07 19:44:45'),
(30, 'registration', 60, NULL, 'guest', '**************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Mobile Safari/537.36', '2025-07-07 19:46:21'),
(31, 'registration', 60, NULL, 'guest', '**************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Mobile Safari/537.36', '2025-07-07 19:46:31'),
(32, 'registration', 60, NULL, 'guest', '**************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Mobile Safari/537.36', '2025-07-07 19:47:12'),
(33, 'registration', 60, NULL, 'guest', '**************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Mobile Safari/537.36', '2025-07-07 19:47:19'),
(34, 'registration', 60, NULL, 'guest', '**************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Mobile Safari/537.36', '2025-07-07 19:47:44'),
(35, 'registration', 59, NULL, 'guest', '**************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Mobile Safari/537.36', '2025-07-07 19:48:07'),
(36, 'registration', 60, NULL, 'guest', '**************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Mobile Safari/537.36', '2025-07-07 19:48:55'),
(37, 'registration', 60, NULL, 'guest', '**************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Mobile Safari/537.36', '2025-07-07 19:50:05'),
(38, 'registration', 59, NULL, 'guest', '**************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Mobile Safari/537.36', '2025-07-07 19:52:27'),
(39, 'registration', 59, NULL, 'guest', '**************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Mobile Safari/537.36', '2025-07-07 19:53:38'),
(40, 'registration', 59, 3, 'admin', '**************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', '2025-07-07 19:54:57'),
(41, 'registration', 59, NULL, 'guest', '**************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Mobile Safari/537.36', '2025-07-07 19:55:32'),
(42, 'registration', 59, NULL, 'guest', '**************', 'Mozilla/5.0 (Android 15; Mobile; rv:140.0) Gecko/140.0 Firefox/140.0', '2025-07-07 20:00:12'),
(43, 'registration', 37, NULL, 'guest', '**************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Mobile Safari/537.36', '2025-07-08 00:00:05'),
(44, 'registration', 37, NULL, 'guest', '**************', 'Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Mobile Safari/537.36', '2025-07-08 00:07:31');
