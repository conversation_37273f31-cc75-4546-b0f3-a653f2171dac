<?php
/**
 * Test Engagement Controller directly
 */

// Include the necessary files
require_once '../config/config.php';
require_once '../core/Database.php';
require_once '../helpers/session_helper.php';

// Start session and check admin access
session_start();
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'admin') {
    die('Access denied. Admin access required.');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Test Engagement Controller</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>Test Engagement Controller</h1>
    
    <?php
    try {
        $db = new Database();
        
        echo "<div class='info'>Testing engagement data retrieval...</div>\n";
        
        // Test photo ID 576 (the one that was failing)
        $photoId = 576;
        
        echo "<h3>Testing Photo ID: $photoId</h3>";
        
        // Check if photo exists
        $db->query("SELECT * FROM images WHERE id = :photo_id");
        $db->bind(':photo_id', $photoId);
        $photo = $db->single();
        
        if ($photo) {
            echo "<div class='success'>✓ Photo $photoId exists</div>\n";
            echo "<div class='info'>Entity Type: {$photo->entity_type}, Entity ID: {$photo->entity_id}</div>\n";
        } else {
            echo "<div class='error'>✗ Photo $photoId does not exist</div>\n";
        }
        
        // Check engagement stats
        $db->query("SELECT * FROM photo_engagement_stats WHERE photo_id = :photo_id");
        $db->bind(':photo_id', $photoId);
        $stats = $db->single();
        
        if ($stats) {
            echo "<div class='success'>✓ Engagement stats exist for photo $photoId</div>\n";
            echo "<div class='info'>Likes: {$stats->likes_count}, Comments: {$stats->comments_count}, Shares: {$stats->shares_count}, Favorites: {$stats->favorites_count}</div>\n";
        } else {
            echo "<div class='error'>✗ No engagement stats for photo $photoId</div>\n";
        }
        
        // Test the actual engagement controller logic
        echo "<h3>Testing Engagement Controller Logic</h3>";
        
        // Simulate what the controller does
        $currentUserId = $_SESSION['user_id'];
        
        // Get engagement stats
        $db->query('SELECT * FROM photo_engagement_stats WHERE photo_id = :photo_id');
        $db->bind(':photo_id', $photoId);
        $stats = $db->single();
        
        // Get user's engagement status if logged in
        $userEngagement = [
            'liked' => false,
            'favorited' => false
        ];
        
        if ($currentUserId) {
            // Check if user liked
            $db->query('SELECT id FROM photo_likes WHERE photo_id = :photo_id AND user_id = :user_id');
            $db->bind(':photo_id', $photoId);
            $db->bind(':user_id', $currentUserId);
            $userEngagement['liked'] = $db->single() ? true : false;
            
            // Check if user favorited
            $db->query('SELECT id FROM photo_favorites WHERE photo_id = :photo_id AND user_id = :user_id');
            $db->bind(':photo_id', $photoId);
            $db->bind(':user_id', $currentUserId);
            $userEngagement['favorited'] = $db->single() ? true : false;
        }
        
        $response = [
            'success' => true,
            'stats' => $stats ?: (object)[
                'likes_count' => 0,
                'comments_count' => 0,
                'shares_count' => 0,
                'favorites_count' => 0,
                'tags_count' => 0
            ],
            'user_engagement' => $userEngagement
        ];
        
        echo "<div class='success'>✓ Controller logic completed successfully</div>\n";
        echo "<h4>Response Data:</h4>";
        echo "<pre>" . json_encode($response, JSON_PRETTY_PRINT) . "</pre>";
        
        // Test the actual API endpoint
        echo "<h3>Testing API Endpoint</h3>";
        echo "<div class='info'>Making AJAX request to engagement endpoint...</div>\n";
        
        ?>
        <script>
        fetch('<?php echo BASE_URL; ?>/engagement/getEngagementData/<?php echo $photoId; ?>')
            .then(response => response.json())
            .then(data => {
                console.log('API Response:', data);
                document.getElementById('api-result').innerHTML = 
                    '<div class="success">✓ API call successful</div>' +
                    '<pre>' + JSON.stringify(data, null, 2) + '</pre>';
            })
            .catch(error => {
                console.error('API Error:', error);
                document.getElementById('api-result').innerHTML = 
                    '<div class="error">✗ API call failed: ' + error.message + '</div>';
            });
        </script>
        
        <div id="api-result">
            <div class="info">Loading API response...</div>
        </div>
        
        <?php
        
        // Test all photos in the gallery
        echo "<h3>Testing All Gallery Photos</h3>";
        
        $db->query("
            SELECT i.id, i.entity_type, i.entity_id, pes.likes_count, pes.comments_count 
            FROM images i 
            LEFT JOIN photo_engagement_stats pes ON i.id = pes.photo_id 
            WHERE i.entity_type = 'event_photo' AND i.entity_id = 5
            ORDER BY i.id
        ");
        
        $allPhotos = $db->resultSet();
        
        echo "<table border='1' cellpadding='5'>";
        echo "<tr><th>Photo ID</th><th>Entity Type</th><th>Entity ID</th><th>Likes</th><th>Comments</th><th>API Test</th></tr>";
        
        foreach ($allPhotos as $photo) {
            echo "<tr>";
            echo "<td>{$photo->id}</td>";
            echo "<td>{$photo->entity_type}</td>";
            echo "<td>{$photo->entity_id}</td>";
            echo "<td>" . ($photo->likes_count ?? 'NULL') . "</td>";
            echo "<td>" . ($photo->comments_count ?? 'NULL') . "</td>";
            echo "<td><button onclick=\"testPhoto({$photo->id})\">Test API</button></td>";
            echo "</tr>";
        }
        
        echo "</table>";
        
        ?>
        
        <script>
        function testPhoto(photoId) {
            fetch('<?php echo BASE_URL; ?>/engagement/getEngagementData/' + photoId)
                .then(response => response.json())
                .then(data => {
                    alert('Photo ' + photoId + ' API Response:\n' + JSON.stringify(data, null, 2));
                })
                .catch(error => {
                    alert('Photo ' + photoId + ' API Error: ' + error.message);
                });
        }
        </script>
        
        <?php
        
    } catch (Exception $e) {
        echo "<div class='error'>Error: " . htmlspecialchars($e->getMessage()) . "</div>\n";
        echo "<div class='error'>Stack trace:</div>\n";
        echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>\n";
    }
    ?>
    
    <p><a href="<?php echo BASE_URL; ?>/image_editor/eventGallery/show/5">← Back to Gallery</a></p>
</body>
</html>
