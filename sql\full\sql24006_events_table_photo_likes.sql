
-- --------------------------------------------------------

--
-- Table structure for table `photo_likes`
--
-- Creation: Aug 01, 2025 at 06:04 PM
-- Last update: Aug 01, 2025 at 08:24 PM
--

CREATE TABLE `photo_likes` (
  `id` int(10) UNSIGNED NOT NULL,
  `photo_id` int(10) UNSIGNED NOT NULL,
  `user_id` int(10) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_uca1400_ai_ci;

--
-- RELATIONSHIPS FOR TABLE `photo_likes`:
--   `photo_id`
--       `images` -> `id`
--   `user_id`
--       `users` -> `id`
--

--
-- Dumping data for table `photo_likes`
--

INSERT INTO `photo_likes` (`id`, `photo_id`, `user_id`, `created_at`) VALUES
(3, 589, 3, '2025-08-01 18:47:21'),
(4, 576, 3, '2025-08-01 20:24:25');
