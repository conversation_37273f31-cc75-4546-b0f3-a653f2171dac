
-- --------------------------------------------------------

--
-- Table structure for table `vehicles`
--
-- Creation: Jul 10, 2025 at 01:46 PM
--

CREATE TABLE `vehicles` (
  `id` int(10) UNSIGNED NOT NULL,
  `owner_id` int(10) UNSIGNED NOT NULL,
  `make` varchar(255) NOT NULL,
  `model` varchar(255) NOT NULL,
  `year` int(11) NOT NULL,
  `color` varchar(255) DEFAULT NULL,
  `license_plate` varchar(50) DEFAULT NULL,
  `vin` varchar(50) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `primary_image` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `template_id` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- RELATIONSHIPS FOR TABLE `vehicles`:
--   `owner_id`
--       `users` -> `id`
--

--
-- Dumping data for table `vehicles`
--

INSERT INTO `vehicles` (`id`, `owner_id`, `make`, `model`, `year`, `color`, `license_plate`, `vin`, `description`, `primary_image`, `created_at`, `updated_at`, `template_id`) VALUES
(2, 3, 'Jeep', 'Wrangler', 2008, 'White', 'RBBRDUK', '', 'Rubber Duck - The Move Convoy 1978 / Back the Blue Jeep', 'vehicle_2_6838f2a7f1d79.jpg', '2025-05-29 23:25:29', '2025-05-30 13:16:45', NULL),
(19, 12, 'Toyota', 'Supra', 1994, 'White', 'PQR-1234', NULL, 'Concours quality restoration, multiple show winner.', NULL, '2025-06-09 00:03:49', '2025-06-09 00:03:49', NULL),
(24, 210, 'Jeep', 'liberty', 2007, 'red', 'redjeep', '', '', NULL, '2025-06-13 14:11:35', '2025-06-13 14:11:35', NULL);
