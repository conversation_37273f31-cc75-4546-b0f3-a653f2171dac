
-- --------------------------------------------------------

--
-- Table structure for table `shows_backup`
--
-- Creation: Jun 23, 2025 at 08:30 PM
--

CREATE TABLE `shows_backup` (
  `id` int(10) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `location` varchar(255) NOT NULL,
  `start_date` date NOT NULL,
  `end_date` date NOT NULL,
  `registration_start` date NOT NULL,
  `registration_end` date NOT NULL,
  `coordinator_id` int(10) UNSIGNED DEFAULT NULL,
  `status` enum('draft','published','completed','cancelled') NOT NULL DEFAULT 'draft',
  `fan_voting_enabled` tinyint(1) NOT NULL DEFAULT 1,
  `banner_image` varchar(255) DEFAULT NULL,
  `form_template_id` int(11) DEFAULT NULL,
  `registration_fee` decimal(10,2) NOT NULL DEFAULT 0.00,
  `is_free` tinyint(1) NOT NULL DEFAULT 0,
  `listing_fee` decimal(10,2) NOT NULL DEFAULT 0.00,
  `listing_paid` tinyint(1) NOT NULL DEFAULT 0,
  `featured_image_id` int(10) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `custom_field_1` varchar(255) DEFAULT NULL,
  `custom_field_2` text DEFAULT NULL,
  `custom_field_3` text DEFAULT NULL,
  `custom_field_4` decimal(10,2) DEFAULT NULL,
  `custom_field_5` tinyint(1) DEFAULT 0,
  `custom_field_6` varchar(100) DEFAULT NULL,
  `custom_field_7` time DEFAULT NULL,
  `custom_field_8` varchar(20) DEFAULT NULL,
  `custom_field_9` int(11) DEFAULT NULL,
  `custom_field_10` varchar(255) DEFAULT NULL,
  `template_id` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- RELATIONSHIPS FOR TABLE `shows_backup`:
--

--
-- Dumping data for table `shows_backup`
--

INSERT INTO `shows_backup` (`id`, `name`, `description`, `location`, `start_date`, `end_date`, `registration_start`, `registration_end`, `coordinator_id`, `status`, `fan_voting_enabled`, `banner_image`, `form_template_id`, `registration_fee`, `is_free`, `listing_fee`, `listing_paid`, `featured_image_id`, `created_at`, `updated_at`, `custom_field_1`, `custom_field_2`, `custom_field_3`, `custom_field_4`, `custom_field_5`, `custom_field_6`, `custom_field_7`, `custom_field_8`, `custom_field_9`, `custom_field_10`, `template_id`) VALUES
(5, 'Name of show', 'This is a description of a carRoll up to the biggest auto bash of the year and feast your eyes on everything from classic roadsters to souped-up tuners.  We\'ve got chrome, curves, and horsepower galore, with something to rev everyone\'s engine.  Check out the off-road rigs tearing up the dirt track and admire the sleek lines of vintage sports cars. Bikes, trucks, and even custom creations will be on display, so don\'t miss out!', 'Salisbury nc', '2025-06-04', '2025-06-04', '2025-05-28', '2025-06-02', 5, 'published', 1, NULL, NULL, 0.00, 1, 0.00, 0, NULL, '2025-06-01 11:01:03', '2025-06-04 13:04:14', NULL, NULL, NULL, NULL, 0, '', NULL, '', NULL, NULL, NULL);
