
-- --------------------------------------------------------

--
-- Table structure for table `email_templates`
--
-- Creation: Jul 17, 2025 at 12:33 PM
--

CREATE TABLE `email_templates` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `subject` varchar(255) NOT NULL,
  `body` text NOT NULL,
  `template_variables` text DEFAULT NULL,
  `category` varchar(50) DEFAULT 'general',
  `is_active` tinyint(1) DEFAULT 1,
  `created_by` int(11) DEFAULT 1,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_uca1400_ai_ci;

--
-- RELATIONSHIPS FOR TABLE `email_templates`:
--

--
-- Dumping data for table `email_templates`
--

INSERT INTO `email_templates` (`id`, `name`, `subject`, `body`, `template_variables`, `category`, `is_active`, `created_by`, `created_at`, `updated_at`) VALUES
(1, 'Welcome Response', 'Welcome to Events and Shows Platform', 'Hello,\n\nThank you for contacting us regarding {{subject}}.\n\nWe have received your message and will respond within 24 hours.\n\nBest regards,\n{{admin_name}}\nEvents and Shows Platform', 'subject,admin_name,date,ticket_number', 'general', 1, 1, '2025-07-17 12:33:44', '2025-07-17 12:33:44'),
(2, 'Event Information', 'Re: {{subject}} - Event Information', 'Hello,\n\nThank you for your inquiry about our events.\n\nHere is the information you requested:\n\n[Please add specific event details here]\n\nIf you have any other questions, please do not hesitate to ask.\n\nBest regards,\n{{admin_name}}\nEvents and Shows Platform', 'subject,admin_name,date,ticket_number', 'events', 1, 1, '2025-07-17 12:33:44', '2025-07-17 12:33:44'),
(3, 'Registration Confirmation', 'Registration Confirmation - {{subject}}', 'Hello,\n\nYour registration has been confirmed for the event.\n\nTicket Number: {{ticket_number}}\nDate: {{date}}\n\nWe look forward to seeing you at the event!\n\nBest regards,\n{{admin_name}}\nEvents and Shows Platform', 'subject,admin_name,date,ticket_number', 'registration', 1, 1, '2025-07-17 12:33:44', '2025-07-17 12:33:44'),
(4, 'Follow Up Required', 'Re: {{subject}} - Follow Up Required', 'Hello,\n\nWe are following up on your previous message regarding {{subject}}.\n\nPlease let us know if you need any additional assistance.\n\nBest regards,\n{{admin_name}}\nEvents and Shows Platform', 'subject,admin_name,date,ticket_number', 'followup', 1, 1, '2025-07-17 12:33:44', '2025-07-17 12:33:44');
