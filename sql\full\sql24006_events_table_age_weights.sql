
-- --------------------------------------------------------

--
-- Table structure for table `age_weights`
--
-- Creation: Jul 10, 2025 at 01:46 PM
--

CREATE TABLE `age_weights` (
  `id` int(10) UNSIGNED NOT NULL,
  `show_id` int(10) UNSIGNED NOT NULL,
  `min_age` int(11) NOT NULL,
  `max_age` int(11) NOT NULL,
  `multiplier` decimal(5,2) NOT NULL DEFAULT 1.00,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- RELATIONSHIPS FOR TABLE `age_weights`:
--

--
-- Dumping data for table `age_weights`
--

INSERT INTO `age_weights` (`id`, `show_id`, `min_age`, `max_age`, `multiplier`, `created_at`, `updated_at`) VALUES
(54, 5, 2000, 2015, 1.20, '2025-06-05 12:54:46', '2025-06-05 16:33:41'),
(55, 5, 1980, 1999, 1.50, '2025-06-05 12:54:46', '2025-06-05 12:54:46'),
(56, 5, 1960, 1979, 1.70, '2025-06-05 12:54:46', '2025-06-05 14:08:18'),
(57, 5, 1940, 1959, 2.00, '2025-06-05 12:54:46', '2025-06-05 12:54:46'),
(58, 5, 1900, 1939, 2.20, '2025-06-05 12:54:46', '2025-06-05 14:08:32'),
(59, 7, 1900, 1939, 2.25, '2025-06-08 13:33:42', '2025-06-08 13:33:42'),
(60, 7, 1940, 1959, 2.00, '2025-06-08 13:33:42', '2025-06-08 13:33:42'),
(61, 7, 1960, 1979, 1.75, '2025-06-08 13:33:42', '2025-06-08 13:33:42'),
(62, 7, 1980, 1999, 1.50, '2025-06-08 13:33:42', '2025-06-08 13:33:42'),
(63, 7, 2000, 2015, 1.25, '2025-06-08 13:33:42', '2025-06-08 13:33:42'),
(64, 8, 1900, 1939, 2.25, '2025-06-08 13:37:24', '2025-06-08 13:37:24'),
(65, 8, 1940, 1959, 2.00, '2025-06-08 13:37:24', '2025-06-08 13:37:24'),
(66, 8, 1960, 1979, 1.75, '2025-06-08 13:37:24', '2025-06-08 13:37:24'),
(67, 8, 1980, 1999, 1.50, '2025-06-08 13:37:24', '2025-06-08 13:37:24'),
(68, 8, 2000, 2015, 1.25, '2025-06-08 13:37:24', '2025-06-08 13:37:24'),
(71, 9, 1900, 1939, 2.25, '2025-06-08 13:38:33', '2025-06-08 13:38:33'),
(72, 9, 1940, 1959, 2.00, '2025-06-08 13:38:33', '2025-06-08 13:38:33'),
(73, 9, 1960, 1979, 1.75, '2025-06-08 13:38:33', '2025-06-08 13:38:33'),
(74, 9, 1980, 1999, 1.50, '2025-06-08 13:38:33', '2025-06-08 13:38:33'),
(75, 9, 2000, 2015, 1.25, '2025-06-08 13:38:33', '2025-06-08 13:38:33'),
(76, 10, 2000, 2015, 1.25, '2025-06-08 13:44:57', '2025-06-08 13:44:57'),
(77, 10, 1980, 1999, 1.50, '2025-06-08 13:44:57', '2025-06-08 13:44:57'),
(78, 10, 1960, 1979, 1.75, '2025-06-08 13:44:57', '2025-06-08 13:44:57'),
(79, 10, 1940, 1959, 2.00, '2025-06-08 13:44:57', '2025-06-08 13:44:57'),
(80, 10, 1900, 1939, 2.25, '2025-06-08 13:44:57', '2025-06-08 13:44:57'),
(81, 11, 1900, 1939, 2.25, '2025-06-09 00:01:07', '2025-06-09 00:01:07'),
(82, 11, 1940, 1959, 2.00, '2025-06-09 00:01:07', '2025-06-09 00:01:07'),
(83, 11, 1960, 1979, 1.75, '2025-06-09 00:01:07', '2025-06-09 00:01:07'),
(84, 11, 1980, 1999, 1.50, '2025-06-09 00:01:07', '2025-06-09 00:01:07'),
(85, 11, 2000, 2015, 1.25, '2025-06-09 00:01:07', '2025-06-09 00:01:07'),
(88, 12, 1900, 1939, 2.25, '2025-06-09 00:02:45', '2025-06-09 00:02:45'),
(89, 12, 1940, 1959, 2.00, '2025-06-09 00:02:45', '2025-06-09 00:02:45'),
(90, 12, 1960, 1979, 1.75, '2025-06-09 00:02:45', '2025-06-09 00:02:45'),
(91, 12, 1980, 1999, 1.50, '2025-06-09 00:02:45', '2025-06-09 00:02:45'),
(92, 12, 2000, 2015, 1.25, '2025-06-09 00:02:45', '2025-06-09 00:02:45'),
(95, 13, 1900, 1939, 2.25, '2025-06-09 00:03:01', '2025-06-09 00:03:01'),
(96, 13, 1940, 1959, 2.00, '2025-06-09 00:03:01', '2025-06-09 00:03:01'),
(97, 13, 1960, 1979, 1.75, '2025-06-09 00:03:01', '2025-06-09 00:03:01'),
(98, 13, 1980, 1999, 1.50, '2025-06-09 00:03:01', '2025-06-09 00:03:01'),
(99, 13, 2000, 2015, 1.25, '2025-06-09 00:03:01', '2025-06-09 00:03:01'),
(100, 14, 2000, 2015, 1.25, '2025-06-30 18:43:40', '2025-06-30 18:43:40'),
(101, 14, 1980, 1999, 1.50, '2025-06-30 18:43:40', '2025-06-30 18:43:40'),
(102, 14, 1960, 1979, 1.75, '2025-06-30 18:43:40', '2025-06-30 18:43:40'),
(103, 14, 1940, 1959, 2.00, '2025-06-30 18:43:40', '2025-06-30 18:43:40'),
(104, 14, 1900, 1939, 2.25, '2025-06-30 18:43:40', '2025-06-30 18:43:40');
