
-- --------------------------------------------------------

--
-- Table structure for table `show_categories`
--
-- Creation: Jul 09, 2025 at 01:41 PM
--

CREATE TABLE `show_categories` (
  `id` int(10) UNSIGNED NOT NULL,
  `show_id` int(10) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `registration_fee` decimal(10,2) NOT NULL DEFAULT 0.00,
  `max_entries` int(11) NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `requires_judging` tinyint(1) NOT NULL DEFAULT 1,
  `display_order` int(11) NOT NULL DEFAULT 0
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- RELATIONSHIPS FOR TABLE `show_categories`:
--   `show_id`
--       `shows` -> `id`
--

--
-- Dumping data for table `show_categories`
--

INSERT INTO `show_categories` (`id`, `show_id`, `name`, `description`, `registration_fee`, `max_entries`, `created_at`, `updated_at`, `is_active`, `requires_judging`, `display_order`) VALUES
(152, 9, 'Classic Cars (Pre-1960)', 'Vehicles manufactured before 1960', 0.00, 0, '2025-06-08 13:38:33', '2025-06-08 13:38:33', 1, 1, 0),
(153, 9, 'Muscle Cars (1960-1979)', 'American muscle cars from the golden era', 0.00, 0, '2025-06-08 13:38:33', '2025-06-08 13:38:33', 1, 1, 0),
(154, 9, 'Modern Classics (1980-1999)', 'Vehicles from the 80s and 90s', 0.00, 0, '2025-06-08 13:38:33', '2025-06-08 13:38:33', 1, 1, 0),
(155, 9, 'Contemporary (2000-Present)', 'Modern vehicles from 2000 to present', 0.00, 0, '2025-06-08 13:38:33', '2025-06-08 13:38:33', 1, 1, 0),
(156, 9, 'European Imports', 'Vehicles manufactured in Europe', 0.00, 0, '2025-06-08 13:38:33', '2025-06-08 13:38:33', 1, 1, 0),
(157, 9, 'Asian Imports', 'Vehicles manufactured in Asia', 0.00, 0, '2025-06-08 13:38:33', '2025-06-08 13:38:33', 1, 1, 0),
(158, 9, 'Trucks &amp; SUVs', 'Pickup trucks, SUVs, and utility vehicles', 0.00, 0, '2025-06-08 13:38:33', '2025-06-08 13:38:33', 1, 1, 0),
(159, 9, 'Modified/Custom', 'Vehicles with significant modifications', 0.00, 0, '2025-06-08 13:38:33', '2025-06-08 13:38:33', 1, 1, 0),
(160, 9, 'Motorcycles', 'All types of motorcycles', 0.00, 0, '2025-06-08 13:38:33', '2025-06-08 13:38:33', 1, 1, 0),
(161, 14, 'Asian Imports', 'Vehicles manufactured in Asia', 0.00, 0, '2025-06-30 18:43:34', '2025-06-30 18:43:34', 1, 1, 0),
(162, 14, 'Classic Cars (Pre-1960)', 'Vehicles manufactured before 1960', 0.00, 0, '2025-06-30 18:43:34', '2025-06-30 18:43:34', 1, 1, 0),
(163, 14, 'Contemporary (2000-Present)', 'Modern vehicles from 2000 to present', 0.00, 0, '2025-06-30 18:43:34', '2025-06-30 18:43:34', 1, 1, 0),
(164, 14, 'European Imports', 'Vehicles manufactured in Europe', 0.00, 0, '2025-06-30 18:43:34', '2025-06-30 18:43:34', 1, 1, 0),
(165, 14, 'Modern Classics (1980-1999)', 'Vehicles from the 80s and 90s', 0.00, 0, '2025-06-30 18:43:34', '2025-06-30 18:43:34', 1, 1, 0),
(166, 14, 'Modified/Custom', 'Vehicles with significant modifications', 0.00, 0, '2025-06-30 18:43:34', '2025-06-30 18:43:34', 1, 1, 0),
(167, 14, 'Motorcycles', 'All types of motorcycles', 0.00, 0, '2025-06-30 18:43:34', '2025-06-30 18:43:34', 1, 1, 0),
(168, 14, 'Muscle Cars (1960-1979)', 'American muscle cars from the golden era', 0.00, 0, '2025-06-30 18:43:34', '2025-06-30 18:43:34', 1, 1, 0),
(169, 14, 'Trucks &amp; SUVs', 'Pickup trucks, SUVs, and utility vehicles', 0.00, 0, '2025-06-30 18:43:34', '2025-06-30 18:43:34', 1, 1, 0),
(180, 14, 'Classic American', 'American classics from 1920-1980', 0.00, 0, '2025-07-09 18:29:18', '2025-07-09 18:29:18', 1, 1, 1),
(181, 14, 'European Sports Cars', 'European sports and luxury vehicles', 0.00, 0, '2025-07-09 18:29:18', '2025-07-09 18:29:18', 1, 1, 2),
(182, 14, 'Muscle Cars', 'American muscle cars from the golden era', 0.00, 0, '2025-07-09 18:29:18', '2025-07-09 18:29:18', 1, 1, 3),
(183, 14, 'Import Tuners', 'Modified import and tuner vehicles', 0.00, 0, '2025-07-09 18:29:18', '2025-07-09 18:29:18', 1, 1, 4),
(184, 14, 'Exotic Supercars', 'High-end exotic and supercar vehicles', 0.00, 0, '2025-07-09 18:29:18', '2025-07-09 18:29:18', 1, 1, 5),
(238, 14, 'Classic American', 'American classics from 1920-1980', 0.00, 0, '2025-07-09 18:31:26', '2025-07-09 18:31:26', 1, 1, 1),
(239, 14, 'European Sports Cars', 'European sports and luxury vehicles', 0.00, 0, '2025-07-09 18:31:26', '2025-07-09 18:31:26', 1, 1, 2),
(240, 14, 'Muscle Cars', 'American muscle cars from the golden era', 0.00, 0, '2025-07-09 18:31:26', '2025-07-09 18:31:26', 1, 1, 3),
(241, 14, 'Import Tuners', 'Modified import and tuner vehicles', 0.00, 0, '2025-07-09 18:31:26', '2025-07-09 18:31:26', 1, 1, 4),
(342, 14, 'Classic American', 'American classics from 1920-1980', 0.00, 0, '2025-07-09 18:33:53', '2025-07-09 18:33:53', 1, 1, 1),
(343, 14, 'European Sports Cars', 'European sports and luxury vehicles', 0.00, 0, '2025-07-09 18:33:53', '2025-07-09 18:33:53', 1, 1, 2),
(344, 14, 'Muscle Cars', 'American muscle cars from the golden era', 0.00, 0, '2025-07-09 18:33:53', '2025-07-09 18:33:53', 1, 1, 3),
(345, 14, 'Import Tuners', 'Modified import and tuner vehicles', 0.00, 0, '2025-07-09 18:33:53', '2025-07-09 18:33:53', 1, 1, 4),
(346, 14, 'Exotic Supercars', 'High-end exotic and supercar vehicles', 0.00, 0, '2025-07-09 18:33:53', '2025-07-09 18:33:53', 1, 1, 5),
(490, 14, 'Classic American', 'American classics from 1920-1980', 0.00, 0, '2025-07-09 18:53:59', '2025-07-09 18:53:59', 1, 1, 1),
(491, 14, 'European Sports Cars', 'European sports and luxury vehicles', 0.00, 0, '2025-07-09 18:53:59', '2025-07-09 18:53:59', 1, 1, 2),
(492, 14, 'Muscle Cars', 'American muscle cars from the golden era', 0.00, 0, '2025-07-09 18:53:59', '2025-07-09 18:53:59', 1, 1, 3),
(493, 14, 'Import Tuners', 'Modified import and tuner vehicles', 0.00, 0, '2025-07-09 18:53:59', '2025-07-09 18:53:59', 1, 1, 4),
(494, 14, 'Exotic Supercars', 'High-end exotic and supercar vehicles', 0.00, 0, '2025-07-09 18:53:59', '2025-07-09 18:53:59', 1, 1, 5),
(550, 14, 'Classic American', 'American classics from 1920-1980', 0.00, 0, '2025-07-09 19:15:05', '2025-07-09 19:15:05', 1, 1, 1),
(551, 14, 'European Sports Cars', 'European sports and luxury vehicles', 0.00, 0, '2025-07-09 19:15:05', '2025-07-09 19:15:05', 1, 1, 2),
(552, 14, 'Muscle Cars', 'American muscle cars from the golden era', 0.00, 0, '2025-07-09 19:15:05', '2025-07-09 19:15:05', 1, 1, 3),
(553, 14, 'Import Tuners', 'Modified import and tuner vehicles', 0.00, 0, '2025-07-09 19:15:05', '2025-07-09 19:15:05', 1, 1, 4),
(607, 14, 'Classic American', 'American classics from 1920-1980', 0.00, 0, '2025-07-09 19:22:37', '2025-07-09 19:22:37', 1, 1, 1),
(608, 14, 'European Sports Cars', 'European sports and luxury vehicles', 0.00, 0, '2025-07-09 19:22:37', '2025-07-09 19:22:37', 1, 1, 2),
(609, 14, 'Muscle Cars', 'American muscle cars from the golden era', 0.00, 0, '2025-07-09 19:22:37', '2025-07-09 19:22:37', 1, 1, 3),
(610, 14, 'Import Tuners', 'Modified import and tuner vehicles', 0.00, 0, '2025-07-09 19:22:37', '2025-07-09 19:22:37', 1, 1, 4),
(611, 14, 'Exotic Supercars', 'High-end exotic and supercar vehicles', 0.00, 0, '2025-07-09 19:22:37', '2025-07-09 19:22:37', 1, 1, 5),
(667, 14, 'Classic American', 'American classics from 1920-1980', 0.00, 0, '2025-07-09 20:13:30', '2025-07-09 20:13:30', 1, 1, 1),
(668, 14, 'European Sports Cars', 'European sports and luxury vehicles', 0.00, 0, '2025-07-09 20:13:30', '2025-07-09 20:13:30', 1, 1, 2),
(669, 14, 'Muscle Cars', 'American muscle cars from the golden era', 0.00, 0, '2025-07-09 20:13:30', '2025-07-09 20:13:30', 1, 1, 3),
(670, 14, 'Import Tuners', 'Modified import and tuner vehicles', 0.00, 0, '2025-07-09 20:13:30', '2025-07-09 20:13:30', 1, 1, 4),
(671, 14, 'Exotic Supercars', 'High-end exotic and supercar vehicles', 0.00, 0, '2025-07-09 20:13:30', '2025-07-09 20:13:30', 1, 1, 5),
(727, 14, 'Classic American', 'American classics from 1920-1980', 0.00, 0, '2025-07-09 20:47:53', '2025-07-09 20:47:53', 1, 1, 1),
(728, 14, 'European Sports Cars', 'European sports and luxury vehicles', 0.00, 0, '2025-07-09 20:47:53', '2025-07-09 20:47:53', 1, 1, 2),
(729, 14, 'Muscle Cars', 'American muscle cars from the golden era', 0.00, 0, '2025-07-09 20:47:53', '2025-07-09 20:47:53', 1, 1, 3),
(730, 14, 'Import Tuners', 'Modified import and tuner vehicles', 0.00, 0, '2025-07-09 20:47:53', '2025-07-09 20:47:53', 1, 1, 4),
(781, 14, 'Classic American', 'American classics from 1920-1980', 0.00, 0, '2025-07-09 21:01:06', '2025-07-09 21:01:06', 1, 1, 1),
(782, 14, 'European Sports Cars', 'European sports and luxury vehicles', 0.00, 0, '2025-07-09 21:01:06', '2025-07-09 21:01:06', 1, 1, 2),
(783, 14, 'Muscle Cars', 'American muscle cars from the golden era', 0.00, 0, '2025-07-09 21:01:06', '2025-07-09 21:01:06', 1, 1, 3),
(784, 14, 'Import Tuners', 'Modified import and tuner vehicles', 0.00, 0, '2025-07-09 21:01:06', '2025-07-09 21:01:06', 1, 1, 4),
(785, 14, 'Exotic Supercars', 'High-end exotic and supercar vehicles', 0.00, 0, '2025-07-09 21:01:06', '2025-07-09 21:01:06', 1, 1, 5),
(841, 14, 'Classic American', 'American classics from 1920-1980', 0.00, 0, '2025-07-10 01:45:45', '2025-07-10 01:45:45', 1, 1, 1),
(842, 14, 'European Sports Cars', 'European sports and luxury vehicles', 0.00, 0, '2025-07-10 01:45:45', '2025-07-10 01:45:45', 1, 1, 2),
(843, 14, 'Muscle Cars', 'American muscle cars from the golden era', 0.00, 0, '2025-07-10 01:45:45', '2025-07-10 01:45:45', 1, 1, 3),
(844, 14, 'Import Tuners', 'Modified import and tuner vehicles', 0.00, 0, '2025-07-10 01:45:45', '2025-07-10 01:45:45', 1, 1, 4),
(845, 14, 'Exotic Supercars', 'High-end exotic and supercar vehicles', 0.00, 0, '2025-07-10 01:45:45', '2025-07-10 01:45:45', 1, 1, 5),
(889, 5, 'Asian Imports', 'Vehicles manufactured in Asia', 0.00, 0, '2025-07-21 00:09:44', '2025-07-21 00:09:44', 1, 1, 0),
(890, 5, 'Classic Cars (Pre-1960)', 'Vehicles manufactured before 1960', 0.00, 0, '2025-07-21 00:09:44', '2025-07-21 00:09:44', 1, 1, 0),
(891, 5, 'Contemporary (2000-Present)', 'Modern vehicles from 2000 to present', 0.00, 0, '2025-07-21 00:09:44', '2025-07-21 00:09:44', 1, 1, 0),
(892, 5, 'European Imports', 'Vehicles manufactured in Europe', 0.00, 0, '2025-07-21 00:09:44', '2025-07-21 00:09:44', 1, 1, 0),
(893, 5, 'Modern Classics (1980-1999)', 'Vehicles from the 80s and 90s', 0.00, 0, '2025-07-21 00:09:44', '2025-07-21 00:09:44', 1, 1, 0),
(894, 5, 'Modified/Custom', 'Vehicles with significant modifications', 0.00, 0, '2025-07-21 00:09:44', '2025-07-21 00:09:44', 1, 1, 0),
(895, 5, 'Motorcycles', 'All types of motorcycles', 0.00, 0, '2025-07-21 00:09:44', '2025-07-21 00:09:44', 1, 1, 0),
(896, 5, 'Muscle Cars (1960-1979)', 'American muscle cars from the golden era', 0.00, 0, '2025-07-21 00:09:44', '2025-07-21 00:09:44', 1, 1, 0),
(897, 5, 'Trucks &amp; SUVs', 'Pickup trucks, SUVs, and utility vehicles', 0.00, 0, '2025-07-21 00:09:44', '2025-07-21 00:09:44', 1, 1, 0);
