
-- --------------------------------------------------------

--
-- Table structure for table `messages`
--
-- Creation: Jul 18, 2025 at 12:20 AM
--

CREATE TABLE `messages` (
  `id` int(11) NOT NULL,
  `from_user_id` int(10) UNSIGNED NOT NULL,
  `to_user_id` int(10) UNSIGNED NOT NULL,
  `subject` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `show_id` int(11) DEFAULT NULL COMMENT 'Related show if applicable',
  `parent_message_id` int(11) DEFAULT NULL COMMENT 'For reply threading',
  `message_type` enum('direct','system','judging','event','admin','notification','email') NOT NULL DEFAULT 'direct',
  `priority` enum('low','normal','high','urgent') NOT NULL DEFAULT 'normal',
  `is_read` tinyint(1) DEFAULT 0,
  `is_archived` tinyint(1) DEFAULT 0,
  `requires_reply` tinyint(1) DEFAULT 0,
  `allows_reply` tinyint(1) DEFAULT 1,
  `reply_used` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `read_at` timestamp NULL DEFAULT NULL,
  `replied_at` timestamp NULL DEFAULT NULL,
  `ticket_number` varchar(50) DEFAULT NULL COMMENT 'Unique ticket number for email threading',
  `email_message_id` varchar(255) DEFAULT NULL COMMENT 'Original email Message-ID header',
  `original_sender_email` varchar(255) DEFAULT NULL COMMENT 'Original email sender for non-user emails',
  `folder_id` int(11) DEFAULT NULL COMMENT 'Admin folder assignment',
  `owned_by_admin_id` int(11) DEFAULT NULL COMMENT 'Admin who owns this message',
  `security_token` varchar(10) DEFAULT NULL COMMENT 'Random security token for ticket validation'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- RELATIONSHIPS FOR TABLE `messages`:
--   `from_user_id`
--       `users` -> `id`
--   `to_user_id`
--       `users` -> `id`
--   `parent_message_id`
--       `messages` -> `id`
--

--
-- Dumping data for table `messages`
--

INSERT INTO `messages` (`id`, `from_user_id`, `to_user_id`, `subject`, `message`, `show_id`, `parent_message_id`, `message_type`, `priority`, `is_read`, `is_archived`, `requires_reply`, `allows_reply`, `reply_used`, `created_at`, `read_at`, `replied_at`, `ticket_number`, `email_message_id`, `original_sender_email`, `folder_id`, `owned_by_admin_id`, `security_token`) VALUES
(33, 1, 1, '🎉 Test Message Subject', 'This is a test message to verify that both the subject and full message content are properly sent via FCM push notifications and email. The message should appear in both the notification title/subject and the message body.', NULL, NULL, 'direct', 'normal', 0, 0, 0, 1, 0, '2025-07-15 13:10:55', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(34, 1, 1, 'Test Message', 'This is a simple test message.', NULL, NULL, 'direct', 'normal', 0, 0, 0, 1, 0, '2025-07-15 13:11:24', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(35, 1, 1, 'Test Message', 'This is a simple test message.', NULL, NULL, 'direct', 'normal', 0, 0, 0, 1, 0, '2025-07-15 13:16:30', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(36, 1, 1, 'Test Message', 'This is a simple test message.', NULL, NULL, 'direct', 'normal', 0, 0, 0, 1, 0, '2025-07-15 13:16:38', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(37, 1, 1, 'Test Message', 'This is a simple test message.', NULL, NULL, 'direct', 'normal', 0, 0, 0, 1, 0, '2025-07-15 13:16:39', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(38, 1, 1, 'Test Message', 'This is a simple test message.', NULL, NULL, 'direct', 'normal', 0, 0, 0, 1, 0, '2025-07-15 13:23:47', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(68, 1, 211, 'Contact Form: test', 'New contact form submission:\n\nName: dffdfgfdgd\nEmail: <EMAIL>\nSubject: test\n\nMessage:\ntest\n\n---\nThis message was sent via the contact form on 2025-07-15 22:25:13\nReply directly to: <EMAIL>', NULL, NULL, 'system', 'normal', 0, 0, 0, 1, 0, '2025-07-15 22:25:14', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(70, 1, 211, 'Contact Form: fghfghfgh', 'New contact form submission:\n\nName: brian\nEmail: <EMAIL>\nSubject: fghfghfgh\n\nMessage:\nfghfghfghfgh\n\n---\nThis message was sent via the contact form on 2025-07-16 00:37:37\nReply directly to: <EMAIL>\n\n[reply]\nCONTACT_EMAIL:<EMAIL>', NULL, NULL, 'system', 'normal', 0, 0, 1, 1, 0, '2025-07-16 00:37:37', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(71, 3, 1, 'Re: Contact Form: fghfghfgh', 'repling to you\n\n[This reply was sent via email to: <EMAIL>]', NULL, NULL, 'direct', 'normal', 0, 0, 0, 1, 0, '2025-07-16 00:38:19', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(73, 1, 211, 'Contact Form: kjflgjldfjgkldfjklg', 'New contact form submission:\n\nName: dfghdfgdfg\nEmail: <EMAIL>\nSubject: kjflgjldfjgkldfjklg\n\nMessage:\ngfdgdfgdfgdfgdfg\n\n---\nThis message was sent via the contact form on 2025-07-16 01:01:00\nReply directly to: <EMAIL>\n\n[reply]\nCONTACT_EMAIL:<EMAIL>', NULL, NULL, 'system', 'normal', 0, 0, 1, 1, 0, '2025-07-16 01:01:00', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(74, 3, 1, 'Re: Contact Form: kjflgjldfjgkldfjklg', 'hello\n\n[This reply was sent via email to: <EMAIL>]', NULL, NULL, 'direct', 'normal', 0, 0, 0, 1, 0, '2025-07-16 01:12:18', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(75, 3, 1, 'Re: Contact Form: kjflgjldfjgkldfjklg', 'ihgkjhjkhjkhjkhjkhkjhkjhjkhjkhjkhjk\n\n[This reply was sent via email to: <EMAIL>]', NULL, NULL, 'direct', 'normal', 0, 0, 0, 1, 0, '2025-07-16 01:14:33', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(77, 1, 211, 'Contact Form: test email', 'New contact form submission:\n\nName: dgdfgdfgdf\nEmail: <EMAIL>\nSubject: test email\n\nMessage:\nemailing\n\n---\nThis message was sent via the contact form on 2025-07-16 12:00:15\nReply directly to: <EMAIL>\n\n[reply]\nCONTACT_EMAIL:<EMAIL>', NULL, NULL, 'system', 'normal', 0, 0, 1, 1, 0, '2025-07-16 12:00:16', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(78, 3, 1, 'Re: Contact Form: test email', 'fdgdfgfdgdfgd\n\n[This reply was sent via email to: <EMAIL>]', NULL, NULL, 'direct', 'normal', 0, 0, 0, 1, 0, '2025-07-16 12:01:33', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(80, 1, 211, 'Contact Form: fsdgsdfsdf', 'New contact form submission:\n\nName: fgsdgsdfgsdf\nEmail: <EMAIL>\nSubject: fsdgsdfsdf\n\nMessage:\nfsdfsdfsdf\n\n---\nThis message was sent via the contact form on 2025-07-16 12:22:02\nReply directly to: <EMAIL>\n\n[reply]\nCONTACT_EMAIL:<EMAIL>', NULL, NULL, 'system', 'normal', 0, 0, 1, 1, 0, '2025-07-16 12:22:02', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(81, 3, 1, 'Re: Contact Form: fsdgsdfsdf', 'this is a reply .\n\n[This reply was sent via email to: <EMAIL>]', NULL, NULL, 'direct', 'normal', 0, 0, 0, 1, 0, '2025-07-16 13:17:52', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(84, 1, 211, 'Contact Form: test contact form', 'New contact form submission:\n\nName: ham burger\nEmail: <EMAIL>\nSubject: test contact form\n\nMessage:\nthis is\r\na \r\ntest\r\nof \r\nthe contact form \r\ntesting to see\r\nif its working correctly\r\n1\r\n2\r\n3\n\n---\nThis message was sent via the contact form on 2025-07-16 14:02:47\nReply directly to: <EMAIL>\n\n[reply]\nCONTACT_EMAIL:<EMAIL>', NULL, NULL, 'system', 'normal', 0, 0, 1, 1, 0, '2025-07-16 14:02:47', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(85, 3, 1, 'Re: Contact Form: test contact form', 'ghdfgdfgdfg\n\n[This reply was sent via email to: <EMAIL>]', NULL, NULL, 'direct', 'normal', 0, 0, 0, 1, 0, '2025-07-16 14:04:36', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(87, 1, 211, 'Contact Form: ofdgkldfjklk', 'New contact form submission:\n\nName: fdgdfgfdgdfg\nEmail: <EMAIL>\nSubject: ofdgkldfjklk\n\nMessage:\nlklklklkl\n\n---\nThis message was sent via the contact form on 2025-07-16 14:48:04\nReply directly to: <EMAIL>\n\n[reply]\nCONTACT_EMAIL:<EMAIL>', NULL, NULL, 'system', 'normal', 0, 0, 1, 1, 0, '2025-07-16 14:48:04', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(89, 1, 211, 'Contact Form: kdjfgljfdklgjkldfjkl', 'New contact form submission:\n\nName: gfdgdfgdfg\nEmail: <EMAIL>\nSubject: kdjfgljfdklgjkldfjkl\n\nMessage:\nkjkljkljkljkl\n\n---\nThis message was sent via the contact form on 2025-07-16 14:55:54\nReply directly to: <EMAIL>\n\n[reply]\nCONTACT_EMAIL:<EMAIL>', NULL, NULL, 'system', 'normal', 0, 0, 1, 1, 0, '2025-07-16 14:55:54', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(91, 1, 211, 'Contact Form: sdfsdfsdf', 'New contact form submission:\n\nName: sdfsdfsd\nEmail: <EMAIL>\nSubject: sdfsdfsdf\n\nMessage:\nfsdfsdfsdf\n\n---\nThis message was sent via the contact form on 2025-07-16 15:07:26\nReply directly to: <EMAIL>\n\n[reply]\nCONTACT_EMAIL:<EMAIL>', NULL, NULL, 'system', 'normal', 0, 0, 1, 1, 0, '2025-07-16 15:07:27', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(92, 3, 1, 'Re: Contact Form: sdfsdfsdf', 'test\n\n[This reply was sent via email to: <EMAIL>]', NULL, NULL, 'direct', 'normal', 0, 0, 0, 1, 0, '2025-07-16 15:10:19', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(94, 1, 211, 'Contact Form: gdfgdfgfd', 'New contact form submission:\n\nName: fgdfgdfgdf\nEmail: <EMAIL>\nSubject: gdfgdfgfd\n\nMessage:\ngdfgfdgdfgdfgdf\n\n---\nThis message was sent via the contact form on 2025-07-16 15:15:06\nReply directly to: <EMAIL>\n\n[reply]\nCONTACT_EMAIL:<EMAIL>', NULL, NULL, 'system', 'normal', 0, 0, 1, 1, 0, '2025-07-16 15:15:07', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(96, 1, 211, 'Contact Form: gdfgdfgfd', 'New contact form submission:\n\nName: fgdfgdfgdf\nEmail: <EMAIL>\nSubject: gdfgdfgfd\n\nMessage:\ngdfgfdgdfgdfgdf\n\n---\nThis message was sent via the contact form on 2025-07-16 15:18:21\nReply directly to: <EMAIL>\n\n[reply]\nCONTACT_EMAIL:<EMAIL>', NULL, NULL, 'system', 'normal', 0, 0, 1, 1, 0, '2025-07-16 15:18:26', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(98, 1, 211, 'Contact Form: gdfgdfgdfg', 'New contact form submission:\n\nName: gdfgdfgdfgdf\nEmail: <EMAIL>\nSubject: gdfgdfgdfg\n\nMessage:\ngdfgfdgfdgdf\n\n---\nThis message was sent via the contact form on 2025-07-16 15:19:17\nReply directly to: <EMAIL>\n\n[reply]\nCONTACT_EMAIL:<EMAIL>', NULL, NULL, 'system', 'normal', 0, 0, 1, 1, 0, '2025-07-16 15:19:18', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(100, 1, 211, 'Contact Form: Test Notification from Events System', 'New contact form submission:\n\nName: Summer Classic Auto Show 2025\nEmail: <EMAIL>\nSubject: Test Notification from Events System\n\nMessage:\ngffghfghfghfghfghfgh\n\n---\nThis message was sent via the contact form on 2025-07-16 15:25:57\nReply directly to: <EMAIL>\n\n[reply]\nCONTACT_EMAIL:<EMAIL>', NULL, NULL, 'system', 'normal', 0, 0, 1, 1, 0, '2025-07-16 15:25:59', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(102, 1, 211, 'Contact Form: fgjfghfgh', 'New contact form submission:\n\nName: fdhdfdgdfg\nEmail: <EMAIL>\nSubject: fgjfghfgh\n\nMessage:\nfghfghfghfgh\n\n---\nThis message was sent via the contact form on 2025-07-16 15:40:34\nReply directly to: <EMAIL>\n\n[reply]\nCONTACT_EMAIL:<EMAIL>', NULL, NULL, 'system', 'normal', 0, 0, 1, 1, 0, '2025-07-16 15:40:36', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(104, 1, 211, 'Contact Form: dfgdfgdfg', 'New contact form submission:\n\nName: fdgdfgfdgfdg\nEmail: <EMAIL>\nSubject: dfgdfgdfg\n\nMessage:\ngdfgdfgdfgdgd\n\n---\nThis message was sent via the contact form on 2025-07-16 15:42:28\nReply directly to: <EMAIL>\n\n[reply]\nCONTACT_EMAIL:<EMAIL>', NULL, NULL, 'system', 'normal', 0, 0, 1, 1, 0, '2025-07-16 15:42:29', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(106, 1, 211, 'Contact Form: Test Toast Message', 'New contact form submission:\n\nName: fdfhgdfg\nEmail: <EMAIL>\nSubject: Test Toast Message\n\nMessage:\ngdfgdfgdfgdfgd\n\n---\nThis message was sent via the contact form on 2025-07-16 15:44:27\nReply directly to: <EMAIL>\n\n[reply]\nCONTACT_EMAIL:<EMAIL>', NULL, NULL, 'system', 'normal', 0, 0, 1, 1, 0, '2025-07-16 15:44:31', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(108, 1, 211, 'Contact Form: hfghfhfghfghf', 'New contact form submission:\n\nName: dfgfdg\nEmail: <EMAIL>\nSubject: hfghfhfghfghf\n\nMessage:\nhfghfghfghfghf\n\n---\nThis message was sent via the contact form on 2025-07-16 15:47:22\nReply directly to: <EMAIL>\n\n[reply]\nCONTACT_EMAIL:<EMAIL>', NULL, NULL, 'system', 'normal', 0, 0, 1, 1, 0, '2025-07-16 15:47:28', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(110, 1, 211, 'Contact Form: Test Toast Message', 'New contact form submission:\n\nName: Name of show\nEmail: <EMAIL>\nSubject: Test Toast Message\n\nMessage:\ndfgdftgdfgdfgdfg\n\n---\nThis message was sent via the contact form on 2025-07-16 15:50:04\nReply directly to: <EMAIL>\n\n[reply]\nCONTACT_EMAIL:<EMAIL>', NULL, NULL, 'system', 'normal', 0, 0, 1, 1, 0, '2025-07-16 15:50:05', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(112, 1, 211, 'Contact Form: gdfgdfgdfgd', 'New contact form submission:\n\nName: dfgdfgdfg\nEmail: <EMAIL>\nSubject: gdfgdfgdfgd\n\nMessage:\ngdfgdfgdfgdfgd\n\n---\nThis message was sent via the contact form on 2025-07-16 16:23:20\nReply directly to: <EMAIL>\n\n[reply]\nCONTACT_EMAIL:<EMAIL>', NULL, NULL, 'system', 'normal', 0, 0, 1, 1, 0, '2025-07-16 16:23:21', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(114, 1, 211, 'Contact Form: gfdgdfgdfg', 'New contact form submission:\n\nName: cfgfsdgfdgfdg\nEmail: <EMAIL>\nSubject: gfdgdfgdfg\n\nMessage:\ngfdgfdgdfgdfgdfg\n\n---\nThis message was sent via the contact form on 2025-07-16 16:30:44\nReply directly to: <EMAIL>\n\n[reply]\nCONTACT_EMAIL:<EMAIL>', NULL, NULL, 'system', 'normal', 0, 0, 1, 1, 0, '2025-07-16 16:30:45', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(117, 1, 211, 'Contact Form: dfgdfgdf', 'New contact form submission:\n\nName: fgdfgdfgd\nEmail: <EMAIL>\nSubject: dfgdfgdf\n\nMessage:\ngdfgdfgdfgdf\n\n---\nThis message was sent via the contact form on 2025-07-16 16:41:40\nReply directly to: <EMAIL>\n\n[reply]\nCONTACT_EMAIL:<EMAIL>', NULL, NULL, 'system', 'normal', 0, 0, 1, 1, 0, '2025-07-16 16:41:41', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(119, 1, 211, 'Contact Form: Test Toast Message', 'New contact form submission:\n\nName: Name of show\nEmail: <EMAIL>\nSubject: Test Toast Message\n\nMessage:\ndfgdfgdfgdfg\n\n---\nThis message was sent via the contact form on 2025-07-16 17:18:12\nReply directly to: <EMAIL>\n\n[reply]\nCONTACT_EMAIL:<EMAIL>', NULL, NULL, 'system', 'normal', 0, 0, 1, 1, 0, '2025-07-16 17:18:16', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(121, 1, 211, 'Contact Form: Test Notification from Events System', 'New contact form submission:\n\nName: Name of show\nEmail: <EMAIL>\nSubject: Test Notification from Events System\n\nMessage:\nsdgdfgdfgdfgdfgdf\n\n---\nThis message was sent via the contact form on 2025-07-16 17:35:05\nReply directly to: <EMAIL>\n\n[reply]\nCONTACT_EMAIL:<EMAIL>', NULL, NULL, 'system', 'normal', 0, 0, 1, 1, 0, '2025-07-16 17:35:06', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(122, 1, 5, 'Judge Request Approved - Summer Classic Auto Show 2025', 'The judge request for Brian Correll has been approved by an administrator for your show \'Summer Classic Auto Show 2025\'.', NULL, NULL, 'notification', 'normal', 1, 0, 0, 1, 0, '2025-07-11 11:35:24', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(123, 1, 5, 'Judge Request Sent - Summer Classic Auto Show 2025', 'A judge request has been sent to Brian Correll for your show \'Summer Classic Auto Show 2025\'. They will receive a notification to accept or decline the request.', NULL, NULL, 'notification', 'normal', 1, 0, 0, 1, 0, '2025-07-11 11:36:06', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(124, 1, 5, 'Judge Request Sent - Summer Classic Auto Show 2025', 'A judge request has been sent to Brian Correll for your show \'Summer Classic Auto Show 2025\'. They will receive a notification to accept or decline the request.', NULL, NULL, 'notification', 'normal', 1, 0, 0, 1, 0, '2025-07-11 11:37:05', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(150, 1, 1, '🎉 Test Message Subject', 'This is a test message to verify that both the subject and full message content are properly sent...', NULL, NULL, 'notification', 'normal', 0, 0, 0, 1, 0, '2025-07-15 13:10:55', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(151, 1, 1, 'Test Message', 'This is a simple test message.', NULL, NULL, 'notification', 'normal', 0, 0, 0, 1, 0, '2025-07-15 13:11:24', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(152, 1, 1, 'Test Message', 'This is a simple test message.', NULL, NULL, 'notification', 'normal', 0, 0, 0, 1, 0, '2025-07-15 13:16:30', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(153, 1, 1, 'Test Message', 'This is a simple test message.', NULL, NULL, 'notification', 'normal', 0, 0, 0, 1, 0, '2025-07-15 13:16:38', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(154, 1, 1, 'Test Message', 'This is a simple test message.', NULL, NULL, 'notification', 'normal', 0, 0, 0, 1, 0, '2025-07-15 13:16:39', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(155, 1, 1, 'Test Message', 'This is a simple test message.', NULL, NULL, 'notification', 'normal', 0, 0, 0, 1, 0, '2025-07-15 13:23:47', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(185, 1, 5, 'Judge Request Approved - Summer Classic Auto Show 2025', 'The judge request for Brian Correll has been approved by an administrator for your show \'Summer Classic Auto Show 2025\'.', NULL, NULL, 'notification', 'normal', 1, 0, 0, 1, 0, '2025-07-11 11:35:24', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(186, 1, 5, 'Judge Request Sent - Summer Classic Auto Show 2025', 'A judge request has been sent to Brian Correll for your show \'Summer Classic Auto Show 2025\'. They will receive a notification to accept or decline the request.', NULL, NULL, 'notification', 'normal', 1, 0, 0, 1, 0, '2025-07-11 11:36:06', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(187, 1, 5, 'Judge Request Sent - Summer Classic Auto Show 2025', 'A judge request has been sent to Brian Correll for your show \'Summer Classic Auto Show 2025\'. They will receive a notification to accept or decline the request.', NULL, NULL, 'notification', 'normal', 1, 0, 0, 1, 0, '2025-07-11 11:37:05', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(227, 1, 1, 'New Message', 'From System: 🎉 Test Message Subject', NULL, NULL, 'notification', 'normal', 0, 0, 0, 1, 0, '2025-07-15 13:10:55', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(228, 1, 1, 'New Message', 'From System: Test Message', NULL, NULL, 'notification', 'normal', 0, 0, 0, 1, 0, '2025-07-15 13:11:24', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(229, 1, 1, 'New Message', 'From System: Test Message', NULL, NULL, 'notification', 'normal', 0, 0, 0, 1, 0, '2025-07-15 13:16:30', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(230, 1, 1, 'New Message', 'From System: Test Message', NULL, NULL, 'notification', 'normal', 0, 0, 0, 1, 0, '2025-07-15 13:16:38', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(231, 1, 1, 'New Message', 'From System: Test Message', NULL, NULL, 'notification', 'normal', 0, 0, 0, 1, 0, '2025-07-15 13:16:39', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(232, 1, 1, 'New Message', 'From System: Test Message', NULL, NULL, 'notification', 'normal', 0, 0, 0, 1, 0, '2025-07-15 13:23:47', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(262, 1, 211, 'New Message', 'From System: Contact Form: test', NULL, NULL, 'notification', 'normal', 0, 0, 0, 1, 0, '2025-07-15 22:25:14', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(264, 1, 211, 'New Message', 'From System: Contact Form: fghfghfgh', NULL, NULL, 'notification', 'normal', 0, 0, 0, 1, 0, '2025-07-16 00:37:37', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(265, 1, 1, 'New Message', 'From Brian Correll: Re: Contact Form: fghfghfgh', NULL, NULL, 'notification', 'normal', 0, 0, 0, 1, 0, '2025-07-16 00:38:19', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(267, 1, 211, 'New Message', 'From System: Contact Form: kjflgjldfjgkldfjklg', NULL, NULL, 'notification', 'normal', 0, 0, 0, 1, 0, '2025-07-16 01:01:00', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(268, 1, 1, 'New Message', 'From Brian Correll: Re: Contact Form: kjflgjldfjgkldfjklg', NULL, NULL, 'notification', 'normal', 0, 0, 0, 1, 0, '2025-07-16 01:12:18', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(269, 1, 1, 'New Message', 'From Brian Correll: Re: Contact Form: kjflgjldfjgkldfjklg', NULL, NULL, 'notification', 'normal', 0, 0, 0, 1, 0, '2025-07-16 01:14:33', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(271, 1, 211, 'New Message', 'From System: Contact Form: test email', NULL, NULL, 'notification', 'normal', 0, 0, 0, 1, 0, '2025-07-16 12:00:16', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(272, 1, 1, 'New Message', 'From Brian Correll: Re: Contact Form: test email', NULL, NULL, 'notification', 'normal', 0, 0, 0, 1, 0, '2025-07-16 12:01:33', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(274, 1, 211, 'New Message', 'From System: Contact Form: fsdgsdfsdf', NULL, NULL, 'notification', 'normal', 0, 0, 0, 1, 0, '2025-07-16 12:22:02', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(275, 1, 1, 'New Message', 'From Brian Correll: Re: Contact Form: fsdgsdfsdf', NULL, NULL, 'notification', 'normal', 0, 0, 0, 1, 0, '2025-07-16 13:17:52', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(277, 1, 211, 'New Message', 'From System: Contact Form: test contact form', NULL, NULL, 'notification', 'normal', 0, 0, 0, 1, 0, '2025-07-16 14:02:47', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(278, 1, 1, 'New Message', 'From Brian Correll: Re: Contact Form: test contact form', NULL, NULL, 'notification', 'normal', 0, 0, 0, 1, 0, '2025-07-16 14:04:36', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(280, 1, 211, 'New Message', 'From System: Contact Form: ofdgkldfjklk', NULL, NULL, 'notification', 'normal', 0, 0, 0, 1, 0, '2025-07-16 14:48:04', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(282, 1, 211, 'New Message', 'From System: Contact Form: kdjfgljfdklgjkldfjkl', NULL, NULL, 'notification', 'normal', 0, 0, 0, 1, 0, '2025-07-16 14:55:54', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(284, 1, 211, 'New Message', 'From System: Contact Form: sdfsdfsdf', NULL, NULL, 'notification', 'normal', 0, 0, 0, 1, 0, '2025-07-16 15:07:28', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(285, 1, 1, 'New Message', 'From Brian Correll: Re: Contact Form: sdfsdfsdf', NULL, NULL, 'notification', 'normal', 0, 0, 0, 1, 0, '2025-07-16 15:10:19', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(287, 1, 211, 'New Message', 'From System: Contact Form: gdfgdfgfd', NULL, NULL, 'notification', 'normal', 0, 0, 0, 1, 0, '2025-07-16 15:15:08', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(289, 1, 211, 'New Message', 'From System: Contact Form: gdfgdfgfd', NULL, NULL, 'notification', 'normal', 0, 0, 0, 1, 0, '2025-07-16 15:18:32', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(291, 1, 211, 'New Message', 'From System: Contact Form: gdfgdfgdfg', NULL, NULL, 'notification', 'normal', 0, 0, 0, 1, 0, '2025-07-16 15:19:19', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(293, 1, 211, 'New Message', 'From System: Contact Form: Test Notification from Events System', NULL, NULL, 'notification', 'normal', 0, 0, 0, 1, 0, '2025-07-16 15:26:00', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(295, 1, 211, 'New Message', 'From System: Contact Form: fgjfghfgh', NULL, NULL, 'notification', 'normal', 0, 0, 0, 1, 0, '2025-07-16 15:40:37', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(297, 1, 211, 'New Message', 'From System: Contact Form: dfgdfgdfg', NULL, NULL, 'notification', 'normal', 0, 0, 0, 1, 0, '2025-07-16 15:42:30', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(299, 1, 211, 'New Message', 'From System: Contact Form: Test Toast Message', NULL, NULL, 'notification', 'normal', 0, 0, 0, 1, 0, '2025-07-16 15:44:32', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(301, 1, 211, 'New Message', 'From System: Contact Form: hfghfhfghfghf', NULL, NULL, 'notification', 'normal', 0, 0, 0, 1, 0, '2025-07-16 15:47:32', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(303, 1, 211, 'New Message', 'From System: Contact Form: Test Toast Message', NULL, NULL, 'notification', 'normal', 0, 0, 0, 1, 0, '2025-07-16 15:50:06', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(305, 1, 211, 'New Message', 'From System: Contact Form: gdfgdfgdfgd', NULL, NULL, 'notification', 'normal', 0, 0, 0, 1, 0, '2025-07-16 16:23:22', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(307, 1, 211, 'New Message', 'From System: Contact Form: gfdgdfgdfg', NULL, NULL, 'notification', 'normal', 0, 0, 0, 1, 0, '2025-07-16 16:30:46', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(310, 1, 211, 'New Message', 'From System: Contact Form: dfgdfgdf', NULL, NULL, 'notification', 'normal', 0, 0, 0, 1, 0, '2025-07-16 16:41:42', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(312, 1, 211, 'New Message', 'From System: Contact Form: Test Toast Message', NULL, NULL, 'notification', 'normal', 0, 0, 0, 1, 0, '2025-07-16 17:18:16', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(314, 1, 211, 'New Message', 'From System: Contact Form: Test Notification from Events System', NULL, NULL, 'notification', 'normal', 0, 0, 0, 1, 0, '2025-07-16 17:35:07', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(500, 3, 3, 'show question [RER-2025-001-NFQKS9]', '------sinikael-?=_1-17528868592260.1560288133272248\nContent-Type: text/plain; charset=utf-8\nContent-Transfer-Encoding: 7bit\n\ntest message  \n \n asking a question\n------sinikael-?=_1-17528868592260.1560288133272248\nContent-Type: text/html; charset=utf-8\nContent-Transfer-Encoding: quoted-printable\n\n<html><head></head><body><div>\n    <div dir=\"auto\" id=\"compose-body-wrapper\">test message </div><div dir=\"auto\" id=\"compose-body-wrapper\">asking a question</div><div dir=\"auto\" id=\"compose-body-wrapper\"><br/>\n      <div dir=\"auto\"><br/></div>\n      <div dir=\"auto\" id=\"tmjah_g_1299\">\n      Get <a href=\"https://bluemail.me/download/\" target=\"_blank\">BlueMail for Mobile</a>\n      </div>\n      <div dir=\"auto\"><br/></div>\n    </div>\n  </div></body></html>\n\n------sinikael-?=_1-17528868592260.1560288133272248--', 5, NULL, 'email', 'normal', 1, 0, 0, 1, 0, '2025-07-19 01:01:18', '2025-07-19 01:01:51', NULL, 'RER-2025-001', '<<EMAIL>>', '<EMAIL>', 1, NULL, 'NFQKS9'),
(501, 3, 3, 'Re: show question [RER-2025-001-NFQKS9]', 'Hello,\r\n\r\nThank you for your inquiry about our events.\r\n\r\nHere is the information you requested:\r\n\r\n[Please add specific event details here]\r\n\r\nIf you have any other questions, please do not hesitate to ask.\r\n\r\nBest regards,\r\nAdmin\r\nEvents and Shows Platform', 5, 500, 'email', 'normal', 1, 0, 0, 1, 0, '2025-07-19 01:01:48', '2025-07-19 01:01:51', NULL, 'RER-2025-001', NULL, NULL, NULL, NULL, 'NFQKS9'),
(502, 3, 3, 'Re: show question [RER-2025-001-NFQKS9]', '------sinikael-?=_1-17528869465410.7023045675010756\nContent-Type: text/plain; charset=utf-8\nContent-Transfer-Encoding: 7bit\n\ntest reply  \n \n \n \n \n \n \n \n Hello,\n\nThank you for your inquiry about our events.\n\nHere is the information you requested:\n\n[Please add specific event details here]\n\nIf you have any other questions, please do not hesitate to ask.\n\nBest regards,\nAdmin\nEvents and Shows Platform\n------sinikael-?=_1-17528869465410.7023045675010756\nContent-Type: text/html; charset=utf-8\nContent-Transfer-Encoding: quoted-printable\n\n<html><head></head><body><div> \n    \n    \n      \n        <meta charset=\"utf-8\"/>\n      \n      \n        <div id=\"compose-body-wrapper\" dir=\"auto\"><div dir=\"auto\">test reply </div><div dir=\"auto\"><br/></div><div dir=\"auto\" id=\"tmjah_g_1299\">Get <a href=\"https://bluemail.me/download/\" target=\"_blank\">BlueMail for Mobile</a></div></div><div class=\"replyHeader\" dir=\"auto\">On July 18, 2025, at 21:01, Rowan Elite Rides Events and Shows &lt;<a href=\"mailto:<EMAIL>\"><EMAIL></a>&gt; wrote:<br/></div><br/><br/><div><blockquote cite=\"mid:<EMAIL>\" type=\"cite\" style=\"margin:0 0 0 .8ex;border-left:1px #ccc solid;padding-left:1ex\">Hello,\n\nThank you for your inquiry about our events.\n\nHere is the information you requested:\n\n[Please add specific event details here]\n\nIf you have any other questions, please do not hesitate to ask.\n\nBest regards,\nAdmin\nEvents and Shows Platform\n</blockquote></div>\n      \n    \n  </div></body></html>\n\n------sinikael-?=_1-17528869465410.7023045675010756--', 5, 500, 'email', 'normal', 1, 0, 0, 1, 0, '2025-07-19 01:02:39', '2025-07-21 00:42:35', NULL, 'RER-2025-001', '<<EMAIL>>', '<EMAIL>', 1, NULL, 'NFQKS9'),
(503, 3, 3, 'test message from registration', 'testing registration messages [reply]', 9, NULL, 'admin', 'normal', 1, 0, 1, 1, 0, '2025-07-19 01:04:24', '2025-07-19 22:06:45', NULL, NULL, NULL, NULL, NULL, NULL, NULL),
(504, 3, 3, 'Re: test message from registration', 'testing reply from admin', 9, 503, 'direct', 'normal', 1, 0, 0, 1, 0, '2025-07-19 01:04:47', '2025-07-19 01:04:48', NULL, NULL, NULL, NULL, NULL, NULL, NULL);
