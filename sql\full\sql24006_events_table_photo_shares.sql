
-- --------------------------------------------------------

--
-- Table structure for table `photo_shares`
--
-- Creation: Aug 01, 2025 at 01:07 PM
-- Last update: Aug 01, 2025 at 01:36 PM
--

CREATE TABLE `photo_shares` (
  `id` int(10) UNSIGNED NOT NULL,
  `photo_id` int(10) UNSIGNED NOT NULL,
  `user_id` int(10) UNSIGNED DEFAULT NULL,
  `platform` enum('facebook','twitter','instagram','whatsapp','email','copy_link') NOT NULL,
  `shared_at` timestamp NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_uca1400_ai_ci;

--
-- RELATIONSHIPS FOR TABLE `photo_shares`:
--   `photo_id`
--       `images` -> `id`
--   `user_id`
--       `users` -> `id`
--

--
-- Dumping data for table `photo_shares`
--

INSERT INTO `photo_shares` (`id`, `photo_id`, `user_id`, `platform`, `shared_at`) VALUES
(1, 589, 3, 'facebook', '2025-08-01 13:09:40'),
(2, 589, 3, 'facebook', '2025-08-01 13:09:55'),
(3, 589, 3, 'twitter', '2025-08-01 13:10:01'),
(4, 589, 3, 'twitter', '2025-08-01 13:12:24'),
(5, 589, 3, 'facebook', '2025-08-01 13:36:20'),
(6, 589, 3, 'twitter', '2025-08-01 13:36:40'),
(7, 589, 3, 'whatsapp', '2025-08-01 13:36:54');
