
-- --------------------------------------------------------

--
-- Table structure for table `sms_providers`
--
-- Creation: Jun 23, 2025 at 08:30 PM
--

CREATE TABLE `sms_providers` (
  `id` int(11) NOT NULL,
  `name` varchar(100) NOT NULL,
  `provider_key` varchar(50) NOT NULL,
  `api_endpoint` varchar(255) NOT NULL,
  `api_key` varchar(255) DEFAULT NULL,
  `api_secret` varchar(255) DEFAULT NULL,
  `sender_id` varchar(50) DEFAULT NULL,
  `is_active` tinyint(1) DEFAULT 0,
  `is_default` tinyint(1) DEFAULT 0,
  `configuration` text DEFAULT NULL COMMENT 'JSON configuration for provider-specific settings',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- RELATIONSHIPS FOR TABLE `sms_providers`:
--

--
-- Dumping data for table `sms_providers`
--

INSERT INTO `sms_providers` (`id`, `name`, `provider_key`, `api_endpoint`, `api_key`, `api_secret`, `sender_id`, `is_active`, `is_default`, `configuration`, `created_at`, `updated_at`) VALUES
(1, 'Twilio', 'twilio', 'https://api.twilio.com/2010-04-01/Accounts/{account_sid}/Messages.json', NULL, NULL, 'Events', 0, 0, '{\"account_sid\":\"**********************************\",\"auth_token\":\"b715f9ae1d44c408a7df2318464e0704\",\"from_number\":\"+***********\"}', '2025-06-22 01:24:12', '2025-06-23 14:24:53'),
(2, 'TextMagic', 'textmagic', 'https://rest.textmagic.com/api/v2/messages', NULL, NULL, 'Events', 0, 0, '{\"username\":\"\",\"api_key\":\"\"}', '2025-06-22 01:24:12', '2025-06-22 01:24:12'),
(3, 'Nexmo/Vonage', 'nexmo', 'https://rest.nexmo.com/sms/json', NULL, NULL, 'Events', 0, 0, '{\"api_key\":\"\",\"api_secret\":\"\",\"from\":\"\"}', '2025-06-22 01:24:12', '2025-06-22 01:24:12'),
(4, 'ClickSend', 'clicksend', 'https://rest.clicksend.com/v3/sms/send', NULL, NULL, 'Events', 0, 0, '{\"username\":\"\",\"api_key\":\"\"}', '2025-06-22 01:24:12', '2025-06-22 01:24:12'),
(5, 'Plivo', 'plivo', 'https://api.plivo.com/v1/Account/{auth_id}/Message/', NULL, NULL, 'Events', 0, 0, '{\"auth_id\":\"\",\"auth_token\":\"\",\"src\":\"\"}', '2025-06-22 01:24:12', '2025-06-22 01:24:12');
