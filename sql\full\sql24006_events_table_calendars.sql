
-- --------------------------------------------------------

--
-- Table structure for table `calendars`
--
-- Creation: Jul 10, 2025 at 01:46 PM
--

CREATE TABLE `calendars` (
  `id` int(10) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `color` varchar(20) NOT NULL DEFAULT '#3788d8',
  `is_visible` tinyint(1) NOT NULL DEFAULT 1,
  `is_public` tinyint(1) NOT NULL DEFAULT 1,
  `owner_id` int(10) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- RELATIONSHIPS FOR TABLE `calendars`:
--   `owner_id`
--       `users` -> `id`
--

--
-- Dumping data for table `calendars`
--

INSERT INTO `calendars` (`id`, `name`, `description`, `color`, `is_visible`, `is_public`, `owner_id`, `created_at`, `updated_at`) VALUES
(1, 'Public Car Show Calendar NC', 'Public Car Show Calendar NC', '#ff0000', 1, 1, 3, '2025-06-16 18:33:22', '2025-06-16 18:33:22'),
(2, 'Shows - NC', 'Calendar for car shows in NC', '#3788d8', 1, 1, NULL, '2025-06-17 17:07:39', '2025-06-17 17:07:39'),
(4, 'Shows - AK', 'Calendar for car shows in AK', '#3788d8', 1, 1, NULL, '2025-07-09 21:17:59', '2025-07-09 21:17:59');
