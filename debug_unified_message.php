<?php
/**
 * Debug UnifiedMessageModel Loading
 */

require_once 'config/config.php';
require_once 'core/Database.php';

echo "<h1>Debug UnifiedMessageModel Loading</h1>\n";

echo "<h2>1. Check File Existence</h2>\n";
$files = [
    'models/UnifiedMessageModel.php',
    'models/NotificationModel.php', 
    'models/EnhancedTicketService.php',
    'core/ContentCleaner.php'
];

foreach ($files as $file) {
    $fullPath = APPROOT . '/' . $file;
    if (file_exists($fullPath)) {
        echo "<p style='color: green;'>✓ File exists: $file</p>\n";
    } else {
        echo "<p style='color: red;'>✗ File missing: $file</p>\n";
    }
}

echo "<h2>2. Try Loading UnifiedMessageModel Step by Step</h2>\n";

try {
    echo "<p>Loading Database...</p>\n";
    $db = new Database();
    echo "<p style='color: green;'>✓ Database loaded</p>\n";
    
    echo "<p>Loading NotificationModel...</p>\n";
    require_once APPROOT . '/models/NotificationModel.php';
    $notificationModel = new NotificationModel();
    echo "<p style='color: green;'>✓ NotificationModel loaded</p>\n";
    
    echo "<p>Loading EnhancedTicketService...</p>\n";
    require_once APPROOT . '/models/EnhancedTicketService.php';
    $ticketService = new EnhancedTicketService();
    echo "<p style='color: green;'>✓ EnhancedTicketService loaded</p>\n";
    
    echo "<p>Loading ContentCleaner...</p>\n";
    require_once APPROOT . '/core/ContentCleaner.php';
    echo "<p style='color: green;'>✓ ContentCleaner loaded</p>\n";
    
    echo "<p>Loading UnifiedMessageModel...</p>\n";
    require_once APPROOT . '/models/UnifiedMessageModel.php';
    $unifiedMessageModel = new UnifiedMessageModel();
    echo "<p style='color: green;'>✓ UnifiedMessageModel loaded successfully!</p>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<p style='color: red;'>Stack trace:</p>\n";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>\n";
}

echo "<h2>3. Try Loading JudgingConflictModel</h2>\n";

try {
    echo "<p>Loading JudgingConflictModel...</p>\n";
    require_once APPROOT . '/models/JudgingConflictModel.php';
    $conflictModel = new JudgingConflictModel();
    echo "<p style='color: green;'>✓ JudgingConflictModel loaded successfully!</p>\n";
    
    // Test a method
    $stats = $conflictModel->getConflictStatistics();
    echo "<p style='color: green;'>✓ getConflictStatistics() method works</p>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error loading JudgingConflictModel: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<p style='color: red;'>Stack trace:</p>\n";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>\n";
}

echo "<h2>4. Check Required Database Tables</h2>\n";

$tables = [
    'user_notification_preferences',
    'notification_queue',
    'messages',
    'judging_conflicts'
];

foreach ($tables as $table) {
    try {
        $db->query("SELECT COUNT(*) as count FROM $table LIMIT 1");
        $result = $db->single();
        echo "<p style='color: green;'>✓ Table $table exists (count: {$result->count})</p>\n";
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Table $table error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    }
}

echo "<hr>\n";
echo "<p><a href='" . BASE_URL . "'>← Return to Home</a></p>\n";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2 {
    color: #333;
}

p {
    margin: 5px 0;
}

pre {
    background: #f5f5f5;
    padding: 10px;
    border-radius: 4px;
    overflow-x: auto;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>