
-- --------------------------------------------------------

--
-- Table structure for table `layouts`
--
-- Creation: Jun 23, 2025 at 08:30 PM
--

CREATE TABLE `layouts` (
  `id` int(10) UNSIGNED NOT NULL,
  `type` varchar(50) NOT NULL,
  `name` varchar(100) NOT NULL,
  `content` text NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- RELATIONSHIPS FOR TABLE `layouts`:
--

--
-- Dumping data for table `layouts`
--

INSERT INTO `layouts` (`id`, `type`, `name`, `content`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'header', 'Default Header', '\r\n<header class=\"main-header\">\r\n    <nav class=\"navbar navbar-expand-lg navbar-dark bg-primary\">\r\n        <div class=\"container\">\r\n            <a class=\"navbar-brand\" href=\"<?php echo BASE_URL; ?>\">\r\n                <?php if (!empty($site_logo)): ?>\r\n                    <img src=\"<?php echo BASE_URL . $site_logo; ?>\" alt=\"<?php echo APP_NAME; ?>\" height=\"40\">\r\n                <?php else: ?>\r\n                    <?php echo APP_NAME; ?>\r\n                <?php endif; ?>\r\n            </a>\r\n            <button class=\"navbar-toggler\" type=\"button\" data-bs-toggle=\"collapse\" data-bs-target=\"#navbarMain\" aria-controls=\"navbarMain\" aria-expanded=\"false\" aria-label=\"Toggle navigation\">\r\n                <span class=\"navbar-toggler-icon\"></span>\r\n            </button>\r\n            <div class=\"collapse navbar-collapse\" id=\"navbarMain\">\r\n                <ul class=\"navbar-nav me-auto mb-2 mb-lg-0\">\r\n                    <li class=\"nav-item\">\r\n                        <a class=\"nav-link <?php echo ($currentPage == \'home\') ? \'active\' : \'\'; ?>\" href=\"<?php echo BASE_URL; ?>\">Home</a>\r\n                    </li>\r\n                    <li class=\"nav-item\">\r\n                        <a class=\"nav-link <?php echo ($currentPage == \'shows\') ? \'active\' : \'\'; ?>\" href=\"<?php echo BASE_URL; ?>/show/index\">Shows</a>\r\n                    </li>\r\n                    <?php if (isLoggedIn()): ?>\r\n                        <li class=\"nav-item dropdown\">\r\n                            <a class=\"nav-link dropdown-toggle\" href=\"#\" id=\"navbarDropdown\" role=\"button\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\">\r\n                                My Account\r\n                            </a>\r\n                            <ul class=\"dropdown-menu\" aria-labelledby=\"navbarDropdown\">\r\n                                <?php if (hasRole(\'admin\')): ?>\r\n                                    <li><a class=\"dropdown-item\" href=\"<?php echo BASE_URL; ?>/admin/dashboard\">Admin Dashboard</a></li>\r\n                                    <li><hr class=\"dropdown-divider\"></li>\r\n                                <?php endif; ?>\r\n                                <?php if (hasRole(\'coordinator\')): ?>\r\n                                    <li><a class=\"dropdown-item\" href=\"<?php echo BASE_URL; ?>/coordinator/dashboard\">Coordinator Dashboard</a></li>\r\n                                    <li><hr class=\"dropdown-divider\"></li>\r\n                                <?php endif; ?>\r\n                                <?php if (hasRole(\'judge\')): ?>\r\n                                    <li><a class=\"dropdown-item\" href=\"<?php echo BASE_URL; ?>/judge/dashboard\">Judge Dashboard</a></li>\r\n                                    <li><hr class=\"dropdown-divider\"></li>\r\n                                <?php endif; ?>\r\n                                <li><a class=\"dropdown-item\" href=\"<?php echo BASE_URL; ?>/user/dashboard\">My Dashboard</a></li>\r\n                                <li><a class=\"dropdown-item\" href=\"<?php echo BASE_URL; ?>/user/profile\">My Profile</a></li>\r\n                                <li><a class=\"dropdown-item\" href=\"<?php echo BASE_URL; ?>/user/vehicles\">My Vehicles</a></li>\r\n                                <li><a class=\"dropdown-item\" href=\"<?php echo BASE_URL; ?>/user/registrations\">My Registrations</a></li>\r\n                                <li><hr class=\"dropdown-divider\"></li>\r\n                                <li><a class=\"dropdown-item\" href=\"<?php echo BASE_URL; ?>/auth/logout\">Logout</a></li>\r\n                            </ul>\r\n                        </li>\r\n                    <?php else: ?>\r\n                        <li class=\"nav-item\">\r\n                            <a class=\"nav-link <?php echo ($currentPage == \'login\') ? \'active\' : \'\'; ?>\" href=\"<?php echo BASE_URL; ?>/auth/login\">Login</a>\r\n                        </li>\r\n                        <li class=\"nav-item\">\r\n                            <a class=\"nav-link <?php echo ($currentPage == \'register\') ? \'active\' : \'\'; ?>\" href=\"<?php echo BASE_URL; ?>/auth/register\">Register</a>\r\n                        </li>\r\n                    <?php endif; ?>\r\n                </ul>\r\n                <form class=\"d-flex\" action=\"<?php echo BASE_URL; ?>/show/search\" method=\"get\">\r\n                    <input class=\"form-control me-2\" type=\"search\" name=\"q\" placeholder=\"Search shows...\" aria-label=\"Search\">\r\n                    <button class=\"btn btn-outline-light\" type=\"submit\">Search</button>\r\n                </form>\r\n            </div>\r\n        </div>\r\n    </nav>\r\n</header>', 1, '2025-05-19 21:09:03', '2025-05-19 21:09:03'),
(2, 'footer', 'Default Footer', '\r\n<footer class=\"main-footer bg-dark text-white py-4 mt-5\">\r\n    <div class=\"container\">\r\n        <div class=\"row\">\r\n            <div class=\"col-md-4\">\r\n                <h5>About Us</h5>\r\n                <p>Events and Shows Management System is a comprehensive platform for managing car shows, events, judging, and vehicle registrations.</p>\r\n            </div>\r\n            <div class=\"col-md-4\">\r\n                <h5>Quick Links</h5>\r\n                <ul class=\"list-unstyled\">\r\n                    <li><a href=\"<?php echo BASE_URL; ?>\" class=\"text-white\">Home</a></li>\r\n                    <li><a href=\"<?php echo BASE_URL; ?>/show/index\" class=\"text-white\">Shows</a></li>\r\n                    <?php if (isLoggedIn()): ?>\r\n                        <li><a href=\"<?php echo BASE_URL; ?>/user/dashboard\" class=\"text-white\">My Dashboard</a></li>\r\n                    <?php else: ?>\r\n                        <li><a href=\"<?php echo BASE_URL; ?>/auth/login\" class=\"text-white\">Login</a></li>\r\n                        <li><a href=\"<?php echo BASE_URL; ?>/auth/register\" class=\"text-white\">Register</a></li>\r\n                    <?php endif; ?>\r\n                </ul>\r\n            </div>\r\n            <div class=\"col-md-4\">\r\n                <h5>Contact Us</h5>\r\n                <p>If you have any questions or need assistance, please contact us.</p>\r\n                <p><a href=\"mailto:<EMAIL>\" class=\"text-white\"><EMAIL></a></p>\r\n            </div>\r\n        </div>\r\n        <hr>\r\n        <div class=\"row\">\r\n            <div class=\"col-md-6\">\r\n                <p>&copy; <?php echo date(\'Y\'); ?> <?php echo !empty($footer_text) ? $footer_text : APP_NAME; ?>. All rights reserved.</p>\r\n            </div>\r\n            <div class=\"col-md-6 text-md-end\">\r\n                <?php if (empty($enable_white_labeling) || $enable_white_labeling != \'1\'): ?>\r\n                    <p>Powered by <a href=\"https://example.com\" class=\"text-white\">Events and Shows Management System</a></p>\r\n                <?php endif; ?>\r\n            </div>\r\n        </div>\r\n    </div>\r\n</footer>', 1, '2025-05-19 21:09:03', '2025-05-19 21:09:03');
