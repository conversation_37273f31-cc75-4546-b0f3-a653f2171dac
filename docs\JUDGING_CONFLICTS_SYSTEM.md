# Judging Conflicts & Dispute Management System

## Overview

The Judging Conflicts system provides a comprehensive solution for managing disputes and conflicts related to judging scores in car shows. It includes automatic conflict detection, manual reporting capabilities, and a complete resolution workflow.

## Features

### For Vehicle Owners
- **Report Conflicts**: Easy-to-use form for reporting judging disputes
- **Track Reports**: Personal dashboard to monitor conflict status
- **Quick Access**: "Report Issue" button directly on score viewing pages
- **Time Limits**: 72-hour window for reporting conflicts after results are posted

### For Administrators & Coordinators
- **Management Dashboard**: Comprehensive interface for managing all conflicts
- **Automatic Detection**: System automatically detects score discrepancies
- **Priority Management**: Urgent, high, normal, and low priority levels
- **Status Tracking**: Open, under review, resolved, dismissed, escalated
- **Comment System**: Internal and public comments for resolution tracking
- **Statistics**: Analytics and reporting on conflict trends

### Automatic Detection
- **Score Discrepancies**: Detects when judges' scores differ significantly
- **Configurable Thresholds**: Adjustable percentage thresholds for detection
- **Real-time Triggers**: Runs automatically when scores are finalized
- **Smart Filtering**: Avoids duplicate conflicts for the same issues

## Installation

1. Run the installation script:
   ```
   http://yoursite.com/install_judging_conflicts.php
   ```

2. The script will:
   - Create necessary database tables
   - Add configuration settings
   - Set up indexes for performance
   - Verify installation

## Database Tables

### `judging_conflicts`
Main conflicts table storing all dispute information:
- Basic conflict details (title, description, type)
- Status and priority tracking
- User assignments and resolution data
- Automatic detection metadata

### `judging_conflict_comments`
Comment system for conflict resolution:
- User comments and responses
- Internal vs public comment types
- Timestamp tracking

### `judging_conflict_related_scores`
Links conflicts to specific scores:
- Many-to-many relationship
- Tracks which scores are involved in disputes

### `judging_conflict_related_judges`
Links conflicts to specific judges:
- Many-to-many relationship
- Tracks which judges are involved

## Configuration Settings

The system adds several configurable settings:

- `conflict_auto_detection_enabled`: Enable/disable automatic detection
- `conflict_score_discrepancy_threshold`: Percentage threshold for detection (default: 15%)
- `conflict_notification_enabled`: Send notifications for conflicts
- `conflict_time_limit_hours`: Time limit for reporting (default: 72 hours)
- `conflict_escalation_threshold_hours`: Auto-escalation time (default: 48 hours)

## URL Structure

### Admin/Coordinator URLs
- `/judging_conflict/dashboard` - Main management dashboard
- `/judging_conflict/viewConflict/{id}` - Individual conflict details
- `/judging_conflict/update/{id}` - Update conflict (AJAX)
- `/judging_conflict/detectConflicts/{showId}` - Run detection (AJAX)

### User URLs
- `/judging_conflict/report` - Report new conflict
- `/judging_conflict/my_reports` - View personal reports
- `/judging_conflict/addComment/{id}` - Add comment (AJAX)

## Conflict Types

1. **Score Discrepancy**: Significant differences between judges' scores
2. **Assignment Conflict**: Issues with judge assignments or conflicts of interest
3. **Scoring Dispute**: Disagreements about scoring criteria or methodology
4. **Technical Error**: System errors or data corruption affecting scores
5. **Owner Complaint**: Vehicle owner disputes about their scores
6. **Judge Concern**: Concerns raised by judges about the process

## Workflow

### Automatic Detection
1. Judge finalizes scores
2. System triggers conflict detection
3. Compares scores against thresholds
4. Creates conflicts for significant discrepancies
5. Notifies administrators

### Manual Reporting
1. User identifies issue
2. Fills out conflict report form
3. System validates and creates conflict
4. Assigns to appropriate administrator
5. Tracks through resolution

### Resolution Process
1. Administrator reviews conflict
2. Investigates related scores and judges
3. Communicates via comment system
4. Takes corrective action if needed
5. Documents resolution and closes conflict

## Integration Points

### Navigation
- Admin dashboard quick link
- Management dropdown menu item
- User account menu item

### Score Viewing
- "Report Issue" button on user score pages
- Direct links to conflict reporting

### Judging Workflow
- Automatic detection on score finalization
- Integration with existing judging models

## Security Features

- CSRF protection on all forms
- Role-based access control
- Input validation and sanitization
- SQL injection prevention
- XSS protection

## Mobile Support

All interfaces are fully responsive and mobile-optimized:
- Touch-friendly buttons and forms
- Responsive tables and layouts
- Mobile-first design approach
- Optimized for various screen sizes

## Performance Considerations

- Database indexes for fast queries
- Efficient pagination for large datasets
- AJAX for real-time updates
- Optimized SQL queries
- Caching where appropriate

## Troubleshooting

### Common Issues

1. **Installation Fails**
   - Check database permissions
   - Verify table creation rights
   - Review error logs

2. **Automatic Detection Not Working**
   - Check `conflict_auto_detection_enabled` setting
   - Verify threshold settings
   - Review error logs for detection failures

3. **Navigation Links Missing**
   - Clear browser cache
   - Check user role permissions
   - Verify header.php modifications

### Debug Mode

When `DEBUG_MODE` is enabled, the system provides detailed logging:
- Conflict detection process
- Database operations
- User actions and permissions
- Error details and stack traces

## Future Enhancements

Potential future improvements:
- Email notifications for conflict updates
- Advanced analytics and reporting
- Integration with external judging systems
- Automated resolution suggestions
- Machine learning for pattern detection

## Support

For technical support or questions about the judging conflicts system:
1. Check the error logs for detailed information
2. Review the database for data integrity
3. Verify configuration settings
4. Test with different user roles and permissions