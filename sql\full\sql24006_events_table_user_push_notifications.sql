
-- --------------------------------------------------------

--
-- Table structure for table `user_push_notifications`
--
-- Creation: Jul 12, 2025 at 04:48 PM
--

CREATE TABLE `user_push_notifications` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `event_id` int(11) DEFAULT NULL,
  `event_type` enum('calendar_event','car_show','test') DEFAULT NULL,
  `is_read` tinyint(1) DEFAULT 0,
  `notification_center_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- RELATIONSHIPS FOR TABLE `user_push_notifications`:
--

--
-- Dumping data for table `user_push_notifications`
--

INSERT INTO `user_push_notifications` (`id`, `user_id`, `title`, `message`, `event_id`, `event_type`, `is_read`, `notification_center_id`, `created_at`) VALUES
(380, 1, '🎉 Test Message Subject', 'This is a test message to verify that both the subject and full message content are properly sent...', 33, '', 0, NULL, '2025-07-15 13:10:55'),
(381, 1, 'Test Message', 'This is a simple test message.', 34, '', 0, NULL, '2025-07-15 13:11:24'),
(382, 1, 'Test Message', 'This is a simple test message.', 35, '', 0, NULL, '2025-07-15 13:16:30'),
(383, 1, 'Test Message', 'This is a simple test message.', 36, '', 0, NULL, '2025-07-15 13:16:38'),
(384, 1, 'Test Message', 'This is a simple test message.', 37, '', 0, NULL, '2025-07-15 13:16:39'),
(385, 1, 'Test Message', 'This is a simple test message.', 38, '', 0, NULL, '2025-07-15 13:23:47');
