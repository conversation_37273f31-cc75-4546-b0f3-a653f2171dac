<?php
/**
 * Judging Conflicts Installation Script
 * 
 * This script installs the judging conflicts system by creating the necessary database tables.
 */

// Include configuration
require_once 'config/config.php';
require_once 'core/Database.php';

echo "<h1>Judging Conflicts System Installation</h1>\n";

// Check if debug mode is requested
$debugMode = isset($_GET['debug']) && $_GET['debug'] === '1';

if ($debugMode) {
    echo "<p style='color: orange;'><strong>Debug Mode Enabled</strong> - Detailed output will be shown</p>\n";
}

try {
    // Create database connection
    $db = new Database();
    
    // Read the migration file
    $migrationFile = __DIR__ . '/database/migrations/add_judging_conflicts_tables.sql';
    
    if (!file_exists($migrationFile)) {
        throw new Exception("Migration file not found: $migrationFile");
    }
    
    $sql = file_get_contents($migrationFile);
    
    if ($sql === false) {
        throw new Exception("Failed to read migration file");
    }
    
    echo "<p>Running migration...</p>\n";
    
    // Clean up the SQL and split into individual statements
    // Remove comments and empty lines
    $lines = explode("\n", $sql);
    $cleanedLines = [];
    
    foreach ($lines as $line) {
        $line = trim($line);
        // Skip empty lines and comment lines
        if (empty($line) || preg_match('/^\s*--/', $line)) {
            continue;
        }
        $cleanedLines[] = $line;
    }
    
    $cleanedSql = implode("\n", $cleanedLines);
    
    // Split by semicolon but be careful with semicolons inside strings
    $statements = [];
    $currentStatement = '';
    $inString = false;
    $stringChar = '';
    
    for ($i = 0; $i < strlen($cleanedSql); $i++) {
        $char = $cleanedSql[$i];
        
        if (!$inString && ($char === '"' || $char === "'")) {
            $inString = true;
            $stringChar = $char;
        } elseif ($inString && $char === $stringChar) {
            // Check if it's escaped
            if ($i > 0 && $cleanedSql[$i-1] !== '\\') {
                $inString = false;
                $stringChar = '';
            }
        } elseif (!$inString && $char === ';') {
            $currentStatement = trim($currentStatement);
            if (!empty($currentStatement)) {
                $statements[] = $currentStatement;
            }
            $currentStatement = '';
            continue;
        }
        
        $currentStatement .= $char;
    }
    
    // Add the last statement if it doesn't end with semicolon
    $currentStatement = trim($currentStatement);
    if (!empty($currentStatement)) {
        $statements[] = $currentStatement;
    }
    
    // Filter out empty statements
    $statements = array_filter($statements, function($stmt) {
        return !empty(trim($stmt));
    });
    
    $successCount = 0;
    $errorCount = 0;
    
    foreach ($statements as $statement) {
        if (empty(trim($statement))) {
            continue;
        }
        
        if ($debugMode) {
            echo "<div style='background: #f5f5f5; padding: 10px; margin: 5px 0; border-left: 3px solid #ccc;'>";
            echo "<strong>Executing:</strong><br>";
            echo "<code style='font-size: 0.9em;'>" . htmlspecialchars(substr($statement, 0, 200)) . (strlen($statement) > 200 ? '...' : '') . "</code>";
            echo "</div>\n";
        }
        
        try {
            $db->query($statement);
            $result = $db->execute();
            
            if ($result) {
                $successCount++;
                
                // Extract table/view name for logging
                if (preg_match('/CREATE\s+(?:TABLE|VIEW)\s+(?:IF\s+NOT\s+EXISTS\s+)?(?:OR\s+REPLACE\s+)?`?(\w+)`?/i', $statement, $matches)) {
                    echo "<p style='color: green;'>✓ Created: {$matches[1]}</p>\n";
                } elseif (preg_match('/INSERT\s+IGNORE\s+INTO\s+`?(\w+)`?/i', $statement, $matches)) {
                    echo "<p style='color: blue;'>✓ Inserted settings into: {$matches[1]}</p>\n";
                } elseif (preg_match('/CREATE\s+INDEX\s+(?:IF\s+NOT\s+EXISTS\s+)?`?(\w+)`?\s+ON\s+`?(\w+)`?/i', $statement, $matches)) {
                    echo "<p style='color: orange;'>✓ Created index: {$matches[1]} on {$matches[2]}</p>\n";
                } else {
                    echo "<p style='color: green;'>✓ Executed statement successfully</p>\n";
                }
            } else {
                $errorCount++;
                echo "<p style='color: red;'>✗ Failed to execute statement (no exception thrown)</p>\n";
                echo "<p style='color: gray; font-size: 0.9em;'>Statement: " . htmlspecialchars(substr($statement, 0, 100)) . "...</p>\n";
            }
            
        } catch (Exception $e) {
            $errorCount++;
            echo "<p style='color: red;'>✗ Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
            echo "<p style='color: gray; font-size: 0.9em;'>Statement: " . htmlspecialchars(substr($statement, 0, 100)) . "...</p>\n";
            
            // Continue with other statements even if one fails
        }
    }
    
    echo "<hr>\n";
    echo "<h2>Installation Summary</h2>\n";
    echo "<p><strong>Successful operations:</strong> $successCount</p>\n";
    echo "<p><strong>Failed operations:</strong> $errorCount</p>\n";
    
    if ($errorCount === 0) {
        echo "<p style='color: green; font-weight: bold;'>✓ Judging Conflicts system installed successfully!</p>\n";
        echo "<h3>Next Steps:</h3>\n";
        echo "<ul>\n";
        echo "<li>Access the admin dashboard to configure conflict settings</li>\n";
        echo "<li>Visit <a href='" . BASE_URL . "/judging_conflict/dashboard'>Judging Conflicts Dashboard</a> to manage conflicts</li>\n";
        echo "<li>Users can report conflicts from their score pages</li>\n";
        echo "<li>Automatic conflict detection will run when scores are finalized</li>\n";
        echo "</ul>\n";
    } else {
        echo "<p style='color: orange; font-weight: bold;'>⚠ Installation completed with some errors. Please review the errors above.</p>\n";
    }
    
    // Test database connectivity
    echo "<h3>System Test</h3>\n";
    
    try {
        $db->query("SELECT COUNT(*) as count FROM judging_conflicts");
        $result = $db->single();
        echo "<p style='color: green;'>✓ Database tables accessible</p>\n";
        
        $db->query("SELECT COUNT(*) as count FROM settings WHERE name LIKE 'conflict_%'");
        $result = $db->single();
        echo "<p style='color: green;'>✓ Configuration settings installed: " . $result->count . " settings</p>\n";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Database test failed: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'><strong>Installation failed:</strong> " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<p>Please check your database configuration and try again.</p>\n";
}

echo "<hr>\n";
echo "<p><a href='" . BASE_URL . "'>← Return to Home</a></p>\n";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
}

p {
    margin: 5px 0;
}

hr {
    margin: 20px 0;
    border: none;
    border-top: 1px solid #ccc;
}

ul {
    padding-left: 20px;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}
</style>