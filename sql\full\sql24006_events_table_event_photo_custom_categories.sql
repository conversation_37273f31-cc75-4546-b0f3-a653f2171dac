
-- --------------------------------------------------------

--
-- Table structure for table `event_photo_custom_categories`
--
-- Creation: Aug 01, 2025 at 09:08 PM
-- Last update: Aug 01, 2025 at 09:08 PM
--

CREATE TABLE `event_photo_custom_categories` (
  `id` int(10) UNSIGNED NOT NULL,
  `category_key` varchar(50) NOT NULL,
  `category_label` varchar(100) NOT NULL,
  `emoji_icon` varchar(10) NOT NULL,
  `sort_order` int(11) DEFAULT 0,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_uca1400_ai_ci;

--
-- RELATIONSHIPS FOR TABLE `event_photo_custom_categories`:
--

--
-- Dumping data for table `event_photo_custom_categories`
--

INSERT INTO `event_photo_custom_categories` (`id`, `category_key`, `category_label`, `emoji_icon`, `sort_order`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'vehicle', 'Vehicle', '🚗', 1, 1, '2025-08-01 21:08:27', '2025-08-01 21:08:27'),
(2, 'atmosphere', 'Atmosphere', '🎪', 2, 1, '2025-08-01 21:08:27', '2025-08-01 21:08:27'),
(3, 'awards', 'Awards', '🏆', 3, 1, '2025-08-01 21:08:27', '2025-08-01 21:08:27'),
(4, 'vendors', 'Vendors', '🍔', 4, 1, '2025-08-01 21:08:27', '2025-08-01 21:08:27'),
(5, 'people', 'People', '👥', 5, 1, '2025-08-01 21:08:27', '2025-08-01 21:08:27'),
(6, 'engine', 'Engine Bay', '🔧', 6, 1, '2025-08-01 21:08:27', '2025-08-01 21:08:27'),
(7, 'wheels', 'Wheels & Tires', '⚙️', 7, 1, '2025-08-01 21:08:27', '2025-08-01 21:08:27'),
(8, 'interior', 'Interior', '🪑', 8, 1, '2025-08-01 21:08:27', '2025-08-01 21:08:27'),
(9, 'exterior', 'Exterior', '✨', 9, 1, '2025-08-01 21:08:27', '2025-08-01 21:08:27'),
(10, 'paint', 'Paint & Graphics', '🎨', 10, 1, '2025-08-01 21:08:27', '2025-08-01 21:08:27'),
(11, 'suspension', 'Suspension', '🏁', 11, 1, '2025-08-01 21:08:27', '2025-08-01 21:08:27'),
(12, 'exhaust', 'Exhaust', '💨', 12, 1, '2025-08-01 21:08:27', '2025-08-01 21:08:27'),
(13, 'sound_system', 'Sound System', '🔊', 13, 1, '2025-08-01 21:08:27', '2025-08-01 21:08:27'),
(14, 'racing', 'Racing Action', '🏎️', 14, 1, '2025-08-01 21:08:27', '2025-08-01 21:08:27'),
(15, 'burnout', 'Burnouts', '🔥', 15, 1, '2025-08-01 21:08:27', '2025-08-01 21:08:27'),
(16, 'dyno', 'Dyno Runs', '📊', 16, 1, '2025-08-01 21:08:27', '2025-08-01 21:08:27'),
(17, 'judging', 'Judging', '👨‍⚖️', 17, 1, '2025-08-01 21:08:27', '2025-08-01 21:08:27'),
(18, 'setup', 'Setup/Prep', '🛠️', 18, 1, '2025-08-01 21:08:27', '2025-08-01 21:08:27'),
(19, 'crowd', 'Crowd Shots', '👥', 19, 1, '2025-08-01 21:08:27', '2025-08-01 21:08:27'),
(20, 'sponsors', 'Sponsors', '🏢', 20, 1, '2025-08-01 21:08:27', '2025-08-01 21:08:27');
