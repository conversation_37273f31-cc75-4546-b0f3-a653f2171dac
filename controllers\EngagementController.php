<?php
/**
 * Engagement Controller
 * 
 * Handles photo engagement features: likes, comments, shares, tags, favorites
 */

class EngagementController extends Controller {
    
    private $db;
    private $auth;
    
    public function __construct() {
        $this->db = new Database();
        $this->auth = new Auth();
    }

    /**
     * Simple helper to check if user is logged in
     */
    private function isUserLoggedIn() {
        return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
    }

    /**
     * Simple helper to get current user ID
     */
    private function getCurrentUserId() {
        return $this->isUserLoggedIn() ? $_SESSION['user_id'] : null;
    }
    
    /**
     * Toggle like on a photo
     */
    public function toggleLike() {
        header('Content-Type: application/json');
        
        if (!$this->auth->isLoggedIn()) {
            http_response_code(401);
            echo json_encode(['error' => 'Login required']);
            return;
        }
        
        $input = json_decode(file_get_contents('php://input'), true);
        $photoId = $input['photo_id'] ?? null;
        $userId = $this->auth->getCurrentUserId();
        
        if (!$photoId) {
            http_response_code(400);
            echo json_encode(['error' => 'Photo ID required']);
            return;
        }
        
        try {
            // Check if already liked
            $this->db->query('SELECT id FROM photo_likes WHERE photo_id = :photo_id AND user_id = :user_id');
            $this->db->bind(':photo_id', $photoId);
            $this->db->bind(':user_id', $userId);
            $existingLike = $this->db->single();
            
            if ($existingLike) {
                // Unlike
                $this->db->query('DELETE FROM photo_likes WHERE photo_id = :photo_id AND user_id = :user_id');
                $this->db->bind(':photo_id', $photoId);
                $this->db->bind(':user_id', $userId);
                $this->db->execute();
                
                $liked = false;
            } else {
                // Like
                $this->db->query('INSERT INTO photo_likes (photo_id, user_id) VALUES (:photo_id, :user_id)');
                $this->db->bind(':photo_id', $photoId);
                $this->db->bind(':user_id', $userId);
                $this->db->execute();
                
                $liked = true;
                
                // Create notification for photo owner
                $this->createEngagementNotification($photoId, 'like', $userId);
            }
            
            // Update engagement stats
            $this->updateEngagementStats($photoId);
            
            // Get updated like count
            $likeCount = $this->getLikeCount($photoId);
            
            echo json_encode([
                'success' => true,
                'liked' => $liked,
                'like_count' => $likeCount
            ]);
            
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => 'Failed to toggle like']);
        }
    }
    
    /**
     * Add comment to photo
     */
    public function addComment() {
        header('Content-Type: application/json');
        
        if (!$this->auth->isLoggedIn()) {
            http_response_code(401);
            echo json_encode(['error' => 'Login required']);
            return;
        }
        
        $input = json_decode(file_get_contents('php://input'), true);
        $photoId = $input['photo_id'] ?? null;
        $comment = trim($input['comment'] ?? '');
        $parentCommentId = $input['parent_comment_id'] ?? null;
        $userId = $this->auth->getCurrentUserId();
        
        if (!$photoId || !$comment) {
            http_response_code(400);
            echo json_encode(['error' => 'Photo ID and comment required']);
            return;
        }
        
        try {
            // Extract mentions (@username)
            $mentions = $this->extractMentions($comment);
            
            // Insert comment (explicitly set is_edited to 0 for new comments)
            $this->db->query('INSERT INTO photo_comments (photo_id, user_id, comment, parent_comment_id, mentions, is_edited)
                             VALUES (:photo_id, :user_id, :comment, :parent_comment_id, :mentions, 0)');
            $this->db->bind(':photo_id', $photoId);
            $this->db->bind(':user_id', $userId);
            $this->db->bind(':comment', $comment);
            $this->db->bind(':parent_comment_id', $parentCommentId);
            $this->db->bind(':mentions', json_encode($mentions));

            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("Adding comment: photo_id=$photoId, user_id=$userId, comment='$comment'");
            }

            $this->db->execute();
            
            $commentId = $this->db->lastInsertId();
            
            // Create notifications
            if ($parentCommentId) {
                $this->createEngagementNotification($photoId, 'reply', $userId, $parentCommentId);
            } else {
                $this->createEngagementNotification($photoId, 'comment', $userId);
            }
            
            // Create mention notifications
            foreach ($mentions as $mentionedUserId) {
                $this->createEngagementNotification($photoId, 'mention', $userId, null, $mentionedUserId);
            }
            
            // Update engagement stats
            $this->updateEngagementStats($photoId);
            
            // Get comment with user info
            $comment = $this->getCommentById($commentId);
            
            echo json_encode([
                'success' => true,
                'comment' => $comment
            ]);

        } catch (Exception $e) {
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("Error adding comment: " . $e->getMessage());
                error_log("Stack trace: " . $e->getTraceAsString());
            }
            http_response_code(500);
            echo json_encode([
                'error' => 'Failed to add comment',
                'debug' => defined('DEBUG_MODE') && DEBUG_MODE ? $e->getMessage() : null
            ]);
        }
    }
    
    /**
     * Get comments for a photo
     */
    public function getComments($photoId) {
        header('Content-Type: application/json');

        try {
            $this->db->query('SELECT c.*, u.name as user_name, u.id as user_id
                             FROM photo_comments c
                             JOIN users u ON c.user_id = u.id
                             WHERE c.photo_id = :photo_id AND c.is_approved = 1
                             ORDER BY c.created_at ASC');
            $this->db->bind(':photo_id', $photoId);
            $comments = $this->db->resultSet();

            $currentUserId = $this->auth->isLoggedIn() ? $this->auth->getCurrentUserId() : null;
            $isAdmin = $this->auth->isLoggedIn() ? $this->auth->hasRole('admin') : false;

            // Process profile images and permissions for each comment
            foreach ($comments as $comment) {
                $comment->profile_image_url = $this->getUserProfileImageUrl($comment->user_id);

                // Add permission flags
                $comment->can_edit = $currentUserId && (($comment->user_id == $currentUserId) || $isAdmin);
                $comment->can_delete = $currentUserId && (($comment->user_id == $currentUserId) || $isAdmin);

                // Format timestamps
                $comment->formatted_date = $this->formatCommentDate($comment->created_at);
                if ($comment->is_edited && $comment->edited_at) {
                    $comment->formatted_edited_date = $this->formatCommentDate($comment->edited_at);
                }
            }

            // Organize comments into threads
            $commentTree = $this->organizeCommentTree($comments);

            echo json_encode([
                'success' => true,
                'comments' => $commentTree,
                'current_user_id' => $currentUserId,
                'is_admin' => $isAdmin
            ]);

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => 'Failed to load comments']);
        }
    }

    /**
     * Format comment date for display
     */
    private function formatCommentDate($dateString) {
        $date = new DateTime($dateString);
        $now = new DateTime();
        $diff = $now->getTimestamp() - $date->getTimestamp();

        if ($diff < 60) {
            return 'Just now';
        } elseif ($diff < 3600) {
            $minutes = floor($diff / 60);
            return $minutes . 'm ago';
        } elseif ($diff < 86400) {
            $hours = floor($diff / 3600);
            return $hours . 'h ago';
        } elseif ($diff < 604800) {
            $days = floor($diff / 86400);
            return $days . 'd ago';
        } else {
            return $date->format('M j, Y \a\t g:i A');
        }
    }

    /**
     * Get user profile image URL using the existing system
     */
    private function getUserProfileImageUrl($userId) {
        // Use the existing helper function if available
        if (function_exists('getUserProfileImageUrl')) {
            return getUserProfileImageUrl($userId);
        }

        // Fallback implementation that matches the existing system
        // First check images table
        $this->db->query('SELECT file_path FROM images WHERE entity_type = :entity_type AND entity_id = :entity_id AND is_primary = 1');
        $this->db->bind(':entity_type', 'user');
        $this->db->bind(':entity_id', $userId);
        $image = $this->db->single();

        if ($image) {
            return BASE_URL . '/' . $image->file_path;
        }

        // Check users table profile_image field
        $this->db->query('SELECT profile_image FROM users WHERE id = :id');
        $this->db->bind(':id', $userId);
        $user = $this->db->single();

        if ($user && !empty($user->profile_image)) {
            // Handle different profile image formats
            if (strpos($user->profile_image, 'http') === 0) {
                return $user->profile_image; // Already a full URL
            } else {
                return BASE_URL . '/' . $user->profile_image;
            }
        }

        return null; // No profile image found
    }
    
    /**
     * Toggle favorite on a photo
     */
    public function toggleFavorite() {
        header('Content-Type: application/json');
        
        if (!$this->auth->isLoggedIn()) {
            http_response_code(401);
            echo json_encode(['error' => 'Login required']);
            return;
        }
        
        $input = json_decode(file_get_contents('php://input'), true);
        $photoId = $input['photo_id'] ?? null;
        $collection = $input['collection'] ?? 'My Favorites';
        $userId = $this->auth->getCurrentUserId();
        
        if (!$photoId) {
            http_response_code(400);
            echo json_encode(['error' => 'Photo ID required']);
            return;
        }
        
        try {
            // Check if already favorited
            $this->db->query('SELECT id FROM photo_favorites WHERE photo_id = :photo_id AND user_id = :user_id');
            $this->db->bind(':photo_id', $photoId);
            $this->db->bind(':user_id', $userId);
            $existingFavorite = $this->db->single();
            
            if ($existingFavorite) {
                // Remove from favorites
                $this->db->query('DELETE FROM photo_favorites WHERE photo_id = :photo_id AND user_id = :user_id');
                $this->db->bind(':photo_id', $photoId);
                $this->db->bind(':user_id', $userId);
                $this->db->execute();
                
                $favorited = false;
            } else {
                // Add to favorites
                $this->db->query('INSERT INTO photo_favorites (photo_id, user_id, collection_name) 
                                 VALUES (:photo_id, :user_id, :collection)');
                $this->db->bind(':photo_id', $photoId);
                $this->db->bind(':user_id', $userId);
                $this->db->bind(':collection', $collection);
                $this->db->execute();
                
                $favorited = true;
            }
            
            // Update engagement stats
            $this->updateEngagementStats($photoId);
            
            echo json_encode([
                'success' => true,
                'favorited' => $favorited
            ]);
            
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => 'Failed to toggle favorite']);
        }
    }
    
    /**
     * Track photo share
     */
    public function trackShare() {
        header('Content-Type: application/json');
        
        $input = json_decode(file_get_contents('php://input'), true);
        $photoId = $input['photo_id'] ?? null;
        $platform = $input['platform'] ?? null;
        $userId = $this->auth->isLoggedIn() ? $this->auth->getCurrentUserId() : null;
        
        if (!$photoId || !$platform) {
            http_response_code(400);
            echo json_encode(['error' => 'Photo ID and platform required']);
            return;
        }
        
        try {
            // Track the share
            $this->db->query('INSERT INTO photo_shares (photo_id, user_id, platform) 
                             VALUES (:photo_id, :user_id, :platform)');
            $this->db->bind(':photo_id', $photoId);
            $this->db->bind(':user_id', $userId);
            $this->db->bind(':platform', $platform);
            $this->db->execute();
            
            // Update engagement stats
            $this->updateEngagementStats($photoId);
            
            echo json_encode(['success' => true]);
            
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => 'Failed to track share']);
        }
    }

    /**
     * Edit a comment
     */
    public function editComment() {
        header('Content-Type: application/json');

        if (!$this->auth->isLoggedIn()) {
            http_response_code(401);
            echo json_encode(['error' => 'Login required']);
            return;
        }

        $input = json_decode(file_get_contents('php://input'), true);
        $commentId = $input['comment_id'] ?? null;
        $newComment = trim($input['comment'] ?? '');
        $userId = $this->auth->getCurrentUserId();

        if (!$commentId || !$newComment) {
            http_response_code(400);
            echo json_encode(['error' => 'Comment ID and new comment text required']);
            return;
        }

        try {
            // Get the comment to check ownership
            $this->db->query('SELECT * FROM photo_comments WHERE id = :comment_id');
            $this->db->bind(':comment_id', $commentId);
            $comment = $this->db->single();

            if (!$comment) {
                http_response_code(404);
                echo json_encode(['error' => 'Comment not found']);
                return;
            }

            // Check if user can edit (owner or admin)
            $canEdit = ($comment->user_id == $userId) || $this->auth->hasRole('admin');

            if (!$canEdit) {
                http_response_code(403);
                echo json_encode(['error' => 'Permission denied']);
                return;
            }

            // Extract mentions from new comment
            $mentions = $this->extractMentions($newComment);

            // Update the comment
            $this->db->query('UPDATE photo_comments
                             SET comment = :comment, mentions = :mentions, is_edited = 1, edited_at = NOW()
                             WHERE id = :comment_id');
            $this->db->bind(':comment', $newComment);
            $this->db->bind(':mentions', json_encode($mentions));
            $this->db->bind(':comment_id', $commentId);
            $this->db->execute();

            // Get updated comment with user info
            $updatedComment = $this->getCommentById($commentId);

            echo json_encode([
                'success' => true,
                'comment' => $updatedComment
            ]);

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => 'Failed to edit comment']);
        }
    }

    /**
     * Delete a comment
     */
    public function deleteComment() {
        header('Content-Type: application/json');

        if (!$this->auth->isLoggedIn()) {
            http_response_code(401);
            echo json_encode(['error' => 'Login required']);
            return;
        }

        $input = json_decode(file_get_contents('php://input'), true);
        $commentId = $input['comment_id'] ?? null;
        $userId = $this->auth->getCurrentUserId();

        if (!$commentId) {
            http_response_code(400);
            echo json_encode(['error' => 'Comment ID required']);
            return;
        }

        try {
            // Get the comment to check ownership and get photo_id
            $this->db->query('SELECT * FROM photo_comments WHERE id = :comment_id');
            $this->db->bind(':comment_id', $commentId);
            $comment = $this->db->single();

            if (!$comment) {
                http_response_code(404);
                echo json_encode(['error' => 'Comment not found']);
                return;
            }

            // Check if user can delete (owner or admin)
            $canDelete = ($comment->user_id == $userId) || $this->auth->hasRole('admin');

            if (!$canDelete) {
                http_response_code(403);
                echo json_encode(['error' => 'Permission denied']);
                return;
            }

            // Delete the comment (CASCADE will handle replies)
            $this->db->query('DELETE FROM photo_comments WHERE id = :comment_id');
            $this->db->bind(':comment_id', $commentId);
            $this->db->execute();

            // Update engagement stats
            $this->updateEngagementStats($comment->photo_id);

            echo json_encode(['success' => true]);

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => 'Failed to delete comment']);
        }
    }
    
    /**
     * Helper Methods
     */

    private function getLikeCount($photoId) {
        $this->db->query('SELECT COUNT(*) as count FROM photo_likes WHERE photo_id = :photo_id');
        $this->db->bind(':photo_id', $photoId);
        $result = $this->db->single();
        return $result->count ?? 0;
    }

    private function extractMentions($comment) {
        preg_match_all('/@(\w+)/', $comment, $matches);
        $mentions = [];

        if (!empty($matches[1])) {
            foreach ($matches[1] as $username) {
                $this->db->query('SELECT id FROM users WHERE name = :username OR email = :username');
                $this->db->bind(':username', $username);
                $user = $this->db->single();

                if ($user) {
                    $mentions[] = $user->id;
                }
            }
        }

        return array_unique($mentions);
    }

    private function createEngagementNotification($photoId, $type, $fromUserId, $commentId = null, $targetUserId = null) {
        // Get photo owner if no specific target
        if (!$targetUserId) {
            $this->db->query('SELECT user_id FROM images WHERE id = :photo_id');
            $this->db->bind(':photo_id', $photoId);
            $photo = $this->db->single();
            $targetUserId = $photo->user_id ?? null;
        }

        // Don't notify yourself
        if ($targetUserId == $fromUserId) {
            return;
        }

        if ($targetUserId) {
            $this->db->query('INSERT INTO engagement_notifications
                             (user_id, photo_id, type, from_user_id)
                             VALUES (:user_id, :photo_id, :type, :from_user_id)');
            $this->db->bind(':user_id', $targetUserId);
            $this->db->bind(':photo_id', $photoId);
            $this->db->bind(':type', $type);
            $this->db->bind(':from_user_id', $fromUserId);
            $this->db->execute();
        }
    }

    private function updateEngagementStats($photoId) {
        // Get current counts
        $this->db->query('SELECT
                         (SELECT COUNT(*) FROM photo_likes WHERE photo_id = :photo_id1) as likes_count,
                         (SELECT COUNT(*) FROM photo_comments WHERE photo_id = :photo_id2 AND is_approved = 1) as comments_count,
                         (SELECT COUNT(*) FROM photo_shares WHERE photo_id = :photo_id3) as shares_count,
                         (SELECT COUNT(*) FROM photo_favorites WHERE photo_id = :photo_id4) as favorites_count,
                         (SELECT COUNT(*) FROM photo_user_tags WHERE photo_id = :photo_id5 AND status = "approved") as tags_count');

        $this->db->bind(':photo_id1', $photoId);
        $this->db->bind(':photo_id2', $photoId);
        $this->db->bind(':photo_id3', $photoId);
        $this->db->bind(':photo_id4', $photoId);
        $this->db->bind(':photo_id5', $photoId);
        $stats = $this->db->single();

        // Update or insert stats
        $this->db->query('INSERT INTO photo_engagement_stats
                         (photo_id, likes_count, comments_count, shares_count, favorites_count, tags_count)
                         VALUES (:photo_id, :likes, :comments, :shares, :favorites, :tags)
                         ON DUPLICATE KEY UPDATE
                         likes_count = :likes2, comments_count = :comments2, shares_count = :shares2,
                         favorites_count = :favorites2, tags_count = :tags2');

        $this->db->bind(':photo_id', $photoId);
        $this->db->bind(':likes', $stats->likes_count);
        $this->db->bind(':comments', $stats->comments_count);
        $this->db->bind(':shares', $stats->shares_count);
        $this->db->bind(':favorites', $stats->favorites_count);
        $this->db->bind(':tags', $stats->tags_count);
        $this->db->bind(':likes2', $stats->likes_count);
        $this->db->bind(':comments2', $stats->comments_count);
        $this->db->bind(':shares2', $stats->shares_count);
        $this->db->bind(':favorites2', $stats->favorites_count);
        $this->db->bind(':tags2', $stats->tags_count);
        $this->db->execute();
    }

    private function getCommentById($commentId) {
        $this->db->query('SELECT c.*, u.name as user_name, u.id as user_id
                         FROM photo_comments c
                         JOIN users u ON c.user_id = u.id
                         WHERE c.id = :comment_id');
        $this->db->bind(':comment_id', $commentId);
        $comment = $this->db->single();

        if ($comment) {
            $comment->profile_image_url = $this->getUserProfileImageUrl($comment->user_id);
        }

        return $comment;
    }

    private function organizeCommentTree($comments) {
        $tree = [];
        $lookup = [];

        // First pass: create lookup and add top-level comments
        foreach ($comments as $comment) {
            $lookup[$comment->id] = $comment;
            $comment->replies = [];

            if (!$comment->parent_comment_id) {
                $tree[] = $comment;
            }
        }

        // Second pass: add replies to their parents
        foreach ($comments as $comment) {
            if ($comment->parent_comment_id && isset($lookup[$comment->parent_comment_id])) {
                $lookup[$comment->parent_comment_id]->replies[] = $comment;
            }
        }

        return $tree;
    }

    /**
     * Get engagement data for a photo
     */
    public function getEngagementData($photoId) {
        header('Content-Type: application/json');

        try {
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("EngagementController::getEngagementData - Photo ID: $photoId");
            }

            // Get engagement stats
            $this->db->query('SELECT * FROM photo_engagement_stats WHERE photo_id = :photo_id');
            $this->db->bind(':photo_id', $photoId);
            $stats = $this->db->single();

            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("EngagementController::getEngagementData - Stats found: " . ($stats ? 'yes' : 'no'));
            }

            // If no stats exist, create them
            if (!$stats) {
                $this->db->query('INSERT IGNORE INTO photo_engagement_stats (photo_id) VALUES (:photo_id)');
                $this->db->bind(':photo_id', $photoId);
                $this->db->execute();

                // Get the newly created stats
                $this->db->query('SELECT * FROM photo_engagement_stats WHERE photo_id = :photo_id');
                $this->db->bind(':photo_id', $photoId);
                $stats = $this->db->single();
            }

            // Get user's engagement status if logged in
            $userEngagement = [
                'liked' => false,
                'favorited' => false
            ];

            if ($this->isUserLoggedIn()) {
                $userId = $this->getCurrentUserId();

                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                    error_log("EngagementController::getEngagementData - User ID: $userId");
                }

                // Check if user liked
                $this->db->query('SELECT id FROM photo_likes WHERE photo_id = :photo_id AND user_id = :user_id');
                $this->db->bind(':photo_id', $photoId);
                $this->db->bind(':user_id', $userId);
                $userEngagement['liked'] = $this->db->single() ? true : false;

                // Check if user favorited
                $this->db->query('SELECT id FROM photo_favorites WHERE photo_id = :photo_id AND user_id = :user_id');
                $this->db->bind(':photo_id', $photoId);
                $this->db->bind(':user_id', $userId);
                $userEngagement['favorited'] = $this->db->single() ? true : false;
            }

            $response = [
                'success' => true,
                'stats' => $stats ?: (object)[
                    'likes_count' => 0,
                    'comments_count' => 0,
                    'shares_count' => 0,
                    'favorites_count' => 0,
                    'tags_count' => 0
                ],
                'user_engagement' => $userEngagement
            ];

            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("EngagementController::getEngagementData - Response: " . json_encode($response));
            }

            echo json_encode($response);

        } catch (Exception $e) {
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("EngagementController::getEngagementData - Error: " . $e->getMessage());
                error_log("EngagementController::getEngagementData - Stack trace: " . $e->getTraceAsString());
            }
            http_response_code(500);
            echo json_encode([
                'error' => 'Failed to load engagement data',
                'debug' => defined('DEBUG_MODE') && DEBUG_MODE ? $e->getMessage() : null
            ]);
        }
    }
}
