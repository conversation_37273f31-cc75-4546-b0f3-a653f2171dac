<?php
/**
 * Test Controller Methods
 */

require_once 'config/config.php';
require_once 'core/Database.php';

echo "<h1>Test Controller Methods</h1>\n";

try {
    echo "<p>Testing ShowModel methods...</p>\n";
    
    require_once APPROOT . '/models/ShowModel.php';
    $showModel = new ShowModel();
    echo "<p style='color: green;'>✓ ShowModel loaded</p>\n";
    
    // Test getShows method
    $shows = $showModel->getShows();
    echo "<p style='color: green;'>✓ getShows() works - found " . count($shows) . " shows</p>\n";
    
    // Test getShowsByCoordinator method
    $coordinatorShows = $showModel->getShowsByCoordinator(1); // Test with user ID 1
    echo "<p style='color: green;'>✓ getShowsByCoordinator() works - found " . count($coordinatorShows) . " shows</p>\n";
    
    // Test getShowsByJudge method
    $judgeShows = $showModel->getShowsByJudge(1); // Test with user ID 1
    echo "<p style='color: green;'>✓ getShowsByJudge() works - found " . count($judgeShows) . " shows</p>\n";
    
    echo "<hr>\n";
    echo "<p>Testing JudgingConflictController loading...</p>\n";
    
    require_once APPROOT . '/controllers/JudgingConflictController.php';
    echo "<p style='color: green;'>✓ JudgingConflictController loaded successfully</p>\n";
    
    echo "<hr>\n";
    echo "<h2>✅ SUCCESS!</h2>\n";
    echo "<p style='color: green; font-weight: bold;'>All controller methods are working properly!</p>\n";
    
    echo "<h3>Ready to test:</h3>\n";
    echo "<ul>\n";
    echo "<li><a href='" . BASE_URL . "/judging_conflict/dashboard'>Dashboard</a></li>\n";
    echo "<li><a href='" . BASE_URL . "/judging_conflict/report'>Report Conflict</a></li>\n";
    echo "</ul>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>\n";
}

echo "<hr>\n";
echo "<p><a href='" . BASE_URL . "'>← Return to Home</a></p>\n";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 600px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2 {
    color: #333;
}

p {
    margin: 5px 0;
}

ul {
    padding-left: 20px;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

pre {
    background: #f5f5f5;
    padding: 10px;
    border-radius: 4px;
    overflow-x: auto;
}
</style>