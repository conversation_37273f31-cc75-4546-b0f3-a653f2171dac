
-- --------------------------------------------------------

--
-- Table structure for table `form_templates`
--
-- Creation: Jun 23, 2025 at 08:30 PM
--

CREATE TABLE `form_templates` (
  `id` int(10) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `type` enum('show','vehicle','user','other','event') NOT NULL,
  `entity_id` int(11) DEFAULT NULL,
  `fields` text NOT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- RELATIONSHIPS FOR TABLE `form_templates`:
--

--
-- Dumping data for table `form_templates`
--

INSERT INTO `form_templates` (`id`, `name`, `type`, `entity_id`, `fields`, `is_active`, `created_at`, `updated_at`) VALUES
(9, 'Default Show Event Form', 'event', 0, '[{\"id\":\"name\",\"type\":\"text\",\"label\":\"Show Name\",\"required\":true,\"name\":\"name\",\"width\":\"col-md-6\"},{\"id\":\"address1\",\"label\":\"Address 1\",\"name\":\"address1\",\"type\":\"text\",\"placeholder\":\"\",\"default\":\"\",\"options\":\"\",\"required\":true,\"width\":\"col-md-6\"},{\"id\":\"address2\",\"label\":\"Address2\",\"name\":\"address2\",\"type\":\"text\",\"placeholder\":\"\",\"default\":\"\",\"options\":\"\",\"required\":false,\"width\":\"col-md-6\"},{\"id\":\"city\",\"label\":\"City\",\"name\":\"city\",\"type\":\"text\",\"placeholder\":\"\",\"default\":\"\",\"options\":\"\",\"required\":true,\"width\":\"col-md-4\"},{\"id\":\"state\",\"label\":\"State\",\"name\":\"state\",\"type\":\"select\",\"placeholder\":\"\",\"default\":\"\",\"options\":\"[  \\n  {\\\"value\\\":\\\"AK\\\",\\\"name\\\":\\\"Alaska\\\"}, \\n  {\\\"value\\\":\\\"AL\\\",\\\"name\\\":\\\"Alabama\\\"}, \\n  {\\\"value\\\":\\\"AR\\\",\\\"name\\\":\\\"Arkansas\\\"}, \\n  {\\\"value\\\":\\\"AS\\\",\\\"name\\\":\\\"American Samoa\\\"}, \\n  {\\\"value\\\":\\\"AZ\\\",\\\"name\\\":\\\"Arizona\\\"}, \\n  {\\\"value\\\":\\\"CA\\\",\\\"name\\\":\\\"California\\\"}, \\n  {\\\"value\\\":\\\"CO\\\",\\\"name\\\":\\\"Colorado\\\"}, \\n  {\\\"value\\\":\\\"CT\\\",\\\"name\\\":\\\"Connecticut\\\"}, \\n  {\\\"value\\\":\\\"DC\\\",\\\"name\\\":\\\"District of Columbia\\\"}, \\n  {\\\"value\\\":\\\"DE\\\",\\\"name\\\":\\\"Delaware\\\"}, \\n  {\\\"value\\\":\\\"FL\\\",\\\"name\\\":\\\"Florida\\\"}, \\n  {\\\"value\\\":\\\"GA\\\",\\\"name\\\":\\\"Georgia\\\"}, \\n  {\\\"value\\\":\\\"GU\\\",\\\"name\\\":\\\"Guam\\\"}, \\n  {\\\"value\\\":\\\"HI\\\",\\\"name\\\":\\\"Hawaii\\\"}, \\n  {\\\"value\\\":\\\"IA\\\",\\\"name\\\":\\\"Iowa\\\"}, \\n  {\\\"value\\\":\\\"ID\\\",\\\"name\\\":\\\"Idaho\\\"}, \\n  {\\\"value\\\":\\\"IL\\\",\\\"name\\\":\\\"Illinois\\\"}, \\n  {\\\"value\\\":\\\"IN\\\",\\\"name\\\":\\\"Indiana\\\"}, \\n  {\\\"value\\\":\\\"KS\\\",\\\"name\\\":\\\"Kansas\\\"}, \\n  {\\\"value\\\":\\\"KY\\\",\\\"name\\\":\\\"Kentucky\\\"}, \\n  {\\\"value\\\":\\\"LA\\\",\\\"name\\\":\\\"Louisiana\\\"}, \\n  {\\\"value\\\":\\\"MA\\\",\\\"name\\\":\\\"Massachusetts\\\"}, \\n  {\\\"value\\\":\\\"MD\\\",\\\"name\\\":\\\"Maryland\\\"}, \\n  {\\\"value\\\":\\\"ME\\\",\\\"name\\\":\\\"Maine\\\"}, \\n  {\\\"value\\\":\\\"MI\\\",\\\"name\\\":\\\"Michigan\\\"}, \\n  {\\\"value\\\":\\\"MN\\\",\\\"name\\\":\\\"Minnesota\\\"}, \\n  {\\\"value\\\":\\\"MO\\\",\\\"name\\\":\\\"Missouri\\\"}, \\n  {\\\"value\\\":\\\"MS\\\",\\\"name\\\":\\\"Mississippi\\\"}, \\n  {\\\"value\\\":\\\"MT\\\",\\\"name\\\":\\\"Montana\\\"}, \\n  {\\\"value\\\":\\\"NC\\\",\\\"name\\\":\\\"North Carolina\\\"}, \\n  {\\\"value\\\":\\\"ND\\\",\\\"name\\\":\\\"North Dakota\\\"}, \\n  {\\\"value\\\":\\\"NE\\\",\\\"name\\\":\\\"Nebraska\\\"}, \\n  {\\\"value\\\":\\\"NH\\\",\\\"name\\\":\\\"New Hampshire\\\"}, \\n  {\\\"value\\\":\\\"NJ\\\",\\\"name\\\":\\\"New Jersey\\\"}, \\n  {\\\"value\\\":\\\"NM\\\",\\\"name\\\":\\\"New Mexico\\\"}, \\n  {\\\"value\\\":\\\"NV\\\",\\\"name\\\":\\\"Nevada\\\"}, \\n  {\\\"value\\\":\\\"NY\\\",\\\"name\\\":\\\"New York\\\"}, \\n  {\\\"value\\\":\\\"OH\\\",\\\"name\\\":\\\"Ohio\\\"}, \\n  {\\\"value\\\":\\\"OK\\\",\\\"name\\\":\\\"Oklahoma\\\"}, \\n  {\\\"value\\\":\\\"OR\\\",\\\"name\\\":\\\"Oregon\\\"}, \\n  {\\\"value\\\":\\\"PA\\\",\\\"name\\\":\\\"Pennsylvania\\\"}, \\n  {\\\"value\\\":\\\"PR\\\",\\\"name\\\":\\\"Puerto Rico\\\"}, \\n  {\\\"value\\\":\\\"RI\\\",\\\"name\\\":\\\"Rhode Island\\\"}, \\n  {\\\"value\\\":\\\"SC\\\",\\\"name\\\":\\\"South Carolina\\\"}, \\n  {\\\"value\\\":\\\"SD\\\",\\\"name\\\":\\\"South Dakota\\\"}, \\n  {\\\"value\\\":\\\"TN\\\",\\\"name\\\":\\\"Tennessee\\\"}, \\n  {\\\"value\\\":\\\"TX\\\",\\\"name\\\":\\\"Texas\\\"}, \\n  {\\\"value\\\":\\\"UT\\\",\\\"name\\\":\\\"Utah\\\"}, \\n  {\\\"value\\\":\\\"VA\\\",\\\"name\\\":\\\"Virginia\\\"}, \\n  {\\\"value\\\":\\\"VI\\\",\\\"name\\\":\\\"Virgin Islands\\\"}, \\n  {\\\"value\\\":\\\"VT\\\",\\\"name\\\":\\\"Vermont\\\"}, \\n  {\\\"value\\\":\\\"WA\\\",\\\"name\\\":\\\"Washington\\\"}, \\n  {\\\"value\\\":\\\"WI\\\",\\\"name\\\":\\\"Wisconsin\\\"}, \\n  {\\\"value\\\":\\\"WV\\\",\\\"name\\\":\\\"West Virginia\\\"}, \\n  {\\\"value\\\":\\\"WY\\\",\\\"name\\\":\\\"Wyoming\\\"}\\n]\",\"required\":true,\"width\":\"col-md-4\"},{\"id\":\"zipcode\",\"label\":\"Zip Code\",\"name\":\"zipcode\",\"type\":\"text\",\"placeholder\":\"\",\"default\":\"\",\"options\":\"\",\"required\":true,\"width\":\"col-md-4\"},{\"id\":\"description\",\"type\":\"textarea\",\"label\":\"Description\",\"required\":false,\"name\":\"description\"},{\"id\":\"start_date\",\"type\":\"datetime\",\"label\":\"Start Date & Time\",\"required\":true,\"name\":\"start_date\",\"width\":\"col-md-6\"},{\"id\":\"end_date\",\"type\":\"datetime\",\"label\":\"End Date & Time\",\"required\":true,\"name\":\"end_date\",\"width\":\"col-md-6\"},{\"id\":\"registration_start\",\"type\":\"datetime\",\"label\":\"Registration Start\",\"required\":true,\"name\":\"registration_start\",\"width\":\"col-md-6\"},{\"id\":\"registration_end\",\"type\":\"datetime\",\"label\":\"Registration End\",\"required\":true,\"name\":\"registration_end\",\"width\":\"col-md-6\"},{\"id\":\"Judge_time\",\"label\":\"Judging Start Time\",\"name\":\"Judge_time\",\"type\":\"time\",\"placeholder\":\"\",\"default\":\"\",\"options\":\"\",\"required\":false,\"width\":\"col-md-6\"},{\"id\":\"awards_time\",\"label\":\"Awards Start Time\",\"name\":\"awards_time\",\"type\":\"time\",\"placeholder\":\"\",\"default\":\"\",\"options\":\"\",\"required\":false,\"width\":\"col-md-6\"},{\"id\":\"coordinator_id\",\"type\":\"select\",\"label\":\"Coordinator\",\"required\":true,\"options\":\"coordinators\",\"name\":\"coordinator_id\"},{\"id\":\"status\",\"type\":\"select\",\"label\":\"Status\",\"required\":true,\"options\":{\"draft\":\"Draft\",\"published\":\"Published\",\"cancelled\":\"Cancelled\",\"completed\":\"Completed\"},\"name\":\"status\",\"width\":\"col-md-6\"},{\"id\":\"fan_voting_enabled\",\"type\":\"checkbox\",\"label\":\"Fan Favorite Voting\",\"required\":false,\"name\":\"fan_voting_enabled\",\"width\":\"col-md-12\"},{\"id\":\"registration_fee\",\"type\":\"number\",\"label\":\"Registration Fee ($)\",\"required\":false,\"step\":\"0.01\",\"min\":\"0\",\"help\":\"This field will be automatically set to 0 and disabled if \\\"This is a free show\\\" is checked.\",\"name\":\"registration_fee\"},{\"id\":\"is_free\",\"type\":\"checkbox\",\"label\":\"This is a free show (no registration fees)\",\"required\":false,\"help\":\"When checked, the Registration Fee field will be automatically set to 0 and disabled.\",\"name\":\"is_free\"},{\"id\":\"location\",\"type\":\"text\",\"label\":\"Venue\",\"required\":true,\"name\":\"location\",\"width\":\"col-md-12\",\"placeholder\":\"\",\"default\":\"\"},{\"id\":\"club\",\"label\":\"Club\",\"name\":\"club\",\"type\":\"text\",\"placeholder\":\"\",\"default\":\"\",\"options\":\"\",\"required\":false,\"width\":\"col-md-12\"},{\"id\":\"listing_fee\",\"type\":\"hidden\",\"label\":\"\",\"required\":false,\"step\":\"0.01\",\"min\":\"0\",\"name\":\"listing_fee\"}]', 1, '2025-05-25 17:04:01', '2025-07-19 19:07:09'),
(13, 'Default Vehicle Template', 'vehicle', 1, '[{\"id\":\"vehiclename\",\"type\":\"text\",\"label\":\"Vehicle Name\",\"order\":2,\"helpText\":\"Name or nickname of your vehicle\",\"required\":true,\"placeholder\":\"Enter vehicle name\",\"width\":\"col-md-6\"},{\"id\":\"plate\",\"type\":\"text\",\"label\":\"License Plate\",\"order\":1,\"helpText\":\"Vehicle license plate or registration number\",\"required\":true,\"placeholder\":\"Enter license plate\",\"width\":\"col-md-6\"},{\"id\":\"make\",\"label\":\"Make\",\"name\":\"make\",\"type\":\"select\",\"placeholder\":\"Enter vehicle make\",\"default\":\"\",\"options\":\"\",\"required\":true,\"width\":\"col-md-4\"},{\"id\":\"model\",\"label\":\"Model\",\"name\":\"model\",\"type\":\"select\",\"placeholder\":\"Enter vehicle model\",\"default\":\"\",\"options\":\"\",\"required\":true,\"width\":\"col-md-4\"},{\"id\":\"year\",\"label\":\"Year\",\"name\":\"year\",\"type\":\"select\",\"placeholder\":\"Enter year\",\"default\":\"\",\"options\":\"\",\"required\":true,\"width\":\"col-md-4\"},{\"id\":\"color\",\"type\":\"text\",\"label\":\"Color\",\"order\":6,\"helpText\":\"Primary color of the vehicle\",\"required\":false,\"placeholder\":\"Enter color\",\"width\":\"col-md-6\"},{\"id\":\"vin\",\"type\":\"text\",\"label\":\"VIN\",\"order\":7,\"helpText\":\"Vehicle Identification Number\",\"required\":false,\"placeholder\":\"Enter VIN\",\"width\":\"col-md-12\"}]', 1, '2025-05-27 11:31:47', '2025-06-10 13:10:47'),
(14, 'Default Show Registration Template', 'show', 2, '[{\"id\":\"vehicle_id\",\"type\":\"select\",\"label\":\"Select Vehicle\",\"required\":true,\"options\":\"vehicles\",\"width\":\"col-12\",\"placeholder\":\"Choose your vehicle\"},{\"id\":\"category_id\",\"type\":\"select\",\"label\":\"Vehicle Category\",\"required\":true,\"options\":\"categories\",\"width\":\"\",\"placeholder\":\"Select category\",\"default\":\"\"},{\"id\":\"special_features\",\"type\":\"textarea\",\"label\":\"Special Features\",\"required\":false,\"width\":\"col-12\",\"placeholder\":\"Describe any special features of your vehicle\"},{\"id\":\"vehicle_condition\",\"type\":\"select\",\"label\":\"Vehicle Condition\",\"required\":true,\"width\":\"col-12 col-md-6\",\"options\":[{\"value\":\"original\",\"label\":\"Original\"},{\"value\":\"restored\",\"label\":\"Restored\"},{\"value\":\"modified\",\"label\":\"Modified\"},{\"value\":\"custom\",\"label\":\"Custom\"}]},{\"id\":\"year_of_manufacture\",\"type\":\"select\",\"label\":\"Year of Manufacture\",\"required\":true,\"options\":\"Other\",\"width\":\"col-md-6\",\"placeholder\":\"Select Vehicle Year\"},{\"id\":\"payment_method_id\",\"type\":\"select\",\"label\":\"Payment Method\",\"required\":true,\"options\":\"payment_methods\",\"width\":\"col-12\",\"placeholder\":\"Select payment method\"},{\"id\":\"additional_notes\",\"type\":\"textarea\",\"label\":\"Additional Notes\",\"required\":false,\"width\":\"col-12\",\"placeholder\":\"Any additional information\"},{\"id\":\"agree_terms\",\"type\":\"checkbox\",\"label\":\"I agree to the show rules and terms\",\"required\":true,\"width\":\"col-12\"}]', 1, '2025-05-27 11:48:25', '2025-06-10 13:10:40');
