
-- --------------------------------------------------------

--
-- Table structure for table `notification_center_items`
--
-- Creation: Jul 12, 2025 at 04:48 PM
--

CREATE TABLE `notification_center_items` (
  `id` int(11) NOT NULL,
  `user_id` int(10) UNSIGNED NOT NULL,
  `notification_type` enum('message','push','toast','system','event','judging') NOT NULL,
  `source_table` varchar(50) DEFAULT NULL COMMENT 'Source table name (user_messages, user_push_notifications, etc)',
  `source_id` int(11) DEFAULT NULL COMMENT 'ID in source table',
  `title` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `action_url` varchar(500) DEFAULT NULL COMMENT 'URL to navigate to when clicked',
  `action_text` varchar(100) DEFAULT NULL COMMENT 'Text for action button',
  `metadata` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL COMMENT 'Additional data for the notification' CHECK (json_valid(`metadata`)),
  `is_read` tinyint(1) DEFAULT 0,
  `is_archived` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `read_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- RELATIONSHIPS FOR TABLE `notification_center_items`:
--   `user_id`
--       `users` -> `id`
--

--
-- Dumping data for table `notification_center_items`
--

INSERT INTO `notification_center_items` (`id`, `user_id`, `notification_type`, `source_table`, `source_id`, `title`, `message`, `action_url`, `action_text`, `metadata`, `is_read`, `is_archived`, `created_at`, `read_at`) VALUES
(543, 5, 'push', 'user_push_notifications', 3, 'Judge Request Approved - Summer Classic Auto Show 2025', 'The judge request for Brian Correll has been approved by an administrator for your show \'Summer Classic Auto Show 2025\'.', NULL, NULL, NULL, 0, 0, '2025-07-11 11:35:24', NULL),
(544, 5, 'push', 'user_push_notifications', 5, 'Judge Request Sent - Summer Classic Auto Show 2025', 'A judge request has been sent to Brian Correll for your show \'Summer Classic Auto Show 2025\'. They will receive a notification to accept or decline the request.', NULL, NULL, NULL, 0, 0, '2025-07-11 11:36:06', NULL),
(545, 5, 'push', 'user_push_notifications', 7, 'Judge Request Sent - Summer Classic Auto Show 2025', 'A judge request has been sent to Brian Correll for your show \'Summer Classic Auto Show 2025\'. They will receive a notification to accept or decline the request.', NULL, NULL, NULL, 0, 0, '2025-07-11 11:37:05', NULL),
(546, 5, 'push', 'user_push_notifications', 9, 'Judge Request Approved - Summer Classic Auto Show 2025', 'The judge request for Brian Correll has been approved by an administrator for your show \'Summer Classic Auto Show 2025\'.', NULL, NULL, NULL, 0, 0, '2025-07-11 11:37:15', NULL),
(550, 5, 'toast', 'user_toast_notifications', 13, 'Judge Request Approved - Summer Classic Auto Show 2025', 'The judge request for Brian Correll has been approved by an administrator for your show \'Summer Classic Auto Show 2025\'.', NULL, NULL, NULL, 0, 0, '2025-07-11 11:35:24', NULL),
(551, 5, 'toast', 'user_toast_notifications', 15, 'Judge Request Sent - Summer Classic Auto Show 2025', 'A judge request has been sent to Brian Correll for your show \'Summer Classic Auto Show 2025\'. They will receive a notification to accept or decline the request.', NULL, NULL, NULL, 0, 0, '2025-07-11 11:36:06', NULL),
(552, 5, 'toast', 'user_toast_notifications', 17, 'Judge Request Sent - Summer Classic Auto Show 2025', 'A judge request has been sent to Brian Correll for your show \'Summer Classic Auto Show 2025\'. They will receive a notification to accept or decline the request.', NULL, NULL, NULL, 0, 0, '2025-07-11 11:37:05', NULL),
(553, 5, 'toast', 'user_toast_notifications', 19, 'Judge Request Approved - Summer Classic Auto Show 2025', 'The judge request for Brian Correll has been approved by an administrator for your show \'Summer Classic Auto Show 2025\'.', NULL, NULL, NULL, 0, 0, '2025-07-11 11:37:15', NULL),
(559, 4, 'message', 'user_messages', 1, 'Test Message', 'This is a test message from the notification center system.', '/notification_center/view/1', 'Reply', '{\"show_id\":null,\"requires_reply\":true}', 0, 0, '2025-07-12 16:53:36', NULL),
(561, 4, 'message', 'user_messages', 2, 'Test Message', 'This is a test message from the notification center system.', '/notification_center/view/2', 'Reply', '{\"show_id\":null,\"requires_reply\":true}', 0, 0, '2025-07-12 16:59:06', NULL),
(563, 4, 'message', 'user_messages', 3, 'Test Message', 'This is a test message from the notification center system.', '/notification_center/viewNotification/3', 'Reply', '{\"show_id\":null,\"requires_reply\":true}', 0, 0, '2025-07-12 17:03:54', NULL),
(587, 3, 'message', 'messages', NULL, 'Test Judge Message - 2025-07-12 20:26:57', 'You received a message from Brian Correll:\n\nThis is a test message from Judge Management system.\n\nPlease confirm you received this message.\n\n[reply]', '/notification_center', 'Reply', '{\"from_user_id\":\"3\",\"from_user_name\":\"Brian Correll\",\"show_id\":1,\"allows_reply\":true,\"original_message\":\"This is a test message from Judge Management system.\\n\\nPlease confirm you received this message.\\n\\n[reply]\",\"original_subject\":\"Test Judge Message - 2025-07-12 20:26:57\"}', 0, 0, '2025-07-12 20:26:57', NULL);
