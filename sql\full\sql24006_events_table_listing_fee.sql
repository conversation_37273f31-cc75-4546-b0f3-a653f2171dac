
-- --------------------------------------------------------

--
-- Table structure for table `listing_fee`
--
-- Creation: Jun 23, 2025 at 08:30 PM
--

CREATE TABLE `listing_fee` (
  `id` int(10) UNSIGNED NOT NULL,
  `amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `fee_type` enum('monthly','per_show') NOT NULL DEFAULT 'per_show',
  `code` varchar(50) DEFAULT NULL,
  `code_used` tinyint(1) NOT NULL DEFAULT 0,
  `expiry_date` date DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- RELATIONSHIPS FOR TABLE `listing_fee`:
--
