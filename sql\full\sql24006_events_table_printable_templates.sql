
-- --------------------------------------------------------

--
-- Table structure for table `printable_templates`
--
-- Creation: Jun 23, 2025 at 08:30 PM
--

CREATE TABLE `printable_templates` (
  `id` int(10) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `template_data` longtext NOT NULL,
  `template_html` longtext DEFAULT NULL,
  `template_css` text DEFAULT NULL,
  `is_default` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- R<PERSON><PERSON>IONSHIPS FOR TABLE `printable_templates`:
--

--
-- Dumping data for table `printable_templates`
--

INSERT INTO `printable_templates` (`id`, `name`, `description`, `template_data`, `template_html`, `template_css`, `is_default`, `created_at`, `updated_at`) VALUES
(19, 'Modern Dashboard with QR', 'A clean, modern dashboard layout with large QR code and comprehensive vehicle information', '{&amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;quot;width&amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;quot;:800,&amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;quot;height&amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;quot;:700,&amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;quot;elements&amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;quot;:[]}', '<div class=\"template-container\">\r\n  <div id=\"element-0\" class=\"template-element template-line\" style=\"position: absolute; left: 50px; top: 90px; width: 700px; height: 2px; background-color: rgb(52, 152, 219); \"></div>\r\n  <div id=\"element-1\" class=\"template-element template-field selected\" data-field=\"registration_number\" data-text-align=\"center\" style=\"position: absolute; left: 428px; top: 104px; width: 331px; height: 48px; font-size: 13px; font-family: Arial, sans-serif; font-weight: bold; text-align: center; \">Registration #: {{registration_number}}</div>\r\n  <div id=\"element-2\" class=\"template-element template-field\" data-field=\"make\" style=\"position: absolute; left: 50px; top: 160px; width: 137px; height: 42px; font-size: 20px; font-family: Arial, sans-serif; font-weight: normal; text-align: left; \">Make: {{vehicle_make}}</div>\r\n  <div id=\"element-3\" class=\"template-element template-field\" data-field=\"model\" style=\"position: absolute; left: 50px; top: 203px; width: 150px; height: 42px; font-size: 20px; font-family: Arial, sans-serif; font-weight: normal; text-align: left; \">Model: {{vehicle_model}}</div>\r\n  <div id=\"element-4\" class=\"template-element template-field\" data-field=\"year\" style=\"position: absolute; left: 50px; top: 246px; width: 117px; height: 42px; font-size: 20px; font-family: Arial, sans-serif; font-weight: normal; text-align: left; \">Year: {{vehicle_year}}</div>\r\n  <div id=\"element-5\" class=\"template-element template-field\" data-field=\"owner_name\" style=\"position: absolute; left: 50px; top: 289px; width: 210px; height: 42px; font-size: 20px; font-family: Arial, sans-serif; font-weight: normal; text-align: left; \">Owner: {{owner_name}}</div>\r\n  <div id=\"element-6\" class=\"template-element template-field\" data-field=\"color\" style=\"position: absolute; left: 50px; top: 332px; width: 131px; height: 42px; font-size: 20px; font-family: Arial, sans-serif; font-weight: normal; text-align: left; \">Color: {{vehicle_color}}</div>\r\n  <div id=\"element-7\" class=\"template-element template-field\" data-field=\"class\" data-text-align=\"left\" style=\"position: absolute; left: 50px; top: 375px; width: 401px; height: 42px; font-size: 20px; font-family: Arial, sans-serif; font-weight: normal; text-align: left; \">Location: {{show_location}}</div>\r\n  <div id=\"element-8\" class=\"template-element template-field\" data-field=\"category\" style=\"position: absolute; left: 50px; top: 418px; width: 403px; height: 42px; font-size: 20.6235px; font-family: Arial, sans-serif; font-weight: normal; text-align: left; \">Category: {{category_name}}</div>\r\n  <div id=\"element-9\" class=\"template-element template-field\" data-field=\"license_plate\" style=\"position: absolute; left: 50px; top: 461px; width: 344px; height: 42px; font-size: 20px; font-family: Arial, sans-serif; font-weight: normal; text-align: left; \">License Plate: {{license_plate}}</div>\r\n  <div id=\"element-10\" class=\"template-element template-text text-align-center\" data-text-align=\"center\" style=\"position: absolute; left: 405px; top: 476px; width: 377px; height: 53px; font-size: 33.93px; font-family: Arial, sans-serif; font-weight: bold; text-align: center; \">SCAN TO VOTE</div>\r\n  <div id=\"element-11\" class=\"template-element template-line\" style=\"position: absolute; left: 51px; top: 547px; width: 700px; height: 2px; background-color: rgb(52, 152, 219); \"></div>\r\n  <div id=\"element-12\" class=\"template-element template-text text-align-center\" data-text-align=\"center\" style=\"position: absolute; left: 48px; top: 558px; font-size: 14px; text-align: center; \">Thank you for viewing this vehicle. Please do not touch.</div>\r\n  <div id=\"element-13\" class=\"template-element template-qrcode\" data-qrcode-element=\"true\" data-placeholder-type=\"qrcode\" style=\"position: absolute; left: 461.5px; top: 182px; width: 264px; height: 264px; background-color: rgb(255, 255, 204); border: 3px dashed rgb(255, 0, 0); \"><div class=\"qrcode-placeholder\"></div><div class=\"qrcode-identifier\">{{QR_CODE}}</div></div>\r\n  <div id=\"element-14\" class=\"template-element template-field\" data-field=\"registration_number\" style=\"position: absolute; left: 61px; top: 40px; width: 449px; height: 47px; font-size: 24.3px; font-family: Arial, sans-serif; font-weight: bold; text-align: left; \"> {{show_name}}</div>\r\n  <div id=\"element-15\" class=\"template-element template-field\" data-field=\"registration_number\" style=\"position: absolute; left: 518px; top: 47px; width: 236px; height: 40px; font-size: 17px; font-family: Arial, sans-serif; font-weight: bold; text-align: right; \"> {{show_date}}</div>\r\n  <div id=\"element-16\" class=\"template-element template-display-number\" data-display-number=\"true\" data-badge-color=\"#000000\" data-text-color=\"#ffffff\" style=\"position: absolute; left: 248px; top: 103px; width: 112px; height: 51px; font-size: 24px; font-weight: bold; color: rgb(255, 255, 255); background-color: rgb(0, 0, 0); \"><div class=\"display-number-badge\">{{DN}}</div></div>\r\n  <div id=\"element-17\" class=\"template-element template-text text-align-left\" data-text-align=\"center\" style=\"position: absolute; left: 50px; top: 113.5px; width: 165px; height: 30px; font-size: 18px; font-family: Arial, sans-serif; font-weight: bold; text-align: center; \">Display Number:</div>\r\n</div>', '.template-container {\r\n  position: relative;\r\n  width: 800px;\r\n  height: 1100px;\r\n  background-color: white;\r\n  margin: 0 auto;\r\n}\r\n.template-element {\r\n  position: absolute;\r\n  box-sizing: border-box;\r\n}\r\n.template-text, .template-field {\r\n  min-width: 50px;\r\n  min-height: 20px;\r\n  position: absolute !important; /* Ensure position is always absolute */\r\n}\r\n\r\n/* Make resize handles more visible */\r\n.ui-resizable-handle {\r\n  background-color: #007bff;\r\n  border-radius: 50%;\r\n  width: 8px !important;\r\n  height: 8px !important;\r\n  visibility: visible !important;\r\n}\r\n\r\n/* Ensure field elements show their resize handles */\r\n.template-field .ui-resizable-handle {\r\n  z-index: 90 !important;\r\n  opacity: 1 !important;\r\n  visibility: visible !important;\r\n}\r\n.template-qrcode {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border: 1px dashed #ccc;\r\n}\r\n.template-image img {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: contain;\r\n}\r\n.template-display-number {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  background-color: #000000;\r\n  color: #ffffff;\r\n  font-weight: bold;\r\n  border-radius: 10px;\r\n  padding: 10px;\r\n  box-sizing: border-box;\r\n}\r\n.template-rectangle {\r\n  border: 1px solid black;\r\n}\r\n.template-line {\r\n  background-color: black;\r\n}\r\n@media print {\r\n  .template-container {\r\n    margin: 0;\r\n    box-shadow: none;\r\n  }\r\n}', 1, '2025-05-31 00:24:09', '2025-06-04 00:22:13'),
(20, 'Classic Car Show with Logo', 'Traditional car show display with space for club logo and comprehensive details', '{&quot;width&quot;:800,&quot;height&quot;:750,&quot;elements&quot;:[]}', '<div class=\"template-container\">\r\n  <div id=\"element-0\" class=\"template-element template-text\" style=\"position: absolute; left: 400px; top: 30px; font-size: 40px; font-weight: bold; text-align: center; color: #8b0000;\">CAR SHOW ENTRY</div>\r\n  <div id=\"element-1\" class=\"template-element template-line\" style=\"position: absolute; left: 100px; top: 90px; width: 600px; height: 3px; background-color: #8b0000;\"></div>\r\n  <div id=\"element-2\" class=\"template-element template-field\" data-field=\"registration_number\" style=\"position: absolute; left: 100px; top: 110px; font-size: 28px; font-weight: bold;\">Entry #: <span class=\"field-value\">{{registration_number}}</span></div>\r\n  <div id=\"element-3\" class=\"template-element template-image\" style=\"position: absolute; left: 100px; top: 160px; width: 150px; height: 150px; border: 2px dashed #8b0000;\"><img src=\"club_logo\" alt=\"Club Logo\"></div>\r\n  <div id=\"element-4\" class=\"template-element template-text\" style=\"position: absolute; left: 100px; top: 320px; font-size: 14px; text-align: center; width: 150px;\">Club Logo</div>\r\n  <div id=\"element-5\" class=\"template-element template-field\" data-field=\"make\" style=\"position: absolute; left: 280px; top: 160px; font-size: 22px; font-weight: bold;\">Make: <span class=\"field-value\">{{make}}</span></div>\r\n  <div id=\"element-6\" class=\"template-element template-field\" data-field=\"model\" style=\"position: absolute; left: 280px; top: 200px; font-size: 22px; font-weight: bold;\">Model: <span class=\"field-value\">{{model}}</span></div>\r\n  <div id=\"element-7\" class=\"template-element template-field\" data-field=\"year\" style=\"position: absolute; left: 280px; top: 240px; font-size: 22px; font-weight: bold;\">Year: <span class=\"field-value\">{{year}}</span></div>\r\n  <div id=\"element-8\" class=\"template-element template-field\" data-field=\"owner_name\" style=\"position: absolute; left: 280px; top: 280px; font-size: 22px;\">Owner: <span class=\"field-value\">{{owner_name}}</span></div>\r\n  <div id=\"element-9\" class=\"template-element template-field\" data-field=\"color\" style=\"position: absolute; left: 280px; top: 320px; font-size: 18px;\">Color: <span class=\"field-value\">{{color}}</span></div>\r\n  <div id=\"element-10\" class=\"template-element template-field\" data-field=\"class\" style=\"position: absolute; left: 100px; top: 380px; font-size: 18px;\">Class: <span class=\"field-value\">{{class}}</span></div>\r\n  <div id=\"element-11\" class=\"template-element template-field\" data-field=\"category\" style=\"position: absolute; left: 100px; top: 420px; font-size: 18px;\">Category: <span class=\"field-value\">{{category}}</span></div>\r\n  <div id=\"element-12\" class=\"template-element template-field\" data-field=\"license_plate\" style=\"position: absolute; left: 100px; top: 460px; font-size: 18px;\">License: <span class=\"field-value\">{{license_plate}}</span></div>\r\n  <div id=\"element-13\" class=\"template-element template-field\" data-field=\"engine\" style=\"position: absolute; left: 100px; top: 500px; font-size: 18px;\">Engine: <span class=\"field-value\">{{engine}}</span></div>\r\n  <div id=\"element-14\" class=\"template-element template-field\" data-field=\"modifications\" style=\"position: absolute; left: 100px; top: 540px; font-size: 18px;\">Mods: <span class=\"field-value\">{{modifications}}</span></div>\r\n  <div id=\"element-15\" class=\"template-element template-field\" data-field=\"club\" style=\"position: absolute; left: 100px; top: 580px; font-size: 18px;\">Club: <span class=\"field-value\">{{club}}</span></div>\r\n  <div id=\"element-16\" class=\"template-element template-qrcode\" style=\"position: absolute; left: 450px; top: 380px; width: 250px; height: 250px;\"></div>\r\n  <div id=\"element-17\" class=\"template-element template-text\" style=\"position: absolute; left: 450px; top: 640px; font-size: 16px; text-align: center; width: 250px;\">Scan QR Code for Details</div>\r\n  <div id=\"element-18\" class=\"template-element template-line\" style=\"position: absolute; left: 100px; top: 680px; width: 600px; height: 3px; background-color: #8b0000;\"></div>\r\n  <div id=\"element-19\" class=\"template-element template-text\" style=\"position: absolute; left: 100px; top: 700px; font-size: 16px; font-style: italic; color: #8b0000;\">Please respect this vehicle - No touching</div>\r\n</div>', '.template-container {\r\n  font-family: &quot;Times New Roman&quot;, serif;\r\n  color: #333;\r\n  background-color: #f9f7f0;\r\n  padding: 20px;\r\n  border: 5px double #8b0000;\r\n}\r\n\r\n.template-line {\r\n  background-color: #8b0000;\r\n}\r\n\r\n.field-value {\r\n  font-weight: normal;\r\n  color: #8b0000;\r\n}\r\n\r\n.template-qrcode {\r\n  border: 3px solid #8b0000;\r\n  background-color: white;\r\n}', 0, '2025-05-31 00:24:10', '2025-05-31 21:33:33'),
(21, 'Minimalist Modern Display', 'Clean, minimalist design with large QR code and essential information', '{&amp;amp;amp;amp;amp;quot;width&amp;amp;amp;amp;amp;quot;:800,&amp;amp;amp;amp;amp;quot;height&amp;amp;amp;amp;amp;quot;:650,&amp;amp;amp;amp;amp;quot;elements&amp;amp;amp;amp;amp;quot;:[]}', '<div class=\"template-container\">\r\n  <div id=\"element-0\" class=\"template-element template-text\" style=\"position: absolute; left: 400px; top: 40px; width: 342px; height: 51px; font-size: 32px; font-family: Arial, sans-serif; font-weight: normal; color: rgb(51, 51, 51); text-align: center; \">SCAN TO VOTE</div>\r\n  <div id=\"element-1\" class=\"template-element template-field selected\" data-field=\"year\" style=\"position: absolute; left: 52px; top: 27px; width: 154px; height: 77px; font-size: 42px; font-family: Arial, sans-serif; font-weight: normal; text-align: left; border: 1px dashed rgb(153, 153, 153); \"> {{vehicle_year}}</div>\r\n  <div id=\"element-2\" class=\"template-element template-field\" data-field=\"make\" style=\"position: absolute; left: 51px; top: 103px; width: 169px; height: 77px; font-size: 42px; font-family: Arial, sans-serif; font-weight: normal; text-align: left; border: 1px dashed rgb(153, 153, 153); \"> {{vehicle_make}}</div>\r\n  <div id=\"element-3\" class=\"template-element template-field\" data-field=\"model\" style=\"position: absolute; left: 50px; top: 180px; width: 156px; height: 68px; font-size: 36px; font-family: Arial, sans-serif; font-weight: normal; text-align: left; border: 1px dashed rgb(153, 153, 153); \"> {{vehicle_model}}</div>\r\n  <div id=\"element-4\" class=\"template-element template-line\" style=\"position: absolute; left: 50px; top: 240px; width: 300px; height: 1px; background-color: rgb(221, 221, 221); \"></div>\r\n  <div id=\"element-5\" class=\"template-element template-field\" data-field=\"registration_number\" style=\"position: absolute; left: 50px; top: 260px; width: 290px; height: 39px; font-size: 18px; font-family: Arial, sans-serif; font-weight: normal; text-align: left; border: 1px dashed rgb(153, 153, 153); \">Registration: {{registration_number}}</div>\r\n  <div id=\"element-6\" class=\"template-element template-field\" data-field=\"owner_name\" style=\"position: absolute; left: 50px; top: 300px; width: 190px; height: 39px; font-size: 18px; font-family: Arial, sans-serif; font-weight: normal; text-align: left; border: 1px dashed rgb(153, 153, 153); \">Owner: {{owner_name}}</div>\r\n  <div id=\"element-7\" class=\"template-element template-field\" data-field=\"color\" style=\"position: absolute; left: 50px; top: 340px; width: 119px; height: 39px; font-size: 18px; font-family: Arial, sans-serif; font-weight: normal; text-align: left; border: 1px dashed rgb(153, 153, 153); \">Color: {{registration_number}}</div>\r\n  <div id=\"element-8\" class=\"template-element template-field\" data-field=\"class\" style=\"position: absolute; left: 50px; top: 380px; width: 122px; height: 39px; font-size: 18px; font-family: Arial, sans-serif; font-weight: normal; text-align: left; border: 1px dashed rgb(153, 153, 153); \">Plate: {{license_plate}}</div>\r\n  <div id=\"element-9\" class=\"template-element template-field\" data-field=\"category\" style=\"position: absolute; left: 50px; top: 420px; width: 178px; height: 39px; font-size: 18px; font-family: Arial, sans-serif; font-weight: normal; text-align: left; border: 1px dashed rgb(153, 153, 153); \">Category: {{category_name}}</div>\r\n  <div id=\"element-10\" class=\"template-element template-line\" style=\"position: absolute; left: 50px; top: 460px; width: 300px; height: 1px; background-color: rgb(221, 221, 221); \"></div>\r\n  <div id=\"element-11\" class=\"template-element template-field\" data-field=\"club\" style=\"position: absolute; left: 50px; top: 600px; font-size: 16px; border: 1px dashed rgb(153, 153, 153); \">Club: <span class=\"field-value\">{{club}}</span></div>\r\n  <div id=\"element-12\" class=\"template-element template-qrcode\" style=\"position: absolute; left: 400px; top: 120px; width: 350px; height: 350px; \"></div>\r\n  <div id=\"element-13\" class=\"template-element template-text\" style=\"position: absolute; left: 400px; top: 480px; width: 350px; font-size: 18px; text-align: center; \">Scan for complete vehicle details</div>\r\n  <div id=\"element-14\" class=\"template-element template-text\" style=\"position: absolute; left: 400px; top: 520px; width: 350px; font-size: 14px; color: rgb(153, 153, 153); text-align: center; \">Please respect this display. No touching.</div>\r\n</div>', '.template-container {\r\n  position: relative;\r\n  width: 800px;\r\n  height: 1100px;\r\n  background-color: white;\r\n  margin: 0 auto;\r\n}\r\n.template-element {\r\n  position: absolute;\r\n  box-sizing: ;\r\n}\r\n.template-text, .template-field {\r\n  min-width: 50px;\r\n  min-height: 20px;\r\n  position: absolute !important; /* Ensure position is always absolute */\r\n}\r\n\r\n/* Make resize handles more visible */\r\n.ui-resizable-handle {\r\n  background-color: #007bff;\r\n  border-radius: 50%;\r\n  width: 8px !important;\r\n  height: 8px !important;\r\n  visibility: visible !important;\r\n}\r\n\r\n/* Ensure field elements show their resize handles */\r\n.template-field .ui-resizable-handle {\r\n  z-index: 90 !important;\r\n  opacity: 1 !important;\r\n  visibility: visible !important;\r\n}\r\n.template-qrcode {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border: 1px dashed #ccc;\r\n}\r\n.template-image img {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: contain;\r\n}\r\n.template-rectangle {\r\n  border: 1px solid black;\r\n}\r\n.template-line {\r\n  background-color: black;\r\n}\r\n@media print {\r\n  .template-container {\r\n    margin: 0;\r\n    box-shadow: none;\r\n  }\r\n}', 0, '2025-05-31 00:24:10', '2025-05-31 21:32:47'),
(22, 'Vintage-Inspired Display', 'Retro design with aged paper look and comprehensive vehicle information', '{\"width\":800,\"height\":700,\"elements\":[]}', '<div class=\"template-container\">\r\n  <div id=\"element-0\" class=\"template-element template-text\" style=\"position: absolute; left: 400px; top: 40px; font-size: 38px; font-weight: bold; text-align: center; color: #5c3a21;\">CLASSIC AUTOMOBILE</div>\r\n  <div id=\"element-1\" class=\"template-element template-text\" style=\"position: absolute; left: 400px; top: 90px; font-size: 24px; text-align: center; color: #5c3a21;\">VEHICLE REGISTRATION CARD</div>\r\n  <div id=\"element-2\" class=\"template-element template-line\" style=\"position: absolute; left: 100px; top: 130px; width: 600px; height: 2px; background-color: #5c3a21;\"></div>\r\n  <div id=\"element-3\" class=\"template-element template-field\" data-field=\"registration_number\" style=\"position: absolute; left: 100px; top: 150px; font-size: 24px; font-weight: bold; color: #5c3a21;\">No. <span class=\"field-value\">{{registration_number}}</span></div>\r\n  <div id=\"element-4\" class=\"template-element template-field\" data-field=\"year\" style=\"position: absolute; left: 100px; top: 200px; font-size: 22px; font-weight: bold;\">Year: <span class=\"field-value\">{{year}}</span></div>\r\n  <div id=\"element-5\" class=\"template-element template-field\" data-field=\"make\" style=\"position: absolute; left: 100px; top: 240px; font-size: 22px;\">Make: <span class=\"field-value\">{{make}}</span></div>\r\n  <div id=\"element-6\" class=\"template-element template-field\" data-field=\"model\" style=\"position: absolute; left: 100px; top: 280px; font-size: 22px;\">Model: <span class=\"field-value\">{{model}}</span></div>\r\n  <div id=\"element-7\" class=\"template-element template-field\" data-field=\"owner_name\" style=\"position: absolute; left: 100px; top: 320px; font-size: 22px;\">Owner: <span class=\"field-value\">{{owner_name}}</span></div>\r\n  <div id=\"element-8\" class=\"template-element template-field\" data-field=\"color\" style=\"position: absolute; left: 100px; top: 360px; font-size: 18px;\">Body Color: <span class=\"field-value\">{{color}}</span></div>\r\n  <div id=\"element-9\" class=\"template-element template-field\" data-field=\"class\" style=\"position: absolute; left: 100px; top: 400px; font-size: 18px;\">Classification: <span class=\"field-value\">{{class}}</span></div>\r\n  <div id=\"element-10\" class=\"template-element template-field\" data-field=\"category\" style=\"position: absolute; left: 100px; top: 440px; font-size: 18px;\">Category: <span class=\"field-value\">{{category}}</span></div>\r\n  <div id=\"element-11\" class=\"template-element template-field\" data-field=\"license_plate\" style=\"position: absolute; left: 100px; top: 480px; font-size: 18px;\">License No.: <span class=\"field-value\">{{license_plate}}</span></div>\r\n  <div id=\"element-12\" class=\"template-element template-field\" data-field=\"engine\" style=\"position: absolute; left: 100px; top: 520px; font-size: 18px;\">Engine Specifications: <span class=\"field-value\">{{engine}}</span></div>\r\n  <div id=\"element-13\" class=\"template-element template-field\" data-field=\"modifications\" style=\"position: absolute; left: 100px; top: 560px; font-size: 18px;\">Modifications: <span class=\"field-value\">{{modifications}}</span></div>\r\n  <div id=\"element-14\" class=\"template-element template-field\" data-field=\"club\" style=\"position: absolute; left: 100px; top: 600px; font-size: 18px;\">Automobile Club: <span class=\"field-value\">{{club}}</span></div>\r\n  <div id=\"element-15\" class=\"template-element template-qrcode\" style=\"position: absolute; left: 450px; top: 200px; width: 250px; height: 250px;\"></div>\r\n  <div id=\"element-16\" class=\"template-element template-text\" style=\"position: absolute; left: 450px; top: 460px; font-size: 16px; text-align: center; width: 250px; color: #5c3a21;\">Scan for detailed information</div>\r\n  <div id=\"element-17\" class=\"template-element template-line\" style=\"position: absolute; left: 100px; top: 640px; width: 600px; height: 2px; background-color: #5c3a21;\"></div>\r\n  <div id=\"element-18\" class=\"template-element template-text\" style=\"position: absolute; left: 400px; top: 660px; font-size: 16px; font-style: italic; text-align: center; color: #5c3a21;\">Please do not touch this historic automobile</div>\r\n</div>', '.template-container {\r\n  font-family: \"Georgia\", serif;\r\n  color: #5c3a21;\r\n  background-color: #f5e8c9;\r\n  padding: 20px;\r\n  border: 8px double #5c3a21;\r\n  background-image: url(\"data:image/svg+xml,%3Csvg width=\'100\' height=\'100\' viewBox=\'0 0 100 100\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cpath d=\'M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z\' fill=\'%235c3a21\' fill-opacity=\'0.05\' fill-rule=\'evenodd\'/%3E%3C/svg%3E\");\r\n}\r\n\r\n.field-value {\r\n  color: #8b5a2b;\r\n  font-weight: normal;\r\n}\r\n\r\n.template-qrcode {\r\n  border: 3px solid #5c3a21;\r\n  background-color: white;\r\n  box-shadow: 5px 5px 15px rgba(0,0,0,0.2);\r\n}', 0, '2025-05-31 00:24:10', '2025-05-31 00:24:10'),
(23, 'High-Tech Digital Display', 'Modern tech-inspired design with large QR code and comprehensive information', '{\"width\":800,\"height\":750,\"elements\":[]}', '<div class=\"template-container\">\r\n  <div id=\"element-0\" class=\"template-element template-text\" style=\"position: absolute; left: 400px; top: 30px; font-size: 36px; font-weight: bold; text-align: center; color: #00b4d8;\">VEHICLE PROFILE</div>\r\n  <div id=\"element-1\" class=\"template-element template-line\" style=\"position: absolute; left: 50px; top: 90px; width: 700px; height: 2px; background-color: #00b4d8;\"></div>\r\n  <div id=\"element-2\" class=\"template-element template-field\" data-field=\"registration_number\" style=\"position: absolute; left: 50px; top: 110px; font-size: 24px; font-weight: bold; color: #00b4d8;\">ID: <span class=\"field-value\">{{registration_number}}</span></div>\r\n  <div id=\"element-3\" class=\"template-element template-qrcode\" style=\"position: absolute; left: 450px; top: 150px; width: 300px; height: 300px;\"></div>\r\n  <div id=\"element-4\" class=\"template-element template-text\" style=\"position: absolute; left: 450px; top: 460px; font-size: 16px; text-align: center; width: 300px; color: #00b4d8;\">SCAN FOR DIGITAL DETAILS</div>\r\n  <div id=\"element-5\" class=\"template-element template-field\" data-field=\"year\" style=\"position: absolute; left: 50px; top: 160px; font-size: 20px; background-color: rgba(0,180,216,0.1); padding: 5px 10px; border-radius: 5px;\">Year: <span class=\"field-value\">{{year}}</span></div>\r\n  <div id=\"element-6\" class=\"template-element template-field\" data-field=\"make\" style=\"position: absolute; left: 50px; top: 210px; font-size: 20px; background-color: rgba(0,180,216,0.1); padding: 5px 10px; border-radius: 5px;\">Make: <span class=\"field-value\">{{make}}</span></div>\r\n  <div id=\"element-7\" class=\"template-element template-field\" data-field=\"model\" style=\"position: absolute; left: 50px; top: 260px; font-size: 20px; background-color: rgba(0,180,216,0.1); padding: 5px 10px; border-radius: 5px;\">Model: <span class=\"field-value\">{{model}}</span></div>\r\n  <div id=\"element-8\" class=\"template-element template-field\" data-field=\"owner_name\" style=\"position: absolute; left: 50px; top: 310px; font-size: 20px; background-color: rgba(0,180,216,0.1); padding: 5px 10px; border-radius: 5px;\">Owner: <span class=\"field-value\">{{owner_name}}</span></div>\r\n  <div id=\"element-9\" class=\"template-element template-field\" data-field=\"color\" style=\"position: absolute; left: 50px; top: 360px; font-size: 20px; background-color: rgba(0,180,216,0.1); padding: 5px 10px; border-radius: 5px;\">Color: <span class=\"field-value\">{{color}}</span></div>\r\n  <div id=\"element-10\" class=\"template-element template-field\" data-field=\"class\" style=\"position: absolute; left: 50px; top: 410px; font-size: 20px; background-color: rgba(0,180,216,0.1); padding: 5px 10px; border-radius: 5px;\">Class: <span class=\"field-value\">{{class}}</span></div>\r\n  <div id=\"element-11\" class=\"template-element template-field\" data-field=\"category\" style=\"position: absolute; left: 50px; top: 460px; font-size: 20px; background-color: rgba(0,180,216,0.1); padding: 5px 10px; border-radius: 5px;\">Category: <span class=\"field-value\">{{category}}</span></div>\r\n  <div id=\"element-12\" class=\"template-element template-line\" style=\"position: absolute; left: 50px; top: 510px; width: 700px; height: 1px; background-color: #00b4d8;\"></div>\r\n  <div id=\"element-13\" class=\"template-element template-field\" data-field=\"engine\" style=\"position: absolute; left: 50px; top: 530px; font-size: 18px;\">Engine: <span class=\"field-value\">{{engine}}</span></div>\r\n  <div id=\"element-14\" class=\"template-element template-field\" data-field=\"modifications\" style=\"position: absolute; left: 50px; top: 570px; font-size: 18px;\">Modifications: <span class=\"field-value\">{{modifications}}</span></div>\r\n  <div id=\"element-15\" class=\"template-element template-field\" data-field=\"license_plate\" style=\"position: absolute; left: 50px; top: 610px; font-size: 18px;\">License: <span class=\"field-value\">{{license_plate}}</span></div>\r\n  <div id=\"element-16\" class=\"template-element template-field\" data-field=\"club\" style=\"position: absolute; left: 50px; top: 650px; font-size: 18px;\">Club: <span class=\"field-value\">{{club}}</span></div>\r\n  <div id=\"element-17\" class=\"template-element template-line\" style=\"position: absolute; left: 50px; top: 690px; width: 700px; height: 1px; background-color: #00b4d8;\"></div>\r\n  <div id=\"element-18\" class=\"template-element template-text\" style=\"position: absolute; left: 400px; top: 710px; font-size: 16px; text-align: center; color: #00b4d8;\">PLEASE DO NOT TOUCH THE DISPLAY</div>\r\n</div>', '.template-container {\r\n  font-family: \"Roboto\", sans-serif;\r\n  color: #333;\r\n  background-color: #f8f9fa;\r\n  background-image: linear-gradient(135deg, #f8f9fa 25%, #e9ecef 25%, #e9ecef 50%, #f8f9fa 50%, #f8f9fa 75%, #e9ecef 75%, #e9ecef 100%);\r\n  background-size: 20px 20px;\r\n  padding: 20px;\r\n  border: 1px solid #00b4d8;\r\n  border-radius: 10px;\r\n  box-shadow: 0 0 20px rgba(0,180,216,0.2);\r\n}\r\n\r\n.field-value {\r\n  color: #0077b6;\r\n  font-weight: 500;\r\n}\r\n\r\n.template-qrcode {\r\n  border: none;\r\n  background-color: white;\r\n  border-radius: 10px;\r\n  box-shadow: 0 5px 15px rgba(0,0,0,0.1);\r\n}', 0, '2025-05-31 00:24:10', '2025-05-31 21:33:58'),
(24, 'test', 'image', '{&amp;amp;amp;amp;amp;amp;quot;width&amp;amp;amp;amp;amp;amp;quot;:800,&amp;amp;amp;amp;amp;amp;quot;height&amp;amp;amp;amp;amp;amp;quot;:1100,&amp;amp;amp;amp;amp;amp;quot;elements&amp;amp;amp;amp;amp;amp;quot;:[]}', '<div class=\"template-container\">\r\n  <div id=\"element-0\" class=\"template-element template-text\" style=\"position: absolute; left: 100px; top: 50px; width: 173px; height: 43px; font-size: 26.8483px; font-family: Arial, sans-serif; font-weight: normal; text-align: center; \">Text Element</div>\r\n  <div id=\"element-1\" class=\"template-element template-field selected\" data-field=\"registration_number\" style=\"position: absolute; left: 33px; top: 168px; width: 246px; height: 48px; font-size: 37px; font-family: Arial, sans-serif; font-weight: bold; text-align: left; border: 1px dashed rgb(153, 153, 153); \">Field: {{registration_number}}</div>\r\n</div>', '.template-container {\r\n  position: relative;\r\n  width: 800px;\r\n  height: 1100px;\r\n  background-color: white;\r\n  margin: 0 auto;\r\n}\r\n.template-element {\r\n  position: absolute;\r\n  box-sizing: border-box;\r\n}\r\n.template-text, .template-field {\r\n  min-width: 50px;\r\n  min-height: 20px;\r\n  position: absolute !important; /* Ensure position is always absolute */\r\n}\r\n\r\n/* Make resize handles more visible */\r\n.ui-resizable-handle {\r\n  background-color: #007bff;\r\n  border-radius: 50%;\r\n  width: 8px !important;\r\n  height: 8px !important;\r\n  visibility: visible !important;\r\n}\r\n\r\n/* Ensure field elements show their resize handles */\r\n.template-field .ui-resizable-handle {\r\n  z-index: 90 !important;\r\n  opacity: 1 !important;\r\n  visibility: visible !important;\r\n}\r\n.template-qrcode {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  border: 1px dashed #ccc;\r\n}\r\n.template-image img {\r\n  width: 100%;\r\n  height: 100%;\r\n  object-fit: contain;\r\n}\r\n.template-rectangle {\r\n  border: 1px solid black;\r\n}\r\n.template-line {\r\n  background-color: black;\r\n}\r\n@media print {\r\n  .template-container {\r\n    margin: 0;\r\n    box-shadow: none;\r\n  }\r\n}', 0, '2025-05-31 00:29:39', '2025-05-31 01:07:18');
