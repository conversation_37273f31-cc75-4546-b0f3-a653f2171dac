
-- --------------------------------------------------------

--
-- Table structure for table `judge_assignments`
--
-- Creation: Jul 11, 2025 at 12:31 AM
--

CREATE TABLE `judge_assignments` (
  `id` int(10) UNSIGNED NOT NULL,
  `show_id` int(10) UNSIGNED NOT NULL,
  `judge_id` int(10) UNSIGNED NOT NULL,
  `category_id` int(10) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- RELATIONSHIPS FOR TABLE `judge_assignments`:
--

--
-- Dumping data for table `judge_assignments`
--

INSERT INTO `judge_assignments` (`id`, `show_id`, `judge_id`, `category_id`, `created_at`, `updated_at`) VALUES
(11, 9, 5, 152, '2025-07-11 00:31:44', '2025-07-11 00:31:44');
