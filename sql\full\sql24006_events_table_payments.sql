
-- --------------------------------------------------------

--
-- Table structure for table `payments`
--
-- Creation: Jul 10, 2025 at 01:46 PM
--

CREATE TABLE `payments` (
  `id` int(10) UNSIGNED NOT NULL,
  `user_id` int(10) UNSIGNED NOT NULL,
  `amount` decimal(10,2) NOT NULL,
  `payment_method_id` int(10) UNSIGNED DEFAULT NULL,
  `payment_status` varchar(20) NOT NULL DEFAULT 'pending',
  `payment_reference` varchar(255) DEFAULT NULL,
  `payment_date` timestamp NULL DEFAULT NULL,
  `payment_type` enum('registration','show_listing','manual','other') NOT NULL DEFAULT 'registration',
  `related_id` int(10) UNSIGNED NOT NULL,
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `is_manual` tinyint(1) NOT NULL DEFAULT 0,
  `processed_by` int(10) UNSIGNED DEFAULT NULL,
  `processed_at` datetime DEFAULT NULL,
  `admin_notes` text DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- RELATIONSHIPS FOR TABLE `payments`:
--   `user_id`
--       `users` -> `id`
--   `payment_method_id`
--       `payment_methods` -> `id`
--   `processed_by`
--       `users` -> `id`
--

--
-- Dumping data for table `payments`
--

INSERT INTO `payments` (`id`, `user_id`, `amount`, `payment_method_id`, `payment_status`, `payment_reference`, `payment_date`, `payment_type`, `related_id`, `notes`, `created_at`, `updated_at`, `is_manual`, `processed_by`, `processed_at`, `admin_notes`) VALUES
(1, 3, 20.00, 3, 'completed', '', NULL, 'registration', 60, '', '2025-06-13 11:55:13', '2025-06-13 11:55:13', 1, 3, NULL, NULL),
(2, 3, 20.00, 5, 'completed', '', NULL, 'registration', 60, '', '2025-06-13 11:55:27', '2025-06-13 11:55:27', 1, 3, NULL, NULL),
(3, 3, 20.00, 5, 'completed', '', NULL, 'registration', 60, '', '2025-06-13 11:57:02', '2025-06-13 11:57:02', 1, 3, NULL, NULL),
(4, 3, 20.00, 4, 'completed', '', NULL, 'registration', 60, '', '2025-06-13 11:57:15', '2025-06-13 11:57:15', 1, 3, NULL, NULL),
(5, 3, 20.00, 4, 'completed', '', NULL, 'registration', 60, '', '2025-06-13 11:57:41', '2025-06-13 11:57:41', 1, 3, NULL, NULL),
(6, 3, 20.00, 4, 'completed', '', NULL, 'registration', 60, '', '2025-06-13 12:00:39', '2025-06-13 12:00:39', 1, 3, NULL, NULL);
