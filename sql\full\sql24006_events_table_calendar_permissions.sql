
-- --------------------------------------------------------

--
-- Table structure for table `calendar_permissions`
--
-- Creation: Jun 23, 2025 at 08:30 PM
--

CREATE TABLE `calendar_permissions` (
  `id` int(10) UNSIGNED NOT NULL,
  `calendar_id` int(10) UNSIGNED NOT NULL,
  `user_id` int(10) UNSIGNED DEFAULT NULL,
  `role_id` int(10) UNSIGNED DEFAULT NULL,
  `permission` enum('view','edit','manage') NOT NULL DEFAULT 'view',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- RELATIONSHIPS FOR TABLE `calendar_permissions`:
--   `calendar_id`
--       `calendars` -> `id`
--   `user_id`
--       `users` -> `id`
--
