
-- --------------------------------------------------------

--
-- Table structure for table `event_notification_subscriptions`
--
-- Creation: Jun 23, 2025 at 08:30 PM
--

CREATE TABLE `event_notification_subscriptions` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `event_id` int(11) NOT NULL,
  `event_type` enum('calendar_event','car_show') NOT NULL DEFAULT 'calendar_event',
  `notification_times` text NOT NULL COMMENT 'JSON array of notification times in minutes before event',
  `notify_registration_end` tinyint(1) DEFAULT 0 COMMENT 'For car shows - notify before registration deadline',
  `registration_notification_times` text DEFAULT NULL COMMENT 'JSON array for registration deadline notifications',
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- RELATIONSHIPS FOR TABLE `event_notification_subscriptions`:
--

--
-- Dumping data for table `event_notification_subscriptions`
--

INSERT INTO `event_notification_subscriptions` (`id`, `user_id`, `event_id`, `event_type`, `notification_times`, `notify_registration_end`, `registration_notification_times`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 3, 10, 'calendar_event', '[60,1440]', 0, '[]', 0, '2025-06-22 23:39:58', '2025-06-23 18:32:29'),
(2, 3, 9, 'car_show', '[60,1440]', 0, '[1440]', 0, '2025-06-22 23:54:04', '2025-06-23 20:33:18'),
(3, 3, 13, 'calendar_event', '[60,1440]', 0, '[]', 0, '2025-06-23 12:20:26', '2025-06-23 12:20:40'),
(4, 3, 6, 'calendar_event', '[60,1440]', 0, '[]', 0, '2025-06-23 18:13:09', '2025-06-23 18:13:15'),
(5, 53, 9, 'car_show', '[60,1440]', 0, '[1440]', 0, '2025-06-24 12:04:31', '2025-06-24 12:04:37'),
(6, 3, 5, 'car_show', '[60,1440]', 0, '[1440]', 0, '2025-06-30 19:12:58', '2025-06-30 19:13:07'),
(7, 3, 9, 'calendar_event', '[60,1440]', 0, '[]', 0, '2025-07-02 22:22:56', '2025-07-08 13:08:58'),
(8, 3, 21, 'calendar_event', '[60,1440]', 0, '[]', 0, '2025-07-12 15:35:19', '2025-07-14 16:34:06'),
(9, 5, 5, 'car_show', '[60,1440]', 0, '[1440]', 1, '2025-07-20 12:14:13', '2025-07-20 12:14:13'),
(10, 5, 20, 'calendar_event', '[60,1440]', 0, '[]', 0, '2025-07-20 14:19:51', '2025-07-20 14:23:00');
