
-- --------------------------------------------------------

--
-- Table structure for table `judges`
--
-- Creation: Jul 11, 2025 at 10:54 AM
--

CREATE TABLE `judges` (
  `id` int(10) UNSIGNED NOT NULL,
  `show_id` int(10) UNSIGNED NOT NULL,
  `user_id` int(10) UNSIGNED NOT NULL,
  `categories` varchar(255) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `denial_note` text DEFAULT NULL,
  `denied_by` int(10) UNSIGNED DEFAULT NULL,
  `denied_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- RELATIONSHIPS FOR TABLE `judges`:
--   `denied_by`
--       `users` -> `id`
--   `show_id`
--       `shows` -> `id`
--   `user_id`
--       `users` -> `id`
--

--
-- Dumping data for table `judges`
--

INSERT INTO `judges` (`id`, `show_id`, `user_id`, `categories`, `created_at`, `updated_at`, `is_active`, `denial_note`, `denied_by`, `denied_at`) VALUES
(3, 5, 3, NULL, '2025-06-02 20:26:31', '2025-06-04 20:27:42', 1, NULL, NULL, NULL),
(4, 5, 4, NULL, '2025-06-02 23:54:11', '2025-06-04 20:27:42', 0, NULL, NULL, NULL),
(12, 5, 12, NULL, '2025-06-06 00:30:24', '2025-06-06 00:30:24', 1, NULL, NULL, NULL),
(102, 9, 3, NULL, '2025-07-11 11:37:05', '2025-07-11 11:37:15', 1, NULL, NULL, NULL);
