-- Performance Optimization Indexes for Event Gallery
-- These indexes dramatically improve query performance for thousands of photos

-- Images table indexes for event photo queries
CREATE INDEX IF NOT EXISTS idx_images_entity_type_id_created 
ON images (entity_type, entity_id, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_images_user_created 
ON images (user_id, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_images_entity_user 
ON images (entity_type, entity_id, user_id);

-- Event photo metadata indexes
CREATE INDEX IF NOT EXISTS idx_event_photo_metadata_image_id 
ON event_photo_metadata (image_id);

CREATE INDEX IF NOT EXISTS idx_event_photo_metadata_category 
ON event_photo_metadata (category);

-- Photo engagement stats indexes (for sorting by popularity)
CREATE INDEX IF NOT EXISTS idx_photo_engagement_likes 
ON photo_engagement_stats (likes_count DESC, photo_id);

CREATE INDEX IF NOT EXISTS idx_photo_engagement_comments 
ON photo_engagement_stats (comments_count DESC, photo_id);

CREATE INDEX IF NOT EXISTS idx_photo_engagement_combined 
ON photo_engagement_stats (photo_id, likes_count, comments_count);

-- Photo favorites indexes (for "my favorites" filter)
CREATE INDEX IF NOT EXISTS idx_photo_favorites_user_photo 
ON photo_favorites (user_id, photo_id);

CREATE INDEX IF NOT EXISTS idx_photo_favorites_photo_user 
ON photo_favorites (photo_id, user_id);

-- Photo likes indexes (for engagement queries)
CREATE INDEX IF NOT EXISTS idx_photo_likes_photo_user 
ON photo_likes (photo_id, user_id);

CREATE INDEX IF NOT EXISTS idx_photo_likes_user_created 
ON photo_likes (user_id, created_at DESC);

-- Photo comments indexes (for engagement queries)
CREATE INDEX IF NOT EXISTS idx_photo_comments_photo_approved 
ON photo_comments (photo_id, is_approved, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_photo_comments_user_created 
ON photo_comments (user_id, created_at DESC);

-- Users table index for name searches
CREATE INDEX IF NOT EXISTS idx_users_name_search 
ON users (name);

-- Composite indexes for complex queries
CREATE INDEX IF NOT EXISTS idx_images_full_query 
ON images (entity_type, entity_id, user_id, created_at DESC);

-- Analyze tables to update statistics (MySQL/MariaDB)
ANALYZE TABLE images;
ANALYZE TABLE event_photo_metadata;
ANALYZE TABLE photo_engagement_stats;
ANALYZE TABLE photo_favorites;
ANALYZE TABLE photo_likes;
ANALYZE TABLE photo_comments;
ANALYZE TABLE users;

-- Optional: Create a materialized view for popular photos (advanced optimization)
-- This can be updated periodically via cron job for even better performance

CREATE OR REPLACE VIEW popular_event_photos AS
SELECT 
    i.id,
    i.entity_type,
    i.entity_id,
    i.user_id,
    i.file_path,
    i.thumbnail_path,
    i.created_at,
    epm.category,
    epm.caption,
    u.name as uploader_name,
    COALESCE(pes.likes_count, 0) as likes_count,
    COALESCE(pes.comments_count, 0) as comments_count,
    COALESCE(pes.shares_count, 0) as shares_count,
    COALESCE(pes.favorites_count, 0) as favorites_count,
    -- Popularity score calculation
    (COALESCE(pes.likes_count, 0) * 1.0 + 
     COALESCE(pes.comments_count, 0) * 2.0 + 
     COALESCE(pes.shares_count, 0) * 3.0 + 
     COALESCE(pes.favorites_count, 0) * 1.5) as popularity_score
FROM images i
LEFT JOIN event_photo_metadata epm ON i.id = epm.image_id
LEFT JOIN users u ON i.user_id = u.id
LEFT JOIN photo_engagement_stats pes ON i.id = pes.photo_id
WHERE i.entity_type = 'event_photo'
ORDER BY popularity_score DESC, i.created_at DESC;

-- Performance monitoring queries (for debugging)
-- Use these to check query performance:

-- Check index usage:
-- EXPLAIN SELECT * FROM images WHERE entity_type = 'event_photo' AND entity_id = 5 ORDER BY created_at DESC LIMIT 20;

-- Check slow queries:
-- SELECT * FROM mysql.slow_log WHERE start_time > DATE_SUB(NOW(), INTERVAL 1 HOUR);

-- Check table sizes:
-- SELECT 
--     table_name,
--     ROUND(((data_length + index_length) / 1024 / 1024), 2) AS 'Size (MB)',
--     table_rows
-- FROM information_schema.tables 
-- WHERE table_schema = DATABASE()
-- AND table_name IN ('images', 'photo_engagement_stats', 'photo_comments', 'photo_likes', 'photo_favorites')
-- ORDER BY (data_length + index_length) DESC;
