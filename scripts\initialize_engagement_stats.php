<?php
/**
 * Initialize engagement stats for all existing photos
 * Web-accessible script following your system's pattern
 */

// Include the necessary files
require_once '../config/config.php';
require_once '../core/Database.php';
require_once '../helpers/session_helper.php';

// Start session and check admin access
session_start();
if (!isset($_SESSION['user_id']) || $_SESSION['user_role'] !== 'admin') {
    die('Access denied. Admin access required.');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Initialize Engagement Stats</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>Initialize Engagement Stats</h1>

    <?php
    try {
        $db = new Database();

        echo "<div class='info'>Starting engagement stats initialization...</div>\n";

        // Check if engagement tables exist
        $db->query("SHOW TABLES LIKE 'photo_engagement_stats'");
        $statsTableExists = $db->single();

        if (!$statsTableExists) {
            echo "<div class='error'>Error: photo_engagement_stats table does not exist. Please run the engagement_system.sql first.</div>";
            exit;
        }

        echo "<div class='success'>✓ photo_engagement_stats table exists</div>\n";

        // Get all photos that don't have engagement stats yet
        $db->query("
            SELECT i.id
            FROM images i
            LEFT JOIN photo_engagement_stats pes ON i.id = pes.photo_id
            WHERE i.entity_type = 'event_photo'
            AND pes.photo_id IS NULL
        ");

        $photosWithoutStats = $db->resultSet();
        $count = count($photosWithoutStats);

        echo "<div class='info'>Found $count photos without engagement stats.</div>\n";

        if ($count > 0) {
            // Insert engagement stats for all photos without them
            $db->query("
                INSERT IGNORE INTO photo_engagement_stats (photo_id, likes_count, comments_count, shares_count, favorites_count, tags_count)
                SELECT i.id, 0, 0, 0, 0, 0
                FROM images i
                LEFT JOIN photo_engagement_stats pes ON i.id = pes.photo_id
                WHERE i.entity_type = 'event_photo'
                AND pes.photo_id IS NULL
            ");

            $db->execute();

            echo "<div class='success'>✓ Successfully initialized engagement stats for $count photos.</div>\n";

            // Now update the stats with actual counts
            echo "<div class='info'>Updating stats with actual engagement counts...</div>\n";

            // Update likes count
            $db->query("
                UPDATE photo_engagement_stats pes
                SET likes_count = (
                    SELECT COUNT(*)
                    FROM photo_likes pl
                    WHERE pl.photo_id = pes.photo_id
                )
            ");
            $db->execute();
            echo "<div class='success'>✓ Updated likes count</div>\n";

            // Update comments count
            $db->query("
                UPDATE photo_engagement_stats pes
                SET comments_count = (
                    SELECT COUNT(*)
                    FROM photo_comments pc
                    WHERE pc.photo_id = pes.photo_id
                    AND pc.is_approved = 1
                )
            ");
            $db->execute();
            echo "<div class='success'>✓ Updated comments count</div>\n";

            // Update shares count
            $db->query("
                UPDATE photo_engagement_stats pes
                SET shares_count = (
                    SELECT COUNT(*)
                    FROM photo_shares ps
                    WHERE ps.photo_id = pes.photo_id
                )
            ");
            $db->execute();
            echo "<div class='success'>✓ Updated shares count</div>\n";

            // Update favorites count
            $db->query("
                UPDATE photo_engagement_stats pes
                SET favorites_count = (
                    SELECT COUNT(*)
                    FROM photo_favorites pf
                    WHERE pf.photo_id = pes.photo_id
                )
            ");
            $db->execute();
            echo "<div class='success'>✓ Updated favorites count</div>\n";

            // Update tags count (if table exists)
            $db->query("SHOW TABLES LIKE 'photo_user_tags'");
            $tagsTableExists = $db->single();
            if ($tagsTableExists) {
                $db->query("
                    UPDATE photo_engagement_stats pes
                    SET tags_count = (
                        SELECT COUNT(*)
                        FROM photo_user_tags put
                        WHERE put.photo_id = pes.photo_id
                        AND put.status = 'approved'
                    )
                ");
                $db->execute();
                echo "<div class='success'>✓ Updated tags count</div>\n";
            } else {
                echo "<div class='info'>⚠ photo_user_tags table doesn't exist, skipping tags count</div>\n";
            }

        } else {
            echo "<div class='info'>All photos already have engagement stats.</div>\n";
        }

        // Show final stats
        $db->query("SELECT COUNT(*) as total FROM photo_engagement_stats");
        $result = $db->single();
        echo "<div class='success'>✓ Total photos with engagement stats: " . $result->total . "</div>\n";

        // Show sample of engagement stats
        $db->query("SELECT * FROM photo_engagement_stats LIMIT 5");
        $samples = $db->resultSet();

        echo "<h3>Sample Engagement Stats:</h3>";
        echo "<pre>";
        foreach ($samples as $sample) {
            echo "Photo ID: {$sample->photo_id}, Likes: {$sample->likes_count}, Comments: {$sample->comments_count}, Shares: {$sample->shares_count}, Favorites: {$sample->favorites_count}\n";
        }
        echo "</pre>";

        echo "<div class='success'><strong>✓ Engagement stats initialization complete!</strong></div>\n";
        echo "<div class='info'>You can now go back to the gallery and the engagement features should work.</div>\n";

    } catch (Exception $e) {
        echo "<div class='error'>Error: " . htmlspecialchars($e->getMessage()) . "</div>\n";
        echo "<div class='error'>Stack trace:</div>\n";
        echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>\n";
    }
    ?>

    <p><a href="<?php echo BASE_URL; ?>/image_editor/eventGallery/show/5">← Back to Gallery</a></p>
</body>
</html>
