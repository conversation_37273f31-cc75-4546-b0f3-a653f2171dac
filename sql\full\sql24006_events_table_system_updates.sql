
-- --------------------------------------------------------

--
-- Table structure for table `system_updates`
--
-- Creation: Jun 23, 2025 at 08:30 PM
--

CREATE TABLE `system_updates` (
  `version` varchar(20) NOT NULL,
  `description` text DEFAULT NULL,
  `applied_at` datetime DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- RELATIONSHIPS FOR TABLE `system_updates`:
--

--
-- Dumping data for table `system_updates`
--

INSERT INTO `system_updates` (`version`, `description`, `applied_at`) VALUES
('2.19.26', 'Added registration deletion capabilities for admins and coordinators', '2025-05-27 08:29:22'),
('2.19.35', 'Added critical field protection to system fields', '2025-05-27 13:21:11'),
('2.19.92', 'Added missing methods for judging metrics management and ensured correct table structure', '2025-05-29 08:06:01'),
('2.19.93', 'Added category support to judging metrics and fixed related errors', '2025-05-29 08:06:21'),
('3.34.10', 'Fixed case sensitivity issues with scheduled tasks event names', '2025-06-09 10:36:10'),
('3.34.13', 'Fixed scheduled tasks execution for show completion', '2025-06-09 10:58:08'),
('3.34.20', 'Added timeout handling and auto-reset for scheduled tasks', '2025-06-09 11:29:53'),
('3.34.24', 'Background execution for all controllers', '2025-06-09 14:29:08'),
('3.34.27', 'Permission-based image browsing', '2025-06-11 08:48:14');
