
--
-- Indexes for dumped tables
--

--
-- Indexes for table `admin_email_folders`
--
ALTER TABLE `admin_email_folders`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_admin_folder` (`admin_user_id`,`name`),
  ADD KEY `idx_admin_user` (`admin_user_id`),
  ADD KEY `idx_sort_order` (`admin_user_id`,`sort_order`);

--
-- Indexes for table `admin_impersonation`
--
ALTER TABLE `admin_impersonation`
  ADD PRIMARY KEY (`id`),
  ADD KEY `admin_id` (`admin_id`),
  ADD KEY `impersonated_user_id` (`impersonated_user_id`),
  ADD KEY `session_id` (`session_id`);

--
-- Indexes for table `age_weights`
--
ALTER TABLE `age_weights`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `awards`
--
ALTER TABLE `awards`
  ADD PRIMARY KEY (`id`),
  ADD KEY `show_id` (`show_id`),
  ADD KEY `category_id` (`category_id`),
  ADD KEY `registration_id` (`registration_id`);

--
-- Indexes for table `calendars`
--
ALTER TABLE `calendars`
  ADD PRIMARY KEY (`id`),
  ADD KEY `owner_id` (`owner_id`),
  ADD KEY `idx_calendars_name` (`name`),
  ADD KEY `idx_calendars_owner_id` (`owner_id`),
  ADD KEY `idx_calendars_is_visible` (`is_visible`),
  ADD KEY `idx_calendars_is_public` (`is_public`),
  ADD KEY `idx_calendars_created_at` (`created_at`),
  ADD KEY `idx_calendars_updated_at` (`updated_at`),
  ADD KEY `idx_calendars_owner_visible` (`owner_id`,`is_visible`),
  ADD KEY `idx_calendars_public_visible` (`is_public`,`is_visible`);

--
-- Indexes for table `calendar_clubs`
--
ALTER TABLE `calendar_clubs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_owner_id` (`owner_id`),
  ADD KEY `idx_verification_status` (`verification_status`),
  ADD KEY `idx_clubs_name` (`name`),
  ADD KEY `idx_clubs_email` (`email`),
  ADD KEY `idx_clubs_owner_id` (`owner_id`),
  ADD KEY `idx_clubs_verification_status` (`verification_status`),
  ADD KEY `idx_clubs_is_verified` (`is_verified`),
  ADD KEY `idx_clubs_created_at` (`created_at`),
  ADD KEY `idx_clubs_updated_at` (`updated_at`),
  ADD KEY `idx_clubs_name_desc` (`name`,`description`(100)),
  ADD KEY `idx_clubs_owner_verified` (`owner_id`,`is_verified`),
  ADD KEY `idx_calendar_clubs_name` (`name`);

--
-- Indexes for table `calendar_club_members`
--
ALTER TABLE `calendar_club_members`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `club_user_unique` (`club_id`,`user_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `idx_club_members_club_id` (`club_id`),
  ADD KEY `idx_club_members_user_id` (`user_id`),
  ADD KEY `idx_club_members_role` (`role`),
  ADD KEY `idx_club_members_joined_at` (`joined_at`),
  ADD KEY `idx_club_members_club_role` (`club_id`,`role`),
  ADD KEY `idx_club_members_user_club` (`user_id`,`club_id`);

--
-- Indexes for table `calendar_events`
--
ALTER TABLE `calendar_events`
  ADD PRIMARY KEY (`id`),
  ADD KEY `calendar_id` (`calendar_id`),
  ADD KEY `venue_id` (`venue_id`),
  ADD KEY `show_id` (`show_id`),
  ADD KEY `created_by` (`created_by`),
  ADD KEY `idx_calendar_events_created_at` (`created_at`),
  ADD KEY `idx_calendar_events_updated_at` (`updated_at`),
  ADD KEY `idx_calendar_events_start_date` (`start_date`),
  ADD KEY `idx_events_title` (`title`),
  ADD KEY `idx_events_start_date` (`start_date`),
  ADD KEY `idx_events_end_date` (`end_date`),
  ADD KEY `idx_events_privacy` (`privacy`),
  ADD KEY `idx_events_created_by` (`created_by`),
  ADD KEY `idx_events_calendar_dates` (`calendar_id`,`start_date`,`end_date`),
  ADD KEY `idx_events_title_desc` (`title`,`description`(100)),
  ADD KEY `idx_events_date_range` (`start_date`,`end_date`,`privacy`),
  ADD KEY `idx_calendar_events_created_by` (`created_by`),
  ADD KEY `idx_calendar_events_calendar_id` (`calendar_id`),
  ADD KEY `idx_calendar_events_end_date` (`end_date`),
  ADD KEY `idx_calendar_events_privacy` (`privacy`),
  ADD KEY `idx_main_image_id` (`main_image_id`);

--
-- Indexes for table `calendar_event_clubs`
--
ALTER TABLE `calendar_event_clubs`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `event_club_unique` (`event_id`,`club_id`),
  ADD KEY `club_id` (`club_id`);

--
-- Indexes for table `calendar_event_images`
--
ALTER TABLE `calendar_event_images`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_event_id` (`event_id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_display_order` (`display_order`),
  ADD KEY `idx_is_featured` (`is_featured`);

--
-- Indexes for table `calendar_imports`
--
ALTER TABLE `calendar_imports`
  ADD PRIMARY KEY (`id`),
  ADD KEY `calendar_id` (`calendar_id`),
  ADD KEY `created_by` (`created_by`);

--
-- Indexes for table `calendar_notifications`
--
ALTER TABLE `calendar_notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `event_id` (`event_id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `calendar_permissions`
--
ALTER TABLE `calendar_permissions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `calendar_id` (`calendar_id`),
  ADD KEY `user_id` (`user_id`);

--
-- Indexes for table `calendar_settings`
--
ALTER TABLE `calendar_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `setting_key` (`setting_key`);

--
-- Indexes for table `calendar_venues`
--
ALTER TABLE `calendar_venues`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_venues_name` (`name`),
  ADD KEY `idx_venues_city` (`city`),
  ADD KEY `idx_venues_state` (`state`),
  ADD KEY `idx_venues_zip` (`zip`),
  ADD KEY `idx_venues_created_at` (`created_at`),
  ADD KEY `idx_venues_updated_at` (`updated_at`),
  ADD KEY `idx_venues_location` (`city`,`state`),
  ADD KEY `idx_venues_name_location` (`name`,`city`,`state`),
  ADD KEY `idx_calendar_venues_name` (`name`),
  ADD KEY `idx_calendar_venues_city` (`city`),
  ADD KEY `idx_calendar_venues_state` (`state`);

--
-- Indexes for table `camera_banners`
--
ALTER TABLE `camera_banners`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_active` (`active`),
  ADD KEY `idx_sort_order` (`sort_order`),
  ADD KEY `idx_type` (`type`);

--
-- Indexes for table `category_winners`
--
ALTER TABLE `category_winners`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_winner` (`show_id`,`category_id`,`place`),
  ADD KEY `idx_show_id` (`show_id`),
  ADD KEY `idx_category_id` (`category_id`),
  ADD KEY `idx_vehicle_id` (`vehicle_id`),
  ADD KEY `idx_registration_id` (`registration_id`);

--
-- Indexes for table `club_ownership_verifications`
--
ALTER TABLE `club_ownership_verifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `club_id` (`club_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `status` (`status`),
  ADD KEY `reviewed_by` (`reviewed_by`),
  ADD KEY `idx_ownership_club_id` (`club_id`),
  ADD KEY `idx_ownership_user_id` (`user_id`),
  ADD KEY `idx_ownership_status` (`status`),
  ADD KEY `idx_ownership_requested_at` (`requested_at`),
  ADD KEY `idx_ownership_reviewed_at` (`reviewed_at`),
  ADD KEY `idx_ownership_status_date` (`status`,`requested_at`);

--
-- Indexes for table `coordinator_payment_settings`
--
ALTER TABLE `coordinator_payment_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `coordinator_setting_key` (`coordinator_id`,`setting_key`);

--
-- Indexes for table `custom_field_definitions`
--
ALTER TABLE `custom_field_definitions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `field_id` (`field_id`);

--
-- Indexes for table `custom_field_values`
--
ALTER TABLE `custom_field_values`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_field_per_show` (`show_id`,`field_id`),
  ADD KEY `field_id_index` (`field_id`),
  ADD KEY `field_id` (`field_id`),
  ADD KEY `idx_field_type` (`field_type`);

--
-- Indexes for table `database_updates`
--
ALTER TABLE `database_updates`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `default_age_weights`
--
ALTER TABLE `default_age_weights`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `default_categories`
--
ALTER TABLE `default_categories`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `default_metrics`
--
ALTER TABLE `default_metrics`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `default_templates`
--
ALTER TABLE `default_templates`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `entity_type` (`entity_type`),
  ADD KEY `idx_template_id` (`template_id`),
  ADD KEY `idx_created_by` (`created_by`);

--
-- Indexes for table `deletion_requests`
--
ALTER TABLE `deletion_requests`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `facebook_id` (`facebook_id`),
  ADD KEY `confirmation_token` (`confirmation_token`),
  ADD KEY `status` (`status`);

--
-- Indexes for table `email_folders`
--
ALTER TABLE `email_folders`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_created_by` (`created_by`);

--
-- Indexes for table `email_processing_log`
--
ALTER TABLE `email_processing_log`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_email_message` (`email_message_id`),
  ADD KEY `idx_email_message_id` (`email_message_id`),
  ADD KEY `idx_sender` (`sender_email`),
  ADD KEY `idx_ticket` (`ticket_number`),
  ADD KEY `idx_status` (`processing_status`),
  ADD KEY `idx_created` (`created_at` DESC);

--
-- Indexes for table `email_reminders`
--
ALTER TABLE `email_reminders`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_message_id` (`message_id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_reminder_date` (`reminder_date`),
  ADD KEY `idx_is_completed` (`is_completed`);

--
-- Indexes for table `email_statistics`
--
ALTER TABLE `email_statistics`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_date` (`date`);

--
-- Indexes for table `email_templates`
--
ALTER TABLE `email_templates`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_category` (`category`),
  ADD KEY `idx_is_active` (`is_active`),
  ADD KEY `idx_created_by` (`created_by`);

--
-- Indexes for table `engagement_notifications`
--
ALTER TABLE `engagement_notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `from_user_id` (`from_user_id`),
  ADD KEY `idx_user_notifications` (`user_id`,`is_read`,`created_at`),
  ADD KEY `idx_photo_notifications` (`photo_id`,`type`);

--
-- Indexes for table `event_categories`
--
ALTER TABLE `event_categories`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `event_category_mapping`
--
ALTER TABLE `event_category_mapping`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_event_category` (`event_id`,`category_id`),
  ADD KEY `category_id` (`category_id`);

--
-- Indexes for table `event_log`
--
ALTER TABLE `event_log`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_event_name` (`event_name`),
  ADD KEY `idx_created_at` (`created_at`);

--
-- Indexes for table `event_notification_subscriptions`
--
ALTER TABLE `event_notification_subscriptions`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `user_event_subscription` (`user_id`,`event_id`,`event_type`),
  ADD KEY `idx_user_subscriptions` (`user_id`),
  ADD KEY `idx_event_subscriptions` (`event_id`,`event_type`),
  ADD KEY `idx_active_subscriptions` (`is_active`);

--
-- Indexes for table `event_photo_admin_settings`
--
ALTER TABLE `event_photo_admin_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `setting_name` (`setting_name`),
  ADD KEY `idx_admin_settings_name` (`setting_name`);

--
-- Indexes for table `event_photo_custom_categories`
--
ALTER TABLE `event_photo_custom_categories`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `category_key` (`category_key`),
  ADD KEY `idx_active_sort` (`is_active`,`sort_order`),
  ADD KEY `idx_category_key` (`category_key`);

--
-- Indexes for table `event_photo_metadata`
--
ALTER TABLE `event_photo_metadata`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_event_photo_image` (`image_id`),
  ADD KEY `idx_event_photo_event` (`event_type`,`event_id`),
  ADD KEY `idx_event_photo_category` (`category`),
  ADD KEY `idx_event_photo_privacy` (`privacy_level`),
  ADD KEY `idx_event_photo_location` (`latitude`,`longitude`),
  ADD KEY `idx_event_photos` (`event_type`,`event_id`),
  ADD KEY `idx_event_photo_metadata_image_id` (`image_id`),
  ADD KEY `idx_event_photo_metadata_category` (`category`);

--
-- Indexes for table `event_photo_settings`
--
ALTER TABLE `event_photo_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `setting_name` (`setting_name`),
  ADD KEY `idx_settings_name` (`setting_name`);

--
-- Indexes for table `event_tags`
--
ALTER TABLE `event_tags`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `event_tag_mapping`
--
ALTER TABLE `event_tag_mapping`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_event_tag` (`event_id`,`tag_id`),
  ADD KEY `tag_id` (`tag_id`);

--
-- Indexes for table `fan_votes`
--
ALTER TABLE `fan_votes`
  ADD PRIMARY KEY (`id`),
  ADD KEY `registration_id` (`registration_id`),
  ADD KEY `idx_fan_votes_fb_user_id` (`fb_user_id`),
  ADD KEY `idx_fan_votes_voter_ip` (`voter_ip`);

--
-- Indexes for table `fcm_tokens`
--
ALTER TABLE `fcm_tokens`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_active` (`active`),
  ADD KEY `idx_user_active` (`user_id`,`active`);

--
-- Indexes for table `field_mappings`
--
ALTER TABLE `field_mappings`
  ADD PRIMARY KEY (`id`),
  ADD KEY `form_field_id` (`form_field_id`),
  ADD KEY `template_field_id` (`template_field_id`),
  ADD KEY `db_column` (`db_column`),
  ADD KEY `database_column` (`database_column`),
  ADD KEY `idx_field_type` (`field_type`);

--
-- Indexes for table `field_mappings_backup`
--
ALTER TABLE `field_mappings_backup`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `template_field_id` (`template_field_id`);

--
-- Indexes for table `field_mappings_new`
--
ALTER TABLE `field_mappings_new`
  ADD PRIMARY KEY (`id`),
  ADD KEY `form_field_id` (`form_field_id`),
  ADD KEY `template_field_id` (`template_field_id`),
  ADD KEY `db_column` (`db_column`),
  ADD KEY `database_column` (`database_column`);

--
-- Indexes for table `form_submissions`
--
ALTER TABLE `form_submissions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `template_id` (`template_id`),
  ADD KEY `entity_index` (`entity_type`,`entity_id`);

--
-- Indexes for table `form_templates`
--
ALTER TABLE `form_templates`
  ADD PRIMARY KEY (`id`),
  ADD KEY `type_entity` (`type`,`entity_id`);

--
-- Indexes for table `images`
--
ALTER TABLE `images`
  ADD PRIMARY KEY (`id`),
  ADD KEY `entity_index` (`entity_type`,`entity_id`),
  ADD KEY `idx_user_id` (`user_id`),
  ADD KEY `idx_entity` (`entity_type`,`entity_id`),
  ADD KEY `idx_entity_engagement` (`entity_type`,`created_at`),
  ADD KEY `idx_images_entity_type_id_created` (`entity_type`,`entity_id`,`created_at` DESC),
  ADD KEY `idx_images_user_created` (`user_id`,`created_at` DESC),
  ADD KEY `idx_images_entity_user` (`entity_type`,`entity_id`,`user_id`),
  ADD KEY `idx_images_full_query` (`entity_type`,`entity_id`,`user_id`,`created_at` DESC);

--
-- Indexes for table `image_edits`
--
ALTER TABLE `image_edits`
  ADD PRIMARY KEY (`id`),
  ADD KEY `image_id` (`image_id`);

--
-- Indexes for table `judges`
--
ALTER TABLE `judges`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_judge` (`show_id`,`user_id`),
  ADD KEY `show_user` (`show_id`,`user_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `fk_judges_denied_by` (`denied_by`),
  ADD KEY `idx_judges_is_active_denied` (`is_active`,`denied_at`);

--
-- Indexes for table `judge_assignments`
--
ALTER TABLE `judge_assignments`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `judge_categories`
--
ALTER TABLE `judge_categories`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_judge_category` (`judge_id`,`category_id`);

--
-- Indexes for table `judge_metric_scores`
--
ALTER TABLE `judge_metric_scores`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_judge_metric` (`judge_total_score_id`,`metric_id`),
  ADD KEY `idx_vehicle_id` (`vehicle_id`),
  ADD KEY `idx_show_id` (`show_id`),
  ADD KEY `idx_metric_id` (`metric_id`),
  ADD KEY `idx_judge_id` (`judge_id`);

--
-- Indexes for table `judge_total_scores`
--
ALTER TABLE `judge_total_scores`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_judge_vehicle` (`show_id`,`vehicle_id`,`judge_id`),
  ADD KEY `idx_show_id` (`show_id`),
  ADD KEY `idx_vehicle_id` (`vehicle_id`),
  ADD KEY `idx_registration_id` (`registration_id`),
  ADD KEY `idx_judge_id` (`judge_id`);

--
-- Indexes for table `judging_metrics`
--
ALTER TABLE `judging_metrics`
  ADD PRIMARY KEY (`id`),
  ADD KEY `show_id` (`show_id`),
  ADD KEY `category_id` (`category_id`);

--
-- Indexes for table `layouts`
--
ALTER TABLE `layouts`
  ADD PRIMARY KEY (`id`),
  ADD KEY `type` (`type`),
  ADD KEY `is_active` (`is_active`);

--
-- Indexes for table `listing_fee`
--
ALTER TABLE `listing_fee`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `code` (`code`);

--
-- Indexes for table `messages`
--
ALTER TABLE `messages`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_to_user` (`to_user_id`),
  ADD KEY `idx_from_user` (`from_user_id`),
  ADD KEY `idx_show` (`show_id`),
  ADD KEY `idx_parent` (`parent_message_id`),
  ADD KEY `idx_unread` (`to_user_id`,`is_read`),
  ADD KEY `idx_created` (`created_at` DESC),
  ADD KEY `idx_type` (`message_type`),
  ADD KEY `idx_messages_conversation` (`from_user_id`,`to_user_id`,`created_at` DESC),
  ADD KEY `idx_messages_user_unread` (`to_user_id`,`is_read`,`created_at` DESC),
  ADD KEY `idx_ticket` (`ticket_number`),
  ADD KEY `idx_email_id` (`email_message_id`),
  ADD KEY `idx_folder` (`folder_id`),
  ADD KEY `idx_owner` (`owned_by_admin_id`),
  ADD KEY `idx_ticket_security` (`ticket_number`,`security_token`);

--
-- Indexes for table `message_attachments`
--
ALTER TABLE `message_attachments`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_message` (`message_id`),
  ADD KEY `idx_filename` (`filename`);

--
-- Indexes for table `message_deliveries`
--
ALTER TABLE `message_deliveries`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_message` (`message_id`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_method` (`delivery_method`),
  ADD KEY `idx_pending` (`status`,`delivery_method`,`created_at`),
  ADD KEY `idx_deliveries_pending` (`status`,`delivery_method`,`created_at`);

--
-- Indexes for table `message_reminders`
--
ALTER TABLE `message_reminders`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_message` (`message_id`),
  ADD KEY `idx_admin_user` (`admin_user_id`),
  ADD KEY `idx_reminder_time` (`reminder_time`,`is_sent`),
  ADD KEY `idx_pending` (`is_sent`,`reminder_time`);

--
-- Indexes for table `notification_center_items`
--
ALTER TABLE `notification_center_items`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_type` (`user_id`,`notification_type`),
  ADD KEY `idx_user_unread` (`user_id`,`is_read`),
  ADD KEY `idx_source` (`source_table`,`source_id`),
  ADD KEY `idx_created` (`created_at` DESC);

--
-- Indexes for table `notification_log`
--
ALTER TABLE `notification_log`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_notification_log_user_id` (`user_id`);

--
-- Indexes for table `notification_queue`
--
ALTER TABLE `notification_queue`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_notifications` (`user_id`),
  ADD KEY `idx_status_notifications` (`status`),
  ADD KEY `idx_subscription_notifications` (`subscription_id`),
  ADD KEY `idx_event_notifications` (`event_id`,`event_type`),
  ADD KEY `idx_scheduled_notifications_new` (`scheduled_for`,`status`),
  ADD KEY `idx_notification_queue_user_type` (`user_id`,`notification_type`),
  ADD KEY `idx_notification_queue_status` (`status`,`scheduled_for`);

--
-- Indexes for table `notification_settings`
--
ALTER TABLE `notification_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `setting_key` (`setting_key`);

--
-- Indexes for table `offline_sync_queue`
--
ALTER TABLE `offline_sync_queue`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_offline_sync_queue_user_id` (`user_id`),
  ADD KEY `idx_offline_sync_queue_status` (`status`);

--
-- Indexes for table `password_reset_tokens`
--
ALTER TABLE `password_reset_tokens`
  ADD PRIMARY KEY (`id`),
  ADD KEY `email` (`email`);

--
-- Indexes for table `payments`
--
ALTER TABLE `payments`
  ADD PRIMARY KEY (`id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `payment_method_id` (`payment_method_id`),
  ADD KEY `processed_by` (`processed_by`),
  ADD KEY `idx_payments_user_id` (`user_id`),
  ADD KEY `idx_payments_related_id` (`related_id`),
  ADD KEY `idx_payments_payment_status` (`payment_status`),
  ADD KEY `idx_payments_created_at` (`created_at`),
  ADD KEY `idx_payments_amount` (`amount`),
  ADD KEY `idx_payments_payment_type` (`payment_type`),
  ADD KEY `idx_payments_user_status` (`user_id`,`payment_status`),
  ADD KEY `idx_payments_status_date` (`payment_status`,`created_at`),
  ADD KEY `idx_payments_type_related` (`payment_type`,`related_id`);

--
-- Indexes for table `payment_methods`
--
ALTER TABLE `payment_methods`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `payment_settings`
--
ALTER TABLE `payment_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `setting_key_is_admin` (`setting_key`,`is_admin`);

--
-- Indexes for table `photo_comments`
--
ALTER TABLE `photo_comments`
  ADD PRIMARY KEY (`id`),
  ADD KEY `parent_comment_id` (`parent_comment_id`),
  ADD KEY `idx_photo_comments` (`photo_id`,`created_at`),
  ADD KEY `idx_user_comments` (`user_id`,`created_at`),
  ADD KEY `idx_photo_comments_photo_approved` (`photo_id`,`is_approved`,`created_at` DESC),
  ADD KEY `idx_photo_comments_user_created` (`user_id`,`created_at` DESC);

--
-- Indexes for table `photo_engagement_stats`
--
ALTER TABLE `photo_engagement_stats`
  ADD PRIMARY KEY (`photo_id`),
  ADD KEY `idx_photo_engagement_likes` (`likes_count` DESC,`photo_id`),
  ADD KEY `idx_photo_engagement_comments` (`comments_count` DESC,`photo_id`),
  ADD KEY `idx_photo_engagement_combined` (`photo_id`,`likes_count`,`comments_count`);

--
-- Indexes for table `photo_favorites`
--
ALTER TABLE `photo_favorites`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_favorite` (`photo_id`,`user_id`),
  ADD KEY `idx_user_favorites` (`user_id`,`collection_name`,`created_at`),
  ADD KEY `idx_photo_favorites_user_photo` (`user_id`,`photo_id`),
  ADD KEY `idx_photo_favorites_photo_user` (`photo_id`,`user_id`);

--
-- Indexes for table `photo_likes`
--
ALTER TABLE `photo_likes`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_like` (`photo_id`,`user_id`),
  ADD KEY `idx_photo_likes_photo_user` (`photo_id`,`user_id`),
  ADD KEY `idx_photo_likes_user_created` (`user_id`,`created_at` DESC);

--
-- Indexes for table `photo_shares`
--
ALTER TABLE `photo_shares`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_photo_shares` (`photo_id`,`platform`),
  ADD KEY `idx_user_shares` (`user_id`,`shared_at`);

--
-- Indexes for table `photo_user_tags`
--
ALTER TABLE `photo_user_tags`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_tag` (`photo_id`,`tagged_user_id`),
  ADD KEY `tagged_by_user_id` (`tagged_by_user_id`),
  ADD KEY `idx_user_tags` (`tagged_user_id`,`status`);

--
-- Indexes for table `pre_approved_cords`
--
ALTER TABLE `pre_approved_cords`
  ADD PRIMARY KEY (`id`),
  ADD KEY `coordinator_id` (`coordinator_id`);

--
-- Indexes for table `printable_templates`
--
ALTER TABLE `printable_templates`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `push_subscriptions`
--
ALTER TABLE `push_subscriptions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_push_subscriptions_user_id` (`user_id`),
  ADD KEY `idx_push_subscriptions_active` (`active`);

--
-- Indexes for table `pwa_metrics`
--
ALTER TABLE `pwa_metrics`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `qr_scans`
--
ALTER TABLE `qr_scans`
  ADD PRIMARY KEY (`id`),
  ADD KEY `entity_type` (`entity_type`),
  ADD KEY `entity_id` (`entity_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `user_role` (`user_role`);

--
-- Indexes for table `registrations`
--
ALTER TABLE `registrations`
  ADD PRIMARY KEY (`id`),
  ADD KEY `show_id` (`show_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `vehicle_id` (`vehicle_id`),
  ADD KEY `category_id` (`category_id`),
  ADD KEY `idx_registrations_show_id` (`show_id`),
  ADD KEY `idx_registrations_vehicle_id` (`vehicle_id`),
  ADD KEY `idx_registrations_category_id` (`category_id`),
  ADD KEY `idx_registrations_payment_status` (`payment_status`),
  ADD KEY `idx_registrations_status` (`status`),
  ADD KEY `idx_registrations_created_at` (`created_at`),
  ADD KEY `idx_registrations_vehicle_show` (`vehicle_id`,`show_id`),
  ADD KEY `idx_registrations_payment_created` (`payment_status`,`created_at`),
  ADD KEY `idx_reg_complex_user_date` (`vehicle_id`,`created_at` DESC),
  ADD KEY `idx_reg_complex_show_payment` (`show_id`,`payment_status`),
  ADD KEY `idx_registrations_updated_at` (`updated_at`),
  ADD KEY `idx_registrations_status_date` (`status`,`created_at`),
  ADD KEY `idx_registrations_vehicle_payment` (`vehicle_id`,`payment_status`);

--
-- Indexes for table `remember_tokens`
--
ALTER TABLE `remember_tokens`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `results`
--
ALTER TABLE `results`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `scheduled_tasks`
--
ALTER TABLE `scheduled_tasks`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_trigger_type` (`trigger_type`),
  ADD KEY `idx_event_name` (`event_name`),
  ADD KEY `idx_status` (`status`),
  ADD KEY `idx_next_run` (`next_run`);

--
-- Indexes for table `scores`
--
ALTER TABLE `scores`
  ADD PRIMARY KEY (`id`),
  ADD KEY `registration_judge_metric` (`registration_id`,`judge_id`,`metric_id`),
  ADD KEY `judge_id` (`judge_id`),
  ADD KEY `metric_id` (`metric_id`),
  ADD KEY `idx_scores_registration` (`registration_id`),
  ADD KEY `idx_scores_judge` (`judge_id`),
  ADD KEY `idx_scores_metric` (`metric_id`);

--
-- Indexes for table `scoring_formulas`
--
ALTER TABLE `scoring_formulas`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `settings`
--
ALTER TABLE `settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `name` (`name`);

--
-- Indexes for table `shows`
--
ALTER TABLE `shows`
  ADD PRIMARY KEY (`id`),
  ADD KEY `coordinator_id` (`coordinator_id`),
  ADD KEY `fk_shows_featured_image` (`featured_image_id`),
  ADD KEY `idx_shows_status` (`status`),
  ADD KEY `idx_shows_start_date` (`start_date`),
  ADD KEY `idx_shows_name` (`name`),
  ADD KEY `idx_shows_coordinator_id` (`coordinator_id`),
  ADD KEY `idx_shows_end_date` (`end_date`),
  ADD KEY `idx_shows_created_at` (`created_at`),
  ADD KEY `idx_shows_location` (`location`),
  ADD KEY `idx_shows_coordinator_status` (`coordinator_id`,`status`),
  ADD KEY `idx_shows_coordinator_date` (`coordinator_id`,`start_date`),
  ADD KEY `idx_shows_status_date` (`status`,`start_date`);

--
-- Indexes for table `shows_backup`
--
ALTER TABLE `shows_backup`
  ADD PRIMARY KEY (`id`),
  ADD KEY `coordinator_id` (`coordinator_id`),
  ADD KEY `fk_shows_featured_image` (`featured_image_id`),
  ADD KEY `idx_shows_status` (`status`),
  ADD KEY `idx_shows_start_date` (`start_date`),
  ADD KEY `idx_shows_name` (`name`),
  ADD KEY `idx_shows_coordinator_id` (`coordinator_id`);

--
-- Indexes for table `shows_backup_v3_35_0`
--
ALTER TABLE `shows_backup_v3_35_0`
  ADD PRIMARY KEY (`id`),
  ADD KEY `coordinator_id` (`coordinator_id`),
  ADD KEY `fk_shows_featured_image` (`featured_image_id`),
  ADD KEY `idx_shows_status` (`status`),
  ADD KEY `idx_shows_start_date` (`start_date`),
  ADD KEY `idx_shows_name` (`name`),
  ADD KEY `idx_shows_coordinator_id` (`coordinator_id`);

--
-- Indexes for table `show_categories`
--
ALTER TABLE `show_categories`
  ADD PRIMARY KEY (`id`),
  ADD KEY `show_id` (`show_id`),
  ADD KEY `idx_show_categories_show_id` (`show_id`),
  ADD KEY `idx_show_categories_name` (`name`);

--
-- Indexes for table `show_payment_methods`
--
ALTER TABLE `show_payment_methods`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `show_payment_method` (`show_id`,`payment_method_id`),
  ADD KEY `show_id` (`show_id`),
  ADD KEY `payment_method_id` (`payment_method_id`);

--
-- Indexes for table `show_payment_settings`
--
ALTER TABLE `show_payment_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `show_setting_key` (`show_id`,`setting_key`,`is_admin`),
  ADD KEY `show_id` (`show_id`);

--
-- Indexes for table `show_role_assignments`
--
ALTER TABLE `show_role_assignments`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_active_assignment` (`show_id`,`user_id`,`assigned_role`),
  ADD KEY `show_id` (`show_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `assigned_by` (`assigned_by`),
  ADD KEY `request_id` (`request_id`),
  ADD KEY `is_active` (`is_active`),
  ADD KEY `auto_cleanup_date` (`auto_cleanup_date`),
  ADD KEY `expires_at` (`expires_at`),
  ADD KEY `idx_show_role_assignments_active` (`is_active`,`show_id`),
  ADD KEY `idx_show_role_assignments_cleanup` (`auto_cleanup_date`,`is_active`);

--
-- Indexes for table `show_role_requests`
--
ALTER TABLE `show_role_requests`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_pending_request` (`show_id`,`user_id`,`requested_role`,`status`),
  ADD KEY `show_id` (`show_id`),
  ADD KEY `user_id` (`user_id`),
  ADD KEY `requested_by` (`requested_by`),
  ADD KEY `status` (`status`),
  ADD KEY `expires_at` (`expires_at`),
  ADD KEY `idx_show_role_requests_pending` (`status`,`expires_at`),
  ADD KEY `fk_show_role_requests_denied_by` (`denied_by`),
  ADD KEY `idx_show_role_requests_status_denied` (`status`,`denied_at`);

--
-- Indexes for table `show_scoring_settings`
--
ALTER TABLE `show_scoring_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `show_id` (`show_id`),
  ADD KEY `formula_id` (`formula_id`);

--
-- Indexes for table `site_settings`
--
ALTER TABLE `site_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `setting_key` (`setting_key`);

--
-- Indexes for table `sms_providers`
--
ALTER TABLE `sms_providers`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `provider_key` (`provider_key`),
  ADD KEY `idx_active_providers` (`is_active`),
  ADD KEY `idx_default_provider` (`is_default`);

--
-- Indexes for table `staff_assignments`
--
ALTER TABLE `staff_assignments`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `staff_show_unique` (`staff_id`,`show_id`),
  ADD KEY `staff_id` (`staff_id`),
  ADD KEY `show_id` (`show_id`),
  ADD KEY `assigned_by` (`assigned_by`),
  ADD KEY `idx_staff_assignments_staff_id` (`staff_id`),
  ADD KEY `idx_staff_assignments_show_id` (`show_id`),
  ADD KEY `idx_staff_assignments_assigned_by` (`assigned_by`);

--
-- Indexes for table `system_fields`
--
ALTER TABLE `system_fields`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `field_id` (`field_id`);

--
-- Indexes for table `system_logs`
--
ALTER TABLE `system_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `log_type` (`log_type`),
  ADD KEY `created_at` (`created_at`);

--
-- Indexes for table `system_settings`
--
ALTER TABLE `system_settings`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `setting_key` (`setting_key`);

--
-- Indexes for table `system_updates`
--
ALTER TABLE `system_updates`
  ADD PRIMARY KEY (`version`);

--
-- Indexes for table `task_logs`
--
ALTER TABLE `task_logs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_task_id` (`task_id`),
  ADD KEY `idx_created_at` (`created_at`),
  ADD KEY `idx_is_error` (`is_error`);

--
-- Indexes for table `ticket_numbers`
--
ALTER TABLE `ticket_numbers`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_ticket` (`ticket_number`),
  ADD UNIQUE KEY `unique_year_sequence` (`prefix`,`year`,`sequence`),
  ADD KEY `idx_ticket_number` (`ticket_number`),
  ADD KEY `idx_year_sequence` (`year`,`sequence`),
  ADD KEY `idx_message` (`message_id`),
  ADD KEY `idx_ticket_security` (`ticket_number`,`security_token`),
  ADD KEY `idx_email_message_id` (`email_message_id`);

--
-- Indexes for table `updates`
--
ALTER TABLE `updates`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `email` (`email`),
  ADD KEY `idx_facebook_id` (`facebook_id`),
  ADD KEY `idx_users_name` (`name`),
  ADD KEY `idx_users_email_search` (`email`),
  ADD KEY `idx_users_role` (`role`),
  ADD KEY `idx_users_status` (`status`),
  ADD KEY `idx_users_created_at` (`created_at`),
  ADD KEY `idx_users_last_login` (`last_login`),
  ADD KEY `idx_users_role_status` (`role`,`status`),
  ADD KEY `idx_users_name_email` (`name`,`email`),
  ADD KEY `idx_users_created_desc` (`created_at` DESC),
  ADD KEY `idx_users_email` (`email`),
  ADD KEY `idx_users_name_search` (`name`);

--
-- Indexes for table `user_messages`
--
ALTER TABLE `user_messages`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_to_user` (`to_user_id`),
  ADD KEY `idx_from_user` (`from_user_id`),
  ADD KEY `idx_show` (`show_id`),
  ADD KEY `idx_parent` (`parent_message_id`),
  ADD KEY `idx_unread` (`to_user_id`,`is_read`),
  ADD KEY `idx_created` (`created_at` DESC),
  ADD KEY `idx_reply_tracking` (`to_user_id`,`allows_reply`,`reply_used`);

--
-- Indexes for table `user_notification_preferences`
--
ALTER TABLE `user_notification_preferences`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `user_id` (`user_id`),
  ADD KEY `idx_user_notifications` (`user_id`);

--
-- Indexes for table `user_push_notifications`
--
ALTER TABLE `user_push_notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_push` (`user_id`),
  ADD KEY `idx_unread_push` (`user_id`,`is_read`),
  ADD KEY `idx_event_push` (`event_id`,`event_type`),
  ADD KEY `idx_notification_center` (`notification_center_id`);

--
-- Indexes for table `user_toast_notifications`
--
ALTER TABLE `user_toast_notifications`
  ADD PRIMARY KEY (`id`),
  ADD KEY `idx_user_toast` (`user_id`),
  ADD KEY `idx_unread_toast` (`user_id`,`is_read`),
  ADD KEY `idx_event_toast` (`event_id`,`event_type`),
  ADD KEY `idx_notification_center` (`notification_center_id`);

--
-- Indexes for table `vehicles`
--
ALTER TABLE `vehicles`
  ADD PRIMARY KEY (`id`),
  ADD KEY `owner_id` (`owner_id`),
  ADD KEY `idx_vehicles_owner_id` (`owner_id`),
  ADD KEY `idx_vehicles_make_model` (`make`,`model`),
  ADD KEY `idx_vehicles_make` (`make`),
  ADD KEY `idx_vehicles_model` (`model`),
  ADD KEY `idx_vehicles_year` (`year`),
  ADD KEY `idx_vehicles_owner_make` (`owner_id`,`make`);

--
-- Indexes for table `vehicle_metric_scores`
--
ALTER TABLE `vehicle_metric_scores`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_vehicle_metric` (`vehicle_total_score_id`,`metric_id`),
  ADD KEY `idx_vehicle_id` (`vehicle_id`),
  ADD KEY `idx_show_id` (`show_id`),
  ADD KEY `idx_metric_id` (`metric_id`);

--
-- Indexes for table `vehicle_total_scores`
--
ALTER TABLE `vehicle_total_scores`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_vehicle_show` (`show_id`,`vehicle_id`),
  ADD KEY `idx_registration` (`registration_id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `admin_email_folders`
--
ALTER TABLE `admin_email_folders`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `admin_impersonation`
--
ALTER TABLE `admin_impersonation`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=83;

--
-- AUTO_INCREMENT for table `age_weights`
--
ALTER TABLE `age_weights`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=105;

--
-- AUTO_INCREMENT for table `awards`
--
ALTER TABLE `awards`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `calendars`
--
ALTER TABLE `calendars`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `calendar_clubs`
--
ALTER TABLE `calendar_clubs`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `calendar_club_members`
--
ALTER TABLE `calendar_club_members`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `calendar_events`
--
ALTER TABLE `calendar_events`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=22;

--
-- AUTO_INCREMENT for table `calendar_event_clubs`
--
ALTER TABLE `calendar_event_clubs`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=47;

--
-- AUTO_INCREMENT for table `calendar_event_images`
--
ALTER TABLE `calendar_event_images`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `calendar_imports`
--
ALTER TABLE `calendar_imports`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `calendar_notifications`
--
ALTER TABLE `calendar_notifications`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `calendar_permissions`
--
ALTER TABLE `calendar_permissions`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `calendar_settings`
--
ALTER TABLE `calendar_settings`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=110;

--
-- AUTO_INCREMENT for table `calendar_venues`
--
ALTER TABLE `calendar_venues`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=25;

--
-- AUTO_INCREMENT for table `camera_banners`
--
ALTER TABLE `camera_banners`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=20;

--
-- AUTO_INCREMENT for table `category_winners`
--
ALTER TABLE `category_winners`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=149;

--
-- AUTO_INCREMENT for table `club_ownership_verifications`
--
ALTER TABLE `club_ownership_verifications`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `coordinator_payment_settings`
--
ALTER TABLE `coordinator_payment_settings`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `custom_field_definitions`
--
ALTER TABLE `custom_field_definitions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `custom_field_values`
--
ALTER TABLE `custom_field_values`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=107;

--
-- AUTO_INCREMENT for table `database_updates`
--
ALTER TABLE `database_updates`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `default_age_weights`
--
ALTER TABLE `default_age_weights`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `default_categories`
--
ALTER TABLE `default_categories`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=15;

--
-- AUTO_INCREMENT for table `default_metrics`
--
ALTER TABLE `default_metrics`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=28;

--
-- AUTO_INCREMENT for table `default_templates`
--
ALTER TABLE `default_templates`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `deletion_requests`
--
ALTER TABLE `deletion_requests`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `email_folders`
--
ALTER TABLE `email_folders`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `email_processing_log`
--
ALTER TABLE `email_processing_log`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1048;

--
-- AUTO_INCREMENT for table `email_reminders`
--
ALTER TABLE `email_reminders`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `email_statistics`
--
ALTER TABLE `email_statistics`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `email_templates`
--
ALTER TABLE `email_templates`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `engagement_notifications`
--
ALTER TABLE `engagement_notifications`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `event_categories`
--
ALTER TABLE `event_categories`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `event_category_mapping`
--
ALTER TABLE `event_category_mapping`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `event_log`
--
ALTER TABLE `event_log`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `event_notification_subscriptions`
--
ALTER TABLE `event_notification_subscriptions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `event_photo_admin_settings`
--
ALTER TABLE `event_photo_admin_settings`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `event_photo_custom_categories`
--
ALTER TABLE `event_photo_custom_categories`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=21;

--
-- AUTO_INCREMENT for table `event_photo_metadata`
--
ALTER TABLE `event_photo_metadata`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT for table `event_photo_settings`
--
ALTER TABLE `event_photo_settings`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=26;

--
-- AUTO_INCREMENT for table `event_tags`
--
ALTER TABLE `event_tags`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `event_tag_mapping`
--
ALTER TABLE `event_tag_mapping`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `fan_votes`
--
ALTER TABLE `fan_votes`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11820;

--
-- AUTO_INCREMENT for table `fcm_tokens`
--
ALTER TABLE `fcm_tokens`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=68;

--
-- AUTO_INCREMENT for table `field_mappings`
--
ALTER TABLE `field_mappings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=71;

--
-- AUTO_INCREMENT for table `field_mappings_backup`
--
ALTER TABLE `field_mappings_backup`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `field_mappings_new`
--
ALTER TABLE `field_mappings_new`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `form_submissions`
--
ALTER TABLE `form_submissions`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `form_templates`
--
ALTER TABLE `form_templates`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=15;

--
-- AUTO_INCREMENT for table `images`
--
ALTER TABLE `images`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=609;

--
-- AUTO_INCREMENT for table `image_edits`
--
ALTER TABLE `image_edits`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `judges`
--
ALTER TABLE `judges`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=103;

--
-- AUTO_INCREMENT for table `judge_assignments`
--
ALTER TABLE `judge_assignments`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- AUTO_INCREMENT for table `judge_categories`
--
ALTER TABLE `judge_categories`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=163;

--
-- AUTO_INCREMENT for table `judge_metric_scores`
--
ALTER TABLE `judge_metric_scores`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=26212;

--
-- AUTO_INCREMENT for table `judge_total_scores`
--
ALTER TABLE `judge_total_scores`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2197;

--
-- AUTO_INCREMENT for table `judging_metrics`
--
ALTER TABLE `judging_metrics`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1248;

--
-- AUTO_INCREMENT for table `layouts`
--
ALTER TABLE `layouts`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `listing_fee`
--
ALTER TABLE `listing_fee`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `messages`
--
ALTER TABLE `messages`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=505;

--
-- AUTO_INCREMENT for table `message_attachments`
--
ALTER TABLE `message_attachments`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `message_deliveries`
--
ALTER TABLE `message_deliveries`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=607;

--
-- AUTO_INCREMENT for table `message_reminders`
--
ALTER TABLE `message_reminders`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `notification_center_items`
--
ALTER TABLE `notification_center_items`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=588;

--
-- AUTO_INCREMENT for table `notification_log`
--
ALTER TABLE `notification_log`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `notification_queue`
--
ALTER TABLE `notification_queue`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=489;

--
-- AUTO_INCREMENT for table `notification_settings`
--
ALTER TABLE `notification_settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=26;

--
-- AUTO_INCREMENT for table `offline_sync_queue`
--
ALTER TABLE `offline_sync_queue`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `password_reset_tokens`
--
ALTER TABLE `password_reset_tokens`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `payments`
--
ALTER TABLE `payments`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=917;

--
-- AUTO_INCREMENT for table `payment_methods`
--
ALTER TABLE `payment_methods`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `payment_settings`
--
ALTER TABLE `payment_settings`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=105;

--
-- AUTO_INCREMENT for table `photo_comments`
--
ALTER TABLE `photo_comments`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `photo_favorites`
--
ALTER TABLE `photo_favorites`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `photo_likes`
--
ALTER TABLE `photo_likes`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `photo_shares`
--
ALTER TABLE `photo_shares`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `photo_user_tags`
--
ALTER TABLE `photo_user_tags`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `pre_approved_cords`
--
ALTER TABLE `pre_approved_cords`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `printable_templates`
--
ALTER TABLE `printable_templates`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=25;

--
-- AUTO_INCREMENT for table `push_subscriptions`
--
ALTER TABLE `push_subscriptions`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=133;

--
-- AUTO_INCREMENT for table `pwa_metrics`
--
ALTER TABLE `pwa_metrics`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `qr_scans`
--
ALTER TABLE `qr_scans`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=45;

--
-- AUTO_INCREMENT for table `registrations`
--
ALTER TABLE `registrations`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1311;

--
-- AUTO_INCREMENT for table `remember_tokens`
--
ALTER TABLE `remember_tokens`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `results`
--
ALTER TABLE `results`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `scheduled_tasks`
--
ALTER TABLE `scheduled_tasks`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `scores`
--
ALTER TABLE `scores`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=158;

--
-- AUTO_INCREMENT for table `scoring_formulas`
--
ALTER TABLE `scoring_formulas`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `settings`
--
ALTER TABLE `settings`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `shows`
--
ALTER TABLE `shows`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=115;

--
-- AUTO_INCREMENT for table `shows_backup`
--
ALTER TABLE `shows_backup`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `shows_backup_v3_35_0`
--
ALTER TABLE `shows_backup_v3_35_0`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=15;

--
-- AUTO_INCREMENT for table `show_categories`
--
ALTER TABLE `show_categories`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=898;

--
-- AUTO_INCREMENT for table `show_payment_methods`
--
ALTER TABLE `show_payment_methods`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=15;

--
-- AUTO_INCREMENT for table `show_payment_settings`
--
ALTER TABLE `show_payment_settings`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `show_role_assignments`
--
ALTER TABLE `show_role_assignments`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `show_role_requests`
--
ALTER TABLE `show_role_requests`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `show_scoring_settings`
--
ALTER TABLE `show_scoring_settings`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `site_settings`
--
ALTER TABLE `site_settings`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `sms_providers`
--
ALTER TABLE `sms_providers`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `staff_assignments`
--
ALTER TABLE `staff_assignments`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `system_fields`
--
ALTER TABLE `system_fields`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=1150;

--
-- AUTO_INCREMENT for table `system_logs`
--
ALTER TABLE `system_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=77818;

--
-- AUTO_INCREMENT for table `system_settings`
--
ALTER TABLE `system_settings`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=155;

--
-- AUTO_INCREMENT for table `task_logs`
--
ALTER TABLE `task_logs`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=110;

--
-- AUTO_INCREMENT for table `ticket_numbers`
--
ALTER TABLE `ticket_numbers`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=14;

--
-- AUTO_INCREMENT for table `updates`
--
ALTER TABLE `updates`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2426;

--
-- AUTO_INCREMENT for table `user_messages`
--
ALTER TABLE `user_messages`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=16;

--
-- AUTO_INCREMENT for table `user_notification_preferences`
--
ALTER TABLE `user_notification_preferences`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `user_push_notifications`
--
ALTER TABLE `user_push_notifications`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=399;

--
-- AUTO_INCREMENT for table `user_toast_notifications`
--
ALTER TABLE `user_toast_notifications`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=226;

--
-- AUTO_INCREMENT for table `vehicles`
--
ALTER TABLE `vehicles`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=225;

--
-- AUTO_INCREMENT for table `vehicle_metric_scores`
--
ALTER TABLE `vehicle_metric_scores`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=161;

--
-- AUTO_INCREMENT for table `vehicle_total_scores`
--
ALTER TABLE `vehicle_total_scores`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `calendars`
--
ALTER TABLE `calendars`
  ADD CONSTRAINT `calendars_ibfk_1` FOREIGN KEY (`owner_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `calendar_club_members`
--
ALTER TABLE `calendar_club_members`
  ADD CONSTRAINT `calendar_club_members_ibfk_1` FOREIGN KEY (`club_id`) REFERENCES `calendar_clubs` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `calendar_club_members_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `calendar_events`
--
ALTER TABLE `calendar_events`
  ADD CONSTRAINT `calendar_events_ibfk_1` FOREIGN KEY (`calendar_id`) REFERENCES `calendars` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `calendar_events_ibfk_2` FOREIGN KEY (`show_id`) REFERENCES `shows` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `calendar_events_ibfk_3` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_calendar_events_main_image` FOREIGN KEY (`main_image_id`) REFERENCES `images` (`id`) ON DELETE SET NULL ON UPDATE CASCADE;

--
-- Constraints for table `calendar_event_clubs`
--
ALTER TABLE `calendar_event_clubs`
  ADD CONSTRAINT `calendar_event_clubs_ibfk_1` FOREIGN KEY (`event_id`) REFERENCES `calendar_events` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `calendar_event_clubs_ibfk_2` FOREIGN KEY (`club_id`) REFERENCES `calendar_clubs` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `calendar_event_images`
--
ALTER TABLE `calendar_event_images`
  ADD CONSTRAINT `fk_event_images_event` FOREIGN KEY (`event_id`) REFERENCES `calendar_events` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_event_images_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `calendar_imports`
--
ALTER TABLE `calendar_imports`
  ADD CONSTRAINT `calendar_imports_ibfk_1` FOREIGN KEY (`calendar_id`) REFERENCES `calendars` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `calendar_imports_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `calendar_notifications`
--
ALTER TABLE `calendar_notifications`
  ADD CONSTRAINT `calendar_notifications_ibfk_1` FOREIGN KEY (`event_id`) REFERENCES `calendar_events` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `calendar_notifications_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `calendar_permissions`
--
ALTER TABLE `calendar_permissions`
  ADD CONSTRAINT `calendar_permissions_ibfk_1` FOREIGN KEY (`calendar_id`) REFERENCES `calendars` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `calendar_permissions_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `club_ownership_verifications`
--
ALTER TABLE `club_ownership_verifications`
  ADD CONSTRAINT `club_ownership_verifications_ibfk_1` FOREIGN KEY (`club_id`) REFERENCES `calendar_clubs` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `club_ownership_verifications_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `club_ownership_verifications_ibfk_3` FOREIGN KEY (`reviewed_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `engagement_notifications`
--
ALTER TABLE `engagement_notifications`
  ADD CONSTRAINT `engagement_notifications_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `engagement_notifications_ibfk_2` FOREIGN KEY (`photo_id`) REFERENCES `images` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `engagement_notifications_ibfk_3` FOREIGN KEY (`from_user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `event_category_mapping`
--
ALTER TABLE `event_category_mapping`
  ADD CONSTRAINT `event_category_mapping_ibfk_1` FOREIGN KEY (`category_id`) REFERENCES `event_categories` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `event_photo_metadata`
--
ALTER TABLE `event_photo_metadata`
  ADD CONSTRAINT `event_photo_metadata_ibfk_1` FOREIGN KEY (`image_id`) REFERENCES `images` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `event_tag_mapping`
--
ALTER TABLE `event_tag_mapping`
  ADD CONSTRAINT `event_tag_mapping_ibfk_1` FOREIGN KEY (`tag_id`) REFERENCES `event_tags` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `fan_votes`
--
ALTER TABLE `fan_votes`
  ADD CONSTRAINT `fan_votes_ibfk_1` FOREIGN KEY (`registration_id`) REFERENCES `registrations` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `judges`
--
ALTER TABLE `judges`
  ADD CONSTRAINT `fk_judges_denied_by` FOREIGN KEY (`denied_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `judges_ibfk_1` FOREIGN KEY (`show_id`) REFERENCES `shows` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `judges_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `judging_metrics`
--
ALTER TABLE `judging_metrics`
  ADD CONSTRAINT `judging_metrics_ibfk_1` FOREIGN KEY (`show_id`) REFERENCES `shows` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `messages`
--
ALTER TABLE `messages`
  ADD CONSTRAINT `messages_ibfk_1` FOREIGN KEY (`from_user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `messages_ibfk_2` FOREIGN KEY (`to_user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `messages_ibfk_3` FOREIGN KEY (`parent_message_id`) REFERENCES `messages` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `message_deliveries`
--
ALTER TABLE `message_deliveries`
  ADD CONSTRAINT `message_deliveries_ibfk_1` FOREIGN KEY (`message_id`) REFERENCES `messages` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `notification_center_items`
--
ALTER TABLE `notification_center_items`
  ADD CONSTRAINT `fk_notification_center_user` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `payments`
--
ALTER TABLE `payments`
  ADD CONSTRAINT `payments_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `payments_ibfk_2` FOREIGN KEY (`payment_method_id`) REFERENCES `payment_methods` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `payments_ibfk_3` FOREIGN KEY (`processed_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `photo_comments`
--
ALTER TABLE `photo_comments`
  ADD CONSTRAINT `photo_comments_ibfk_1` FOREIGN KEY (`photo_id`) REFERENCES `images` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `photo_comments_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `photo_comments_ibfk_3` FOREIGN KEY (`parent_comment_id`) REFERENCES `photo_comments` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `photo_engagement_stats`
--
ALTER TABLE `photo_engagement_stats`
  ADD CONSTRAINT `photo_engagement_stats_ibfk_1` FOREIGN KEY (`photo_id`) REFERENCES `images` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `photo_favorites`
--
ALTER TABLE `photo_favorites`
  ADD CONSTRAINT `photo_favorites_ibfk_1` FOREIGN KEY (`photo_id`) REFERENCES `images` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `photo_favorites_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `photo_likes`
--
ALTER TABLE `photo_likes`
  ADD CONSTRAINT `photo_likes_ibfk_1` FOREIGN KEY (`photo_id`) REFERENCES `images` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `photo_likes_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `photo_shares`
--
ALTER TABLE `photo_shares`
  ADD CONSTRAINT `photo_shares_ibfk_1` FOREIGN KEY (`photo_id`) REFERENCES `images` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `photo_shares_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `photo_user_tags`
--
ALTER TABLE `photo_user_tags`
  ADD CONSTRAINT `photo_user_tags_ibfk_1` FOREIGN KEY (`photo_id`) REFERENCES `images` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `photo_user_tags_ibfk_2` FOREIGN KEY (`tagged_user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `photo_user_tags_ibfk_3` FOREIGN KEY (`tagged_by_user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `pre_approved_cords`
--
ALTER TABLE `pre_approved_cords`
  ADD CONSTRAINT `fk_pre_approved_cords_coordinator` FOREIGN KEY (`coordinator_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `registrations`
--
ALTER TABLE `registrations`
  ADD CONSTRAINT `registrations_ibfk_1` FOREIGN KEY (`show_id`) REFERENCES `shows` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `registrations_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `registrations_ibfk_3` FOREIGN KEY (`vehicle_id`) REFERENCES `vehicles` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `registrations_ibfk_4` FOREIGN KEY (`category_id`) REFERENCES `show_categories` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `scores`
--
ALTER TABLE `scores`
  ADD CONSTRAINT `scores_ibfk_1` FOREIGN KEY (`registration_id`) REFERENCES `registrations` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `scores_ibfk_2` FOREIGN KEY (`judge_id`) REFERENCES `judges` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `scores_ibfk_3` FOREIGN KEY (`metric_id`) REFERENCES `judging_metrics` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `shows`
--
ALTER TABLE `shows`
  ADD CONSTRAINT `fk_shows_featured_image` FOREIGN KEY (`featured_image_id`) REFERENCES `images` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `shows_ibfk_1` FOREIGN KEY (`coordinator_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `show_categories`
--
ALTER TABLE `show_categories`
  ADD CONSTRAINT `show_categories_ibfk_1` FOREIGN KEY (`show_id`) REFERENCES `shows` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `show_payment_methods`
--
ALTER TABLE `show_payment_methods`
  ADD CONSTRAINT `show_payment_methods_ibfk_1` FOREIGN KEY (`show_id`) REFERENCES `shows` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `show_payment_methods_ibfk_2` FOREIGN KEY (`payment_method_id`) REFERENCES `payment_methods` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `show_payment_settings`
--
ALTER TABLE `show_payment_settings`
  ADD CONSTRAINT `show_payment_settings_ibfk_1` FOREIGN KEY (`show_id`) REFERENCES `shows` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `show_role_requests`
--
ALTER TABLE `show_role_requests`
  ADD CONSTRAINT `fk_show_role_requests_denied_by` FOREIGN KEY (`denied_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `user_messages`
--
ALTER TABLE `user_messages`
  ADD CONSTRAINT `fk_user_messages_from_user` FOREIGN KEY (`from_user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `fk_user_messages_parent` FOREIGN KEY (`parent_message_id`) REFERENCES `user_messages` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `fk_user_messages_to_user` FOREIGN KEY (`to_user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `vehicles`
--
ALTER TABLE `vehicles`
  ADD CONSTRAINT `vehicles_ibfk_1` FOREIGN KEY (`owner_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;
