
-- --------------------------------------------------------

--
-- Table structure for table `shows`
--
-- Creation: Jul 10, 2025 at 01:46 PM
--

CREATE TABLE `shows` (
  `id` int(10) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `location` varchar(255) NOT NULL,
  `start_date` datetime NOT NULL,
  `end_date` datetime NOT NULL,
  `registration_start` datetime NOT NULL,
  `registration_end` datetime NOT NULL,
  `coordinator_id` int(10) UNSIGNED DEFAULT NULL,
  `status` varchar(50) NOT NULL DEFAULT 'draft',
  `fan_voting_enabled` tinyint(1) DEFAULT NULL,
  `banner_image` varchar(255) DEFAULT NULL,
  `form_template_id` int(11) DEFAULT NULL,
  `registration_fee` decimal(10,2) NOT NULL DEFAULT 0.00,
  `is_free` tinyint(1) NOT NULL DEFAULT 0,
  `listing_fee` decimal(10,2) NOT NULL DEFAULT 0.00,
  `listing_paid` tinyint(1) NOT NULL DEFAULT 0,
  `featured_image_id` int(10) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `template_id` int(11) DEFAULT NULL,
  `lat` decimal(10,8) DEFAULT NULL,
  `lng` decimal(11,8) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- RELATIONSHIPS FOR TABLE `shows`:
--   `featured_image_id`
--       `images` -> `id`
--   `coordinator_id`
--       `users` -> `id`
--

--
-- Dumping data for table `shows`
--

INSERT INTO `shows` (`id`, `name`, `description`, `location`, `start_date`, `end_date`, `registration_start`, `registration_end`, `coordinator_id`, `status`, `fan_voting_enabled`, `banner_image`, `form_template_id`, `registration_fee`, `is_free`, `listing_fee`, `listing_paid`, `featured_image_id`, `created_at`, `updated_at`, `template_id`, `lat`, `lng`) VALUES
(5, 'Name of show', 'This is a description of a carRoll up to the biggest auto bash of the year and feast your eyes on everything from classic roadsters to souped-up tuners.  We&amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;#039;ve got chrome, curves, and horsepower galore, with something to rev everyone&amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;#039;s engine.  Check out the off-road rigs tearing up the dirt track and admire the sleek lines of vintage sports cars. Bikes, trucks, and even custom creations will be on display, so don&amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;amp;#039;t miss out!', 'Heroes Car Wash', '2025-07-09 08:00:00', '2025-07-09 11:00:00', '2025-07-01 20:00:00', '2025-07-08 19:59:00', 5, 'published', 1, 'show_5_683da8cea4c43.jpg', NULL, 0.00, 1, 20.00, 1, 21, '2025-06-01 11:01:03', '2025-07-20 11:53:49', NULL, 35.65435880, -80.45650910),
(9, 'Summer Classic Auto Show 2025', 'Welcome to the Summer Classic Auto Show 2025! This premier automotive event showcases the finest classic, custom, and modern vehicles from across the region. From meticulously restored vintage classics to cutting-edge custom builds, this show offers something for every automotive enthusiast. Enjoy live music, food vendors, and special guest appearances throughout the weekend. Bring the whole family for a day filled with automotive excellence and community spirit. All proceeds benefit local youth automotive education programs..', 'Rowan Sheriff Dept', '2025-07-07 14:00:00', '2025-07-08 03:59:00', '2025-05-09 04:00:00', '2025-07-03 17:59:00', 5, 'published', 1, 'show_9_684eb050ea831.png', NULL, 20.00, 0, 0.00, 0, 131, '2025-06-08 13:38:32', '2025-07-07 19:49:43', NULL, 35.66899160, -80.46855890),
(14, 'gfdgdfgdf', 'gdfgfdgdf', 'Heroes Car Wash', '2025-06-22 00:00:00', '2025-06-22 11:59:00', '2025-06-12 12:00:00', '2025-06-18 11:59:00', NULL, 'published', 1, NULL, NULL, 25.00, 0, 20.00, 1, NULL, '2025-06-12 23:47:46', '2025-06-30 18:44:24', NULL, 35.65435880, -80.45650910);
