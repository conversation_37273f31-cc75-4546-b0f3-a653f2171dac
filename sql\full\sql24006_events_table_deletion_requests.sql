
-- --------------------------------------------------------

--
-- Table structure for table `deletion_requests`
--
-- Creation: Jun 23, 2025 at 08:30 PM
--

CREATE TABLE `deletion_requests` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `facebook_id` varchar(100) DEFAULT NULL,
  `deletion_type` enum('all','facebook','specific') NOT NULL DEFAULT 'all',
  `deletion_reason` varchar(255) DEFAULT NULL,
  `specific_data` text DEFAULT NULL,
  `confirmation_token` varchar(64) DEFAULT NULL,
  `status` enum('pending','completed','failed') NOT NULL DEFAULT 'pending',
  `notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `completed_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- RELATIONSHIPS FOR TABLE `deletion_requests`:
--
