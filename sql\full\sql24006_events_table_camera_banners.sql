
-- --------------------------------------------------------

--
-- Table structure for table `camera_banners`
--
-- Creation: Jul 03, 2025 at 08:40 PM
--

CREATE TABLE `camera_banners` (
  `id` int(11) NOT NULL,
  `type` enum('text','image') NOT NULL DEFAULT 'text',
  `text` text DEFAULT NULL,
  `image_path` varchar(500) DEFAULT NULL,
  `alt_text` varchar(255) DEFAULT NULL,
  `active` tinyint(1) DEFAULT 1,
  `sort_order` int(11) DEFAULT 0,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_uca1400_ai_ci;

--
-- RELATIONSHIPS FOR TABLE `camera_banners`:
--

--
-- Dumping data for table `camera_banners`
--

INSERT INTO `camera_banners` (`id`, `type`, `text`, `image_path`, `alt_text`, `active`, `sort_order`, `created_at`, `updated_at`) VALUES
(16, 'text', 'Banner 1: Welcome to our Event Platform!', NULL, NULL, 1, 1, '2025-07-06 18:58:49', '2025-07-06 18:58:49'),
(17, 'text', 'Banner 2: Check out our upcoming events!', NULL, NULL, 1, 2, '2025-07-06 18:58:49', '2025-07-06 18:58:49'),
(18, 'text', 'Banner 3: Register your vehicle today!', NULL, NULL, 1, 3, '2025-07-06 18:58:49', '2025-07-06 18:58:49'),
(19, 'image', '', 'banner_686be8da634e6.png', 'Convoy', 1, 3, '2025-07-07 15:33:46', '2025-07-07 15:33:46');
