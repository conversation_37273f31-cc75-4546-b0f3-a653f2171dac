
-- --------------------------------------------------------

--
-- Table structure for table `calendar_settings`
--
-- Creation: Jun 23, 2025 at 08:30 PM
--

CREATE TABLE `calendar_settings` (
  `id` int(10) UNSIGNED NOT NULL,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `setting_group` varchar(50) DEFAULT 'general',
  `description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- RELATIONSHIPS FOR TABLE `calendar_settings`:
--

--
-- Dumping data for table `calendar_settings`
--

INSERT INTO `calendar_settings` (`id`, `setting_key`, `setting_value`, `setting_group`, `description`, `created_at`, `updated_at`) VALUES
(1, 'default_view', 'month', 'general', NULL, '2025-06-16 18:06:32', '2025-06-16 18:06:32'),
(2, 'business_hours_start', '09:00:00', 'general', NULL, '2025-06-16 18:06:32', '2025-06-16 18:06:32'),
(3, 'business_hours_end', '17:00:00', 'general', NULL, '2025-06-16 18:06:32', '2025-06-16 18:06:32'),
(4, 'week_starts_on', '0', 'general', NULL, '2025-06-16 18:06:32', '2025-06-16 18:06:32'),
(5, 'time_format', '12', 'general', NULL, '2025-06-16 18:06:32', '2025-06-16 18:06:32'),
(6, 'date_format', 'MM/DD/YYYY', 'general', NULL, '2025-06-16 18:06:32', '2025-06-16 18:06:32'),
(7, 'default_event_duration', '60', 'general', NULL, '2025-06-16 18:06:32', '2025-06-16 18:06:32'),
(8, 'enable_drag_drop', '1', 'general', NULL, '2025-06-16 18:06:32', '2025-06-16 18:06:32'),
(9, 'enable_resize', '1', 'general', NULL, '2025-06-16 18:06:32', '2025-06-16 18:06:32'),
(10, 'show_weekends', '1', 'general', NULL, '2025-06-16 18:06:32', '2025-06-16 18:06:32'),
(11, 'default_calendar_color', '#3788d8', 'general', NULL, '2025-06-16 18:06:32', '2025-06-16 18:06:32'),
(12, 'notification_default_time', '60', 'general', NULL, '2025-06-16 18:06:32', '2025-06-16 18:06:32'),
(13, 'map_default_zoom', '4', 'general', NULL, '2025-06-17 14:09:49', '2025-06-17 14:09:49'),
(14, 'map_default_lat', '39.8283', 'general', NULL, '2025-06-17 14:09:49', '2025-06-17 14:09:49'),
(15, 'map_default_lng', '-98.5795', 'general', NULL, '2025-06-17 14:09:49', '2025-06-17 14:09:49'),
(16, 'map_filter_radius', '100', 'general', NULL, '2025-06-17 14:09:49', '2025-06-17 14:09:49'),
(17, 'map_provider', 'google', 'general', NULL, '2025-06-17 15:45:44', '2025-06-25 16:13:42'),
(18, 'map_api_key', 'AIzaSyDF0-ALvVKxRBa1djK0zl3Rh4WOgemUe2k', 'general', NULL, '2025-06-17 15:45:44', '2025-06-25 17:04:46'),
(19, 'map_tile_url', 'https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', 'general', NULL, '2025-06-17 15:45:44', '2025-06-17 15:45:44'),
(20, 'map_attribution', '&copy; &lt;a href=&quot;https://www.openstreetmap.org/copyright&quot;&gt;OpenStreetMap&lt;/a&gt; contributors', 'general', NULL, '2025-06-17 15:45:44', '2025-07-20 23:04:44'),
(21, 'calendar_default_view', 'month', 'general', NULL, '2025-06-17 16:05:00', '2025-06-17 16:05:00'),
(22, 'calendar_start_day', '0', 'general', NULL, '2025-06-17 16:05:00', '2025-06-17 16:05:00'),
(23, 'calendar_time_format', '12', 'general', NULL, '2025-06-17 16:05:00', '2025-06-17 16:05:00'),
(24, 'calendar_date_format', 'MM/DD/YYYY', 'general', NULL, '2025-06-17 16:05:00', '2025-06-17 16:05:00'),
(25, 'calendar_events_per_page', '10', 'general', NULL, '2025-06-17 16:05:00', '2025-06-17 16:05:00'),
(26, 'marker_type', 'pin', 'general', NULL, '2025-06-17 23:52:04', '2025-06-18 00:19:11'),
(27, 'marker_size', '16', 'general', NULL, '2025-06-17 23:52:04', '2025-06-17 23:52:04'),
(28, 'marker_border', 'true', 'general', NULL, '2025-06-17 23:52:04', '2025-06-17 23:52:04'),
(29, 'marker_border_color', '#d4d3d9', 'general', NULL, '2025-06-17 23:52:04', '2025-07-20 23:05:23'),
(30, 'marker_border_width', '2', 'general', NULL, '2025-06-17 23:52:04', '2025-06-17 23:52:04'),
(31, 'custom_marker_url', '', 'general', NULL, '2025-06-17 23:52:04', '2025-06-17 23:52:04'),
(32, 'use_calendar_colors', 'true', 'general', NULL, '2025-06-17 23:52:04', '2025-06-17 23:52:04'),
(33, 'default_marker_color', '#3788d8', 'general', NULL, '2025-06-17 23:52:04', '2025-06-17 23:52:04'),
(34, 'gantt_show_weekends', '1', 'general', NULL, '2025-06-20 11:47:35', '2025-06-20 14:07:59'),
(35, 'gantt_show_today_line', '0', 'general', NULL, '2025-06-20 11:47:35', '2025-06-20 14:07:59'),
(36, 'gantt_enable_drag_drop', '1', 'general', NULL, '2025-06-20 11:47:35', '2025-06-20 11:47:35'),
(37, 'gantt_show_event_hover', '1', 'general', NULL, '2025-06-20 11:47:35', '2025-06-20 12:15:44'),
(38, 'gantt_mobile_breakpoint', '576', 'general', NULL, '2025-06-20 11:48:46', '2025-06-20 11:48:46'),
(39, 'event_max_images', '2', 'event_images', 'Maximum number of images allowed per event', '2025-06-21 18:52:51', '2025-06-21 18:52:51'),
(40, 'event_max_image_size', '2', 'event_images', 'Maximum image file size in MB', '2025-06-21 18:52:51', '2025-06-21 18:52:51'),
(41, 'event_allowed_image_types', 'image/jpeg,image/jpg,image/png,image/gif', 'event_images', 'Allowed image file types (comma-separated MIME types)', '2025-06-21 18:52:51', '2025-06-21 18:52:51'),
(42, 'event_enable_wysiwyg', '1', 'event_images', 'Enable WYSIWYG editor for event descriptions', '2025-06-21 18:52:51', '2025-06-21 18:52:51'),
(43, 'event_social_sharing_images', '1', 'event_images', 'Enable images in social sharing previews', '2025-06-21 18:52:51', '2025-06-21 18:52:51'),
(44, 'event_show_weekends', '1', 'general', NULL, '2025-06-25 14:46:59', '2025-06-25 14:46:59'),
(45, 'event_enable_drag_drop', '0', 'general', NULL, '2025-06-25 14:46:59', '2025-06-25 14:46:59'),
(46, 'event_show_today_line', '1', 'general', NULL, '2025-06-25 14:46:59', '2025-06-25 14:46:59'),
(47, 'event_show_event_hover', '1', 'general', NULL, '2025-06-25 14:46:59', '2025-06-25 14:46:59'),
(48, 'event_mobile_breakpoint', '992', 'general', NULL, '2025-06-25 14:46:59', '2025-06-25 14:46:59'),
(99, 'map_server_api_key', 'AIzaSyCY6gXgxodxzHxtnqIuL0S9d9x-gPxbW4A', 'general', NULL, '2025-06-26 00:34:18', '2025-06-26 00:34:18');
