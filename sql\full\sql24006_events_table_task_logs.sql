
-- --------------------------------------------------------

--
-- Table structure for table `task_logs`
--
-- Creation: Jun 23, 2025 at 08:30 PM
--

CREATE TABLE `task_logs` (
  `id` int(11) NOT NULL,
  `task_id` int(11) NOT NULL,
  `message` text NOT NULL,
  `is_error` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- RELATIONSHIPS FOR TABLE `task_logs`:
--

--
-- Dumping data for table `task_logs`
--

INSERT INTO `task_logs` (`id`, `task_id`, `message`, `is_error`, `created_at`) VALUES
(1, 2, 'Running task: Calculate scores on Show Completion (ID: 2)', 0, '2025-06-09 13:43:52'),
(2, 2, 'Using default parameters: --show_id={event_parameter}', 0, '2025-06-09 13:43:52'),
(3, 2, 'Executing command: php \'/home/<USER>/events.rowaneliterides.com/scripts/run_all_scoring.php\' --show_id={event_parameter}', 0, '2025-06-09 13:43:52'),
(4, 2, 'Output: 2025-06-09 13:43:52 - Command line arguments: [\"\\/home\\/<USER>\\/events.rowaneliterides.com\\/scripts\\/run_all_scoring.php\",\"--show_id={event_parameter}\"]', 0, '2025-06-09 13:43:52'),
(5, 2, 'Output: 2025-06-09 13:43:52 - Parsed options: {\"show_id\":\"{event_parameter}\"}', 0, '2025-06-09 13:43:52'),
(6, 2, 'Output: 2025-06-09 13:43:52 - Show ID: 0', 0, '2025-06-09 13:43:52'),
(7, 2, 'Output: 2025-06-09 13:43:52 - Starting scoring calculations for 2 show(s)', 0, '2025-06-09 13:43:52'),
(8, 2, 'Output: 2025-06-09 13:43:52 - Processing show: Name of show (ID: 5)', 0, '2025-06-09 13:43:52'),
(9, 2, 'Output: 2025-06-09 13:43:52 - Step 1: Generating judge scores...', 0, '2025-06-09 13:43:52'),
(10, 2, 'Output: 2025-06-09 13:43:52 - Executing command: php \'/home/<USER>/events.rowaneliterides.com/scripts/generate_judge_scores.php\' \'--show_id=5\'', 0, '2025-06-09 13:43:52'),
(11, 2, 'Output: 2025-06-09 13:43:52 - Judge scores generated successfully', 0, '2025-06-09 13:43:52'),
(12, 2, 'Output: 2025-06-09 13:43:52 - Step 2: Generating vehicle scores...', 0, '2025-06-09 13:43:52'),
(13, 2, 'Output: 2025-06-09 13:43:52 - Executing command: php \'/home/<USER>/events.rowaneliterides.com/scripts/generate_vehicle_scores.php\' \'--show_id=5\'', 0, '2025-06-09 13:43:52'),
(14, 2, 'Output: 2025-06-09 13:43:52 - Vehicle scores generated successfully', 0, '2025-06-09 13:43:52'),
(15, 2, 'Output: 2025-06-09 13:43:52 - Step 3: Calculating category winners...', 0, '2025-06-09 13:43:52'),
(16, 2, 'Output: 2025-06-09 13:43:52 - Executing command: php \'/home/<USER>/events.rowaneliterides.com/scripts/calculate_show_winners.php\' \'--show_id=5\'', 0, '2025-06-09 13:43:52'),
(17, 2, 'Output: 2025-06-09 13:43:52 - Category winners calculated successfully', 0, '2025-06-09 13:43:52'),
(18, 2, 'Output: 2025-06-09 13:43:52 - All scoring calculations completed successfully for show: Name of show', 0, '2025-06-09 13:43:52'),
(19, 2, 'Output: 2025-06-09 13:43:52 - Processing show: Summer Classic Auto Show 2025 (ID: 9)', 0, '2025-06-09 13:43:52'),
(20, 2, 'Output: 2025-06-09 13:43:52 - Step 1: Generating judge scores...', 0, '2025-06-09 13:43:52'),
(21, 2, 'Output: 2025-06-09 13:43:52 - Executing command: php \'/home/<USER>/events.rowaneliterides.com/scripts/generate_judge_scores.php\' \'--show_id=9\'', 0, '2025-06-09 13:43:52'),
(22, 2, 'Output: 2025-06-09 13:43:52 - Judge scores generated successfully', 0, '2025-06-09 13:43:52'),
(23, 2, 'Output: 2025-06-09 13:43:52 - Step 2: Generating vehicle scores...', 0, '2025-06-09 13:43:52'),
(24, 2, 'Output: 2025-06-09 13:43:52 - Executing command: php \'/home/<USER>/events.rowaneliterides.com/scripts/generate_vehicle_scores.php\' \'--show_id=9\'', 0, '2025-06-09 13:43:52'),
(25, 2, 'Output: 2025-06-09 13:43:52 - Vehicle scores generated successfully', 0, '2025-06-09 13:43:52'),
(26, 2, 'Output: 2025-06-09 13:43:52 - Step 3: Calculating category winners...', 0, '2025-06-09 13:43:52'),
(27, 2, 'Output: 2025-06-09 13:43:52 - Executing command: php \'/home/<USER>/events.rowaneliterides.com/scripts/calculate_show_winners.php\' \'--show_id=9\'', 0, '2025-06-09 13:43:52'),
(28, 2, 'Output: 2025-06-09 13:43:52 - Category winners calculated successfully', 0, '2025-06-09 13:43:52'),
(29, 2, 'Output: 2025-06-09 13:43:52 - All scoring calculations completed successfully for show: Summer Classic Auto Show 2025', 0, '2025-06-09 13:43:52'),
(30, 2, 'Output: 2025-06-09 13:43:52 - All scoring calculations completed successfully', 0, '2025-06-09 13:43:52'),
(31, 2, 'Task completed successfully', 0, '2025-06-09 13:43:52'),
(32, 2, 'Using task ID from command line: 2', 0, '2025-06-09 16:51:05'),
(33, 2, 'Running task: Calculate scores on Show Completion (ID: 2)', 0, '2025-06-09 16:51:05'),
(34, 2, 'Using dynamic parameters: 9', 0, '2025-06-09 16:51:05'),
(35, 2, 'Executing command: php \'/home/<USER>/events.rowaneliterides.com/scripts/run_all_scoring.php\' 9', 0, '2025-06-09 16:51:05'),
(36, 2, 'Using custom timeout of 900 seconds for this task', 0, '2025-06-09 16:51:05'),
(37, 2, 'Starting execution with timeout of 900 seconds', 0, '2025-06-09 16:51:05'),
(38, 2, 'Execution completed in 0 seconds', 0, '2025-06-09 16:51:05'),
(39, 2, 'Output: 2025-06-09 16:51:05 - Command line arguments: [\"\\/home\\/<USER>\\/events.rowaneliterides.com\\/scripts\\/run_all_scoring.php\",\"9\"]', 0, '2025-06-09 16:51:05'),
(40, 2, 'Output: 2025-06-09 16:51:05 - Parsed options: []', 0, '2025-06-09 16:51:05'),
(41, 2, 'Output: 2025-06-09 16:51:05 - Show ID: 9', 0, '2025-06-09 16:51:05'),
(42, 2, 'Output: 2025-06-09 16:51:05 - Starting scoring calculations for 1 show(s)', 0, '2025-06-09 16:51:05'),
(43, 2, 'Output: 2025-06-09 16:51:05 - Processing show: Summer Classic Auto Show 2025 (ID: 9)', 0, '2025-06-09 16:51:05'),
(44, 2, 'Output: 2025-06-09 16:51:05 - Step 1: Generating judge scores...', 0, '2025-06-09 16:51:05'),
(45, 2, 'Output: 2025-06-09 16:51:05 - Executing command: php \'/home/<USER>/events.rowaneliterides.com/scripts/generate_judge_scores.php\' \'--show_id=9\'', 0, '2025-06-09 16:51:05'),
(46, 2, 'Output: 2025-06-09 16:51:05 - Judge scores generated successfully', 0, '2025-06-09 16:51:05'),
(47, 2, 'Output: 2025-06-09 16:51:05 - Step 2: Generating vehicle scores...', 0, '2025-06-09 16:51:05'),
(48, 2, 'Output: 2025-06-09 16:51:05 - Executing command: php \'/home/<USER>/events.rowaneliterides.com/scripts/generate_vehicle_scores.php\' \'--show_id=9\'', 0, '2025-06-09 16:51:05'),
(49, 2, 'Output: 2025-06-09 16:51:05 - Vehicle scores generated successfully', 0, '2025-06-09 16:51:05'),
(50, 2, 'Output: 2025-06-09 16:51:05 - Step 3: Calculating category winners...', 0, '2025-06-09 16:51:05'),
(51, 2, 'Output: 2025-06-09 16:51:05 - Executing command: php \'/home/<USER>/events.rowaneliterides.com/scripts/calculate_show_winners.php\' \'--show_id=9\'', 0, '2025-06-09 16:51:05'),
(52, 2, 'Output: 2025-06-09 16:51:05 - Category winners calculated successfully', 0, '2025-06-09 16:51:05'),
(53, 2, 'Output: 2025-06-09 16:51:05 - All scoring calculations completed successfully for show: Summer Classic Auto Show 2025', 0, '2025-06-09 16:51:05'),
(54, 2, 'Output: 2025-06-09 16:51:05 - All scoring calculations completed successfully', 0, '2025-06-09 16:51:05'),
(55, 2, 'Task completed successfully', 0, '2025-06-09 16:51:05'),
(56, 2, 'Task status updated successfully. Task ID: 2, Next run: NULL', 0, '2025-06-09 16:51:05'),
(57, 2, 'Verified task update. Current status: active, Last run: 2025-06-09 12:51:05', 0, '2025-06-09 16:51:05'),
(58, 2, 'Using task ID from command line: 2', 0, '2025-06-09 16:51:12'),
(59, 2, 'Running task: Calculate scores on Show Completion (ID: 2)', 0, '2025-06-09 16:51:12'),
(60, 2, 'Using dynamic parameters: 9', 0, '2025-06-09 16:51:12'),
(61, 2, 'Executing command: php \'/home/<USER>/events.rowaneliterides.com/scripts/run_all_scoring.php\' 9', 0, '2025-06-09 16:51:12'),
(62, 2, 'Using custom timeout of 900 seconds for this task', 0, '2025-06-09 16:51:12'),
(63, 2, 'Starting execution with timeout of 900 seconds', 0, '2025-06-09 16:51:12'),
(64, 2, 'Execution completed in 0 seconds', 0, '2025-06-09 16:51:12'),
(65, 2, 'Output: 2025-06-09 16:51:12 - Command line arguments: [\"\\/home\\/<USER>\\/events.rowaneliterides.com\\/scripts\\/run_all_scoring.php\",\"9\"]', 0, '2025-06-09 16:51:12'),
(66, 2, 'Output: 2025-06-09 16:51:12 - Parsed options: []', 0, '2025-06-09 16:51:12'),
(67, 2, 'Output: 2025-06-09 16:51:12 - Show ID: 9', 0, '2025-06-09 16:51:12'),
(68, 2, 'Output: 2025-06-09 16:51:12 - Starting scoring calculations for 1 show(s)', 0, '2025-06-09 16:51:12'),
(69, 2, 'Output: 2025-06-09 16:51:12 - Processing show: Summer Classic Auto Show 2025 (ID: 9)', 0, '2025-06-09 16:51:12'),
(70, 2, 'Output: 2025-06-09 16:51:12 - Step 1: Generating judge scores...', 0, '2025-06-09 16:51:12'),
(71, 2, 'Output: 2025-06-09 16:51:12 - Executing command: php \'/home/<USER>/events.rowaneliterides.com/scripts/generate_judge_scores.php\' \'--show_id=9\'', 0, '2025-06-09 16:51:12'),
(72, 2, 'Output: 2025-06-09 16:51:12 - Judge scores generated successfully', 0, '2025-06-09 16:51:12'),
(73, 2, 'Output: 2025-06-09 16:51:12 - Step 2: Generating vehicle scores...', 0, '2025-06-09 16:51:12'),
(74, 2, 'Output: 2025-06-09 16:51:12 - Executing command: php \'/home/<USER>/events.rowaneliterides.com/scripts/generate_vehicle_scores.php\' \'--show_id=9\'', 0, '2025-06-09 16:51:12'),
(75, 2, 'Output: 2025-06-09 16:51:12 - Vehicle scores generated successfully', 0, '2025-06-09 16:51:12'),
(76, 2, 'Output: 2025-06-09 16:51:12 - Step 3: Calculating category winners...', 0, '2025-06-09 16:51:12'),
(77, 2, 'Output: 2025-06-09 16:51:12 - Executing command: php \'/home/<USER>/events.rowaneliterides.com/scripts/calculate_show_winners.php\' \'--show_id=9\'', 0, '2025-06-09 16:51:12'),
(78, 2, 'Output: 2025-06-09 16:51:12 - Category winners calculated successfully', 0, '2025-06-09 16:51:12'),
(79, 2, 'Output: 2025-06-09 16:51:12 - All scoring calculations completed successfully for show: Summer Classic Auto Show 2025', 0, '2025-06-09 16:51:12'),
(80, 2, 'Output: 2025-06-09 16:51:12 - All scoring calculations completed successfully', 0, '2025-06-09 16:51:12'),
(81, 2, 'Task completed successfully', 0, '2025-06-09 16:51:12'),
(82, 2, 'Task status updated successfully. Task ID: 2, Next run: NULL', 0, '2025-06-09 16:51:12'),
(83, 2, 'Verified task update. Current status: active, Last run: 2025-06-09 12:51:12', 0, '2025-06-09 16:51:12'),
(84, 2, 'Using task ID from command line: 2', 0, '2025-06-09 16:51:17'),
(85, 2, 'Running task: Calculate scores on Show Completion (ID: 2)', 0, '2025-06-09 16:51:17'),
(86, 2, 'Using dynamic parameters: 9', 0, '2025-06-09 16:51:17'),
(87, 2, 'Executing command: php \'/home/<USER>/events.rowaneliterides.com/scripts/run_all_scoring.php\' 9', 0, '2025-06-09 16:51:17'),
(88, 2, 'Using custom timeout of 900 seconds for this task', 0, '2025-06-09 16:51:17'),
(89, 2, 'Starting execution with timeout of 900 seconds', 0, '2025-06-09 16:51:17'),
(90, 2, 'Execution completed in 0 seconds', 0, '2025-06-09 16:51:17'),
(91, 2, 'Output: 2025-06-09 16:51:17 - Command line arguments: [\"\\/home\\/<USER>\\/events.rowaneliterides.com\\/scripts\\/run_all_scoring.php\",\"9\"]', 0, '2025-06-09 16:51:17'),
(92, 2, 'Output: 2025-06-09 16:51:17 - Parsed options: []', 0, '2025-06-09 16:51:17'),
(93, 2, 'Output: 2025-06-09 16:51:17 - Show ID: 9', 0, '2025-06-09 16:51:17'),
(94, 2, 'Output: 2025-06-09 16:51:17 - Starting scoring calculations for 1 show(s)', 0, '2025-06-09 16:51:17'),
(95, 2, 'Output: 2025-06-09 16:51:17 - Processing show: Summer Classic Auto Show 2025 (ID: 9)', 0, '2025-06-09 16:51:17'),
(96, 2, 'Output: 2025-06-09 16:51:17 - Step 1: Generating judge scores...', 0, '2025-06-09 16:51:17'),
(97, 2, 'Output: 2025-06-09 16:51:17 - Executing command: php \'/home/<USER>/events.rowaneliterides.com/scripts/generate_judge_scores.php\' \'--show_id=9\'', 0, '2025-06-09 16:51:17'),
(98, 2, 'Output: 2025-06-09 16:51:17 - Judge scores generated successfully', 0, '2025-06-09 16:51:17'),
(99, 2, 'Output: 2025-06-09 16:51:17 - Step 2: Generating vehicle scores...', 0, '2025-06-09 16:51:17'),
(100, 2, 'Output: 2025-06-09 16:51:17 - Executing command: php \'/home/<USER>/events.rowaneliterides.com/scripts/generate_vehicle_scores.php\' \'--show_id=9\'', 0, '2025-06-09 16:51:17'),
(101, 2, 'Output: 2025-06-09 16:51:17 - Vehicle scores generated successfully', 0, '2025-06-09 16:51:17'),
(102, 2, 'Output: 2025-06-09 16:51:17 - Step 3: Calculating category winners...', 0, '2025-06-09 16:51:17'),
(103, 2, 'Output: 2025-06-09 16:51:17 - Executing command: php \'/home/<USER>/events.rowaneliterides.com/scripts/calculate_show_winners.php\' \'--show_id=9\'', 0, '2025-06-09 16:51:17'),
(104, 2, 'Output: 2025-06-09 16:51:17 - Category winners calculated successfully', 0, '2025-06-09 16:51:17'),
(105, 2, 'Output: 2025-06-09 16:51:17 - All scoring calculations completed successfully for show: Summer Classic Auto Show 2025', 0, '2025-06-09 16:51:17'),
(106, 2, 'Output: 2025-06-09 16:51:17 - All scoring calculations completed successfully', 0, '2025-06-09 16:51:17'),
(107, 2, 'Task completed successfully', 0, '2025-06-09 16:51:17'),
(108, 2, 'Task status updated successfully. Task ID: 2, Next run: NULL', 0, '2025-06-09 16:51:17'),
(109, 2, 'Verified task update. Current status: active, Last run: 2025-06-09 12:51:17', 0, '2025-06-09 16:51:17');
