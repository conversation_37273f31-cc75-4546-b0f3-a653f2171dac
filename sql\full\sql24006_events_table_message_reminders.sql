
-- --------------------------------------------------------

--
-- Table structure for table `message_reminders`
--
-- Creation: Jul 16, 2025 at 07:00 PM
--

CREATE TABLE `message_reminders` (
  `id` int(11) NOT NULL,
  `message_id` int(11) NOT NULL,
  `admin_user_id` int(11) NOT NULL COMMENT 'Admin who set the reminder',
  `reminder_time` datetime NOT NULL COMMENT 'When to send the reminder',
  `note` text DEFAULT NULL COMMENT 'Admin note for the reminder',
  `is_sent` tinyint(1) DEFAULT 0 COMMENT 'Whether reminder has been sent',
  `sent_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- RELATIONSHIPS FOR TABLE `message_reminders`:
--
