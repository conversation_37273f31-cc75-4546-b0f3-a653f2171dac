
-- --------------------------------------------------------

--
-- Table structure for table `default_metrics`
--
-- Creation: Jun 23, 2025 at 08:30 PM
--

CREATE TABLE `default_metrics` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `weight` int(11) DEFAULT 1,
  `max_score` int(11) DEFAULT 10,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- RELATIONSHIPS FOR TABLE `default_metrics`:
--

--
-- Dumping data for table `default_metrics`
--

INSERT INTO `default_metrics` (`id`, `name`, `description`, `weight`, `max_score`, `created_at`, `updated_at`) VALUES
(11, 'Originality', 'Adherence to factory specifications and period correctness', 2, 10, '2025-05-27 19:08:23', '2025-05-27 19:08:23'),
(12, 'Craftsmanship', 'Quality of modifications, restoration, or custom work', 2, 10, '2025-05-27 19:08:23', '2025-05-27 19:08:23'),
(13, 'Difficulty', 'Rarity of vehicle and difficulty of restoration/build', 5, 10, '2025-05-27 19:08:23', '2025-05-29 16:09:23'),
(14, 'Detail', 'Attention to detail throughout the vehicle', 7, 10, '2025-05-27 19:08:23', '2025-05-29 16:09:02'),
(15, 'Paint Quality', 'Overall quality and condition of the paint', 10, 10, '2025-05-29 14:04:59', '2025-05-29 14:04:59'),
(16, 'Body Condition', 'Overall condition of the body panels and chrome', 10, 10, '2025-05-29 14:04:59', '2025-05-29 14:04:59'),
(18, 'Engine Bay', 'Cleanliness and presentation of the engine bay', 8, 10, '2025-05-29 14:04:59', '2025-05-29 14:04:59'),
(19, 'Wheels & Tires', 'Condition and cleanliness of wheels and tires', 7, 10, '2025-05-29 14:04:59', '2025-05-29 14:04:59'),
(24, 'Interior', 'Condition and cleanliness of the interior', 8, 10, '2025-05-29 14:05:08', '2025-05-29 14:05:08'),
(27, 'Undercarriage', 'Condition and cleanliness of the undercarriage', 7, 10, '2025-05-29 14:05:08', '2025-06-05 12:42:31');
