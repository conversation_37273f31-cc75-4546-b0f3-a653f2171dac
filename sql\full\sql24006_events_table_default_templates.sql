
-- --------------------------------------------------------

--
-- Table structure for table `default_templates`
--
-- Creation: Jun 23, 2025 at 08:30 PM
--

CREATE TABLE `default_templates` (
  `id` int(11) NOT NULL,
  `entity_type` varchar(50) NOT NULL,
  `template_id` int(11) NOT NULL,
  `created_by` int(11) NOT NULL,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- RELATIONSHIPS FOR TABLE `default_templates`:
--

--
-- Dumping data for table `default_templates`
--

INSERT INTO `default_templates` (`id`, `entity_type`, `template_id`, `created_by`, `created_at`, `updated_at`) VALUES
(2, 'show', 9, 3, '2025-05-25 18:59:37', '2025-06-03 15:16:26'),
(5, 'vehicle', 13, 3, '2025-05-27 07:21:07', '2025-06-03 15:16:33'),
(6, 'registration', 14, 3, '2025-05-27 07:49:00', '2025-06-03 15:16:41');
