<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-8">
            <h1><i class="fas fa-exclamation-triangle me-2"></i>Conflict Details</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="<?php echo BASE_URL; ?>/judging_conflict/dashboard">Conflicts</a>
                    </li>
                    <li class="breadcrumb-item active">Conflict #<?php echo $conflict->id; ?></li>
                </ol>
            </nav>
        </div>
        <div class="col-md-4 text-end">
            <a href="<?php echo BASE_URL; ?>/judging_conflict/dashboard" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Main Conflict Details -->
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Conflict Information
                    </h5>
                    <div>
                        <?php
                        $priorityClass = [
                            'urgent' => 'bg-danger',
                            'high' => 'bg-warning',
                            'normal' => 'bg-primary',
                            'low' => 'bg-secondary'
                        ][$conflict->priority] ?? 'bg-secondary';
                        
                        $statusClass = [
                            'open' => 'bg-warning',
                            'under_review' => 'bg-info',
                            'resolved' => 'bg-success',
                            'dismissed' => 'bg-secondary',
                            'escalated' => 'bg-danger'
                        ][$conflict->status] ?? 'bg-secondary';
                        ?>
                        <span class="badge <?php echo $priorityClass; ?> me-2">
                            <?php echo ucfirst($conflict->priority); ?> Priority
                        </span>
                        <span class="badge <?php echo $statusClass; ?>">
                            <?php echo ucwords(str_replace('_', ' ', $conflict->status)); ?>
                        </span>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6><strong><?php echo htmlspecialchars($conflict->title); ?></strong></h6>
                            <?php if ($conflict->auto_detected): ?>
                                <span class="badge bg-info mb-2">
                                    <i class="fas fa-robot me-1"></i>Automatically Detected
                                </span>
                            <?php endif; ?>
                            
                            <p><strong>Type:</strong> <?php echo ucwords(str_replace('_', ' ', $conflict->conflict_type)); ?></p>
                            <p><strong>Show:</strong> <?php echo htmlspecialchars($conflict->show_name); ?></p>
                            <?php if ($conflict->show_start_date): ?>
                                <p><strong>Show Date:</strong> <?php echo formatDateTimeForUser($conflict->show_start_date, $_SESSION['user_id'] ?? null, 'F j, Y'); ?></p>
                            <?php endif; ?>
                            
                            <?php if ($conflict->registration_number): ?>
                                <p><strong>Registration:</strong> #<?php echo htmlspecialchars($conflict->registration_number); ?>
                                <?php if ($conflict->display_number): ?>
                                    (Display #<?php echo htmlspecialchars($conflict->display_number); ?>)
                                <?php endif; ?>
                                </p>
                            <?php endif; ?>
                            
                            <?php if ($conflict->year && $conflict->make && $conflict->model): ?>
                                <p><strong>Vehicle:</strong> <?php echo htmlspecialchars($conflict->year . ' ' . $conflict->make . ' ' . $conflict->model); ?></p>
                            <?php endif; ?>
                            
                            <?php if ($conflict->vehicle_owner_name): ?>
                                <p><strong>Vehicle Owner:</strong> <?php echo htmlspecialchars($conflict->vehicle_owner_name); ?></p>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Reported By:</strong> <?php echo htmlspecialchars($conflict->reported_by_name); ?> 
                               (<?php echo ucfirst($conflict->reported_by_role); ?>)</p>
                            <p><strong>Reported On:</strong> <?php echo formatDateTimeForUser($conflict->created_at, $_SESSION['user_id'] ?? null, 'F j, Y g:i A'); ?></p>
                            
                            <?php if ($conflict->assigned_to_name): ?>
                                <p><strong>Assigned To:</strong> <?php echo htmlspecialchars($conflict->assigned_to_name); ?></p>
                            <?php endif; ?>
                            
                            <?php if ($conflict->resolved_at): ?>
                                <p><strong>Resolved On:</strong> <?php echo formatDateTimeForUser($conflict->resolved_at, $_SESSION['user_id'] ?? null, 'F j, Y g:i A'); ?></p>
                                <?php if ($conflict->resolved_by_name): ?>
                                    <p><strong>Resolved By:</strong> <?php echo htmlspecialchars($conflict->resolved_by_name); ?></p>
                                <?php endif; ?>
                            <?php endif; ?>
                            
                            <?php if ($conflict->escalated_at): ?>
                                <p><strong>Escalated On:</strong> <?php echo formatDateTimeForUser($conflict->escalated_at, $_SESSION['user_id'] ?? null, 'F j, Y g:i A'); ?></p>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <hr>
                    
                    <h6><strong>Description:</strong></h6>
                    <div class="bg-light p-3 rounded">
                        <?php echo nl2br(htmlspecialchars($conflict->description)); ?>
                    </div>
                    
                    <?php if ($conflict->detection_criteria): ?>
                        <hr>
                        <h6><strong>Detection Criteria:</strong></h6>
                        <div class="bg-info bg-opacity-10 p-3 rounded">
                            <small><?php echo htmlspecialchars($conflict->detection_criteria); ?></small>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Related Scores -->
            <?php if (!empty($related_scores)): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-star me-2"></i>Related Scores</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Judge</th>
                                        <th>Metric</th>
                                        <th>Score</th>
                                        <th>Comments</th>
                                        <th>Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($related_scores as $score): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($score->judge_name ?? 'Unknown'); ?></td>
                                            <td><?php echo htmlspecialchars($score->metric_name ?? 'Unknown'); ?></td>
                                            <td><strong><?php echo $score->score; ?></strong></td>
                                            <td><?php echo htmlspecialchars($score->comments ?? ''); ?></td>
                                            <td><?php echo formatDateTimeForUser($score->created_at, $_SESSION['user_id'] ?? null, 'M j g:i A'); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Related Judges -->
            <?php if (!empty($related_judges)): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-users me-2"></i>Related Judges</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <?php foreach ($related_judges as $judge): ?>
                                <div class="col-md-6 mb-2">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-user-tie me-2"></i>
                                        <div>
                                            <strong><?php echo htmlspecialchars($judge->name); ?></strong>
                                            <br><small class="text-muted"><?php echo htmlspecialchars($judge->email); ?></small>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Comments Section -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-comments me-2"></i>Comments</h5>
                </div>
                <div class="card-body">
                    <!-- Existing Comments -->
                    <div id="comments-list">
                        <?php if (empty($conflict->comments)): ?>
                            <p class="text-muted">No comments yet.</p>
                        <?php else: ?>
                            <?php foreach ($conflict->comments as $comment): ?>
                                <div class="comment mb-3 p-3 <?php echo $comment->is_internal ? 'bg-warning bg-opacity-10 border-warning' : 'bg-light'; ?> rounded">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <div>
                                            <strong><?php echo htmlspecialchars($comment->user_name); ?></strong>
                                            <span class="badge bg-secondary ms-2"><?php echo ucfirst($comment->user_role); ?></span>
                                            <?php if ($comment->is_internal): ?>
                                                <span class="badge bg-warning ms-1">Internal</span>
                                            <?php endif; ?>
                                        </div>
                                        <small class="text-muted">
                                            <?php echo formatDateTimeForUser($comment->created_at, $_SESSION['user_id'] ?? null, 'M j, Y g:i A'); ?>
                                        </small>
                                    </div>
                                    <div><?php echo nl2br(htmlspecialchars($comment->comment)); ?></div>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>

                    <!-- Add Comment Form -->
                    <hr>
                    <form id="addCommentForm">
                        <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
                        <div class="mb-3">
                            <label for="comment" class="form-label">Add Comment</label>
                            <textarea name="comment" id="comment" class="form-control" rows="3" 
                                      placeholder="Enter your comment..." required></textarea>
                        </div>
                        <?php if (in_array($user_role, ['admin', 'coordinator'])): ?>
                            <div class="mb-3">
                                <div class="form-check">
                                    <input type="checkbox" name="is_internal" id="is_internal" class="form-check-input" value="1">
                                    <label for="is_internal" class="form-check-label">
                                        Internal comment (visible only to admins and coordinators)
                                    </label>
                                </div>
                            </div>
                        <?php endif; ?>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-comment me-2"></i>Add Comment
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-md-4">
            <!-- Status Management -->
            <?php if ($can_edit): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0"><i class="fas fa-cogs me-2"></i>Manage Conflict</h5>
                    </div>
                    <div class="card-body">
                        <form id="updateConflictForm">
                            <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
                            
                            <div class="mb-3">
                                <label for="status" class="form-label">Status</label>
                                <select name="status" id="status" class="form-select">
                                    <option value="open" <?php echo $conflict->status === 'open' ? 'selected' : ''; ?>>Open</option>
                                    <option value="under_review" <?php echo $conflict->status === 'under_review' ? 'selected' : ''; ?>>Under Review</option>
                                    <option value="resolved" <?php echo $conflict->status === 'resolved' ? 'selected' : ''; ?>>Resolved</option>
                                    <option value="dismissed" <?php echo $conflict->status === 'dismissed' ? 'selected' : ''; ?>>Dismissed</option>
                                    <option value="escalated" <?php echo $conflict->status === 'escalated' ? 'selected' : ''; ?>>Escalated</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="priority" class="form-label">Priority</label>
                                <select name="priority" id="priority" class="form-select">
                                    <option value="low" <?php echo $conflict->priority === 'low' ? 'selected' : ''; ?>>Low</option>
                                    <option value="normal" <?php echo $conflict->priority === 'normal' ? 'selected' : ''; ?>>Normal</option>
                                    <option value="high" <?php echo $conflict->priority === 'high' ? 'selected' : ''; ?>>High</option>
                                    <option value="urgent" <?php echo $conflict->priority === 'urgent' ? 'selected' : ''; ?>>Urgent</option>
                                </select>
                            </div>
                            
                            <?php if ($user_role === 'admin' && !empty($available_admins)): ?>
                                <div class="mb-3">
                                    <label for="assigned_to_admin_id" class="form-label">Assign To</label>
                                    <select name="assigned_to_admin_id" id="assigned_to_admin_id" class="form-select">
                                        <option value="">Unassigned</option>
                                        <?php foreach ($available_admins as $admin): ?>
                                            <option value="<?php echo $admin->id; ?>" 
                                                    <?php echo $conflict->assigned_to_admin_id == $admin->id ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($admin->name); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            <?php endif; ?>
                            
                            <div class="mb-3">
                                <label for="resolution_notes" class="form-label">Resolution Notes</label>
                                <textarea name="resolution_notes" id="resolution_notes" class="form-control" rows="3"
                                          placeholder="Enter resolution notes..."><?php echo htmlspecialchars($conflict->resolution_notes ?? ''); ?></textarea>
                            </div>
                            
                            <div class="mb-3">
                                <label for="resolution_action" class="form-label">Resolution Action</label>
                                <textarea name="resolution_action" id="resolution_action" class="form-control" rows="3"
                                          placeholder="Describe actions taken..."><?php echo htmlspecialchars($conflict->resolution_action ?? ''); ?></textarea>
                            </div>
                            
                            <button type="submit" class="btn btn-success w-100">
                                <i class="fas fa-save me-2"></i>Update Conflict
                            </button>
                        </form>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Conflict Statistics -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Conflict Info</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6">
                            <div class="border-end">
                                <h4 class="text-primary">#<?php echo $conflict->id; ?></h4>
                                <small class="text-muted">Conflict ID</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <h4 class="text-info">
                                <?php 
                                $hoursOpen = $conflict->resolved_at ? 
                                    round((strtotime($conflict->resolved_at) - strtotime($conflict->created_at)) / 3600, 1) :
                                    round((time() - strtotime($conflict->created_at)) / 3600, 1);
                                echo $hoursOpen . 'h';
                                ?>
                            </h4>
                            <small class="text-muted"><?php echo $conflict->status === 'resolved' ? 'Resolution Time' : 'Time Open'; ?></small>
                        </div>
                    </div>
                    
                    <?php if (!empty($conflict->related_data)): ?>
                        <hr>
                        <h6>Additional Data:</h6>
                        <?php foreach ($conflict->related_data as $key => $value): ?>
                            <p class="mb-1">
                                <strong><?php echo ucwords(str_replace('_', ' ', $key)); ?>:</strong> 
                                <?php echo is_numeric($value) ? number_format($value, 2) : htmlspecialchars($value); ?>
                            </p>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>Quick Actions</h5>
                </div>
                <div class="card-body">
                    <?php if ($conflict->registration_id): ?>
                        <a href="<?php echo BASE_URL; ?>/admin/view_registration/<?php echo $conflict->registration_id; ?>" 
                           class="btn btn-outline-primary btn-sm w-100 mb-2">
                            <i class="fas fa-car me-2"></i>View Registration
                        </a>
                    <?php endif; ?>
                    
                    <a href="<?php echo BASE_URL; ?>/admin/judging/<?php echo $conflict->show_id; ?>" 
                       class="btn btn-outline-info btn-sm w-100 mb-2">
                        <i class="fas fa-trophy me-2"></i>View Show Judging
                    </a>
                    
                    <?php if ($user_role === 'admin'): ?>
                        <a href="<?php echo BASE_URL; ?>/admin/shows/edit/<?php echo $conflict->show_id; ?>" 
                           class="btn btn-outline-secondary btn-sm w-100 mb-2">
                            <i class="fas fa-edit me-2"></i>Edit Show
                        </a>
                    <?php endif; ?>
                    
                    <button type="button" class="btn btn-outline-warning btn-sm w-100" 
                            onclick="window.print()">
                        <i class="fas fa-print me-2"></i>Print Details
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Add comment form submission
document.getElementById('addCommentForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    fetch(`<?php echo BASE_URL; ?>/judging_conflict/addComment/<?php echo $conflict->id; ?>`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error: ' + (data.error || 'Failed to add comment'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while adding the comment');
    });
});

// Update conflict form submission
<?php if ($can_edit): ?>
document.getElementById('updateConflictForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    
    fetch(`<?php echo BASE_URL; ?>/judging_conflict/update/<?php echo $conflict->id; ?>`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error: ' + (data.error || 'Failed to update conflict'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating the conflict');
    });
});
<?php endif; ?>
</script>

<style>
@media print {
    .btn, .card-header, nav, .sidebar { display: none !important; }
    .card { border: none !important; box-shadow: none !important; }
    .card-body { padding: 0 !important; }
}
</style>

<?php require APPROOT . '/views/includes/footer.php'; ?>