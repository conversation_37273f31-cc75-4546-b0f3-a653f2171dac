<?php
/**
 * Test Show Search
 */

require_once 'config/config.php';
require_once 'core/Database.php';

echo "<h1>Test Show Search</h1>\n";

try {
    echo "<p>Testing show search functionality...</p>\n";
    
    require_once APPROOT . '/models/ShowModel.php';
    $showModel = new ShowModel();
    echo "<p style='color: green;'>✓ ShowModel loaded</p>\n";
    
    // Test direct database query like the search does
    $search = 'car'; // Test search term
    
    $showModel->db->query("
        SELECT s.*, u.name as coordinator_name 
        FROM shows s 
        LEFT JOIN users u ON s.coordinator_id = u.id 
        WHERE (s.name LIKE :search 
               OR s.description LIKE :search 
               OR s.city LIKE :search 
               OR s.state LIKE :search
               OR u.name LIKE :search)
        ORDER BY s.start_date DESC 
        LIMIT 20
    ");
    $showModel->db->bind(':search', '%' . $search . '%');
    $shows = $showModel->db->resultSet();
    
    echo "<p style='color: green;'>✓ Search query executed - found " . count($shows) . " shows with term 'car'</p>\n";
    
    if (count($shows) > 0) {
        echo "<h3>Sample Results:</h3>\n";
        echo "<ul>\n";
        foreach (array_slice($shows, 0, 5) as $show) {
            echo "<li><strong>" . htmlspecialchars($show->name) . "</strong> - " . 
                 htmlspecialchars($show->city . ', ' . $show->state) . " - " .
                 htmlspecialchars($show->coordinator_name ?? 'Unknown') . "</li>\n";
        }
        echo "</ul>\n";
    }
    
    // Test with a more specific search
    $search2 = 'show'; // Test search term
    
    $showModel->db->query("
        SELECT s.*, u.name as coordinator_name 
        FROM shows s 
        LEFT JOIN users u ON s.coordinator_id = u.id 
        WHERE (s.name LIKE :search 
               OR s.description LIKE :search 
               OR s.city LIKE :search 
               OR s.state LIKE :search
               OR u.name LIKE :search)
        ORDER BY s.start_date DESC 
        LIMIT 20
    ");
    $showModel->db->bind(':search', '%' . $search2 . '%');
    $shows2 = $showModel->db->resultSet();
    
    echo "<p style='color: green;'>✓ Search query executed - found " . count($shows2) . " shows with term 'show'</p>\n";
    
    if (count($shows2) > 0) {
        echo "<h3>Sample Results for 'show':</h3>\n";
        echo "<ul>\n";
        foreach (array_slice($shows2, 0, 5) as $show) {
            echo "<li><strong>" . htmlspecialchars($show->name) . "</strong> - " . 
                 htmlspecialchars($show->city . ', ' . $show->state) . " - " .
                 htmlspecialchars($show->coordinator_name ?? 'Unknown') . "</li>\n";
        }
        echo "</ul>\n";
    }
    
    // Test the actual AJAX endpoint
    echo "<hr>\n";
    echo "<h3>Test AJAX Search Endpoint:</h3>\n";
    echo "<p>Try these URLs in your browser:</p>\n";
    echo "<ul>\n";
    echo "<li><a href='" . BASE_URL . "/judging_conflict/searchShows?q=car' target='_blank'>Search for 'car'</a></li>\n";
    echo "<li><a href='" . BASE_URL . "/judging_conflict/searchShows?q=show' target='_blank'>Search for 'show'</a></li>\n";
    echo "<li><a href='" . BASE_URL . "/judging_conflict/searchShows?q=test' target='_blank'>Search for 'test'</a></li>\n";
    echo "</ul>\n";
    
    echo "<hr>\n";
    echo "<h2>✅ SUCCESS!</h2>\n";
    echo "<p style='color: green; font-weight: bold;'>Show search is working!</p>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>\n";
}

echo "<hr>\n";
echo "<p><a href='" . BASE_URL . "'>← Return to Home</a></p>\n";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2, h3 {
    color: #333;
}

p {
    margin: 5px 0;
}

ul {
    padding-left: 20px;
}

li {
    margin: 5px 0;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

pre {
    background: #f5f5f5;
    padding: 10px;
    border-radius: 4px;
    overflow-x: auto;
}
</style>