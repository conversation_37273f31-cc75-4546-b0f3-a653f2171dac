
-- --------------------------------------------------------

--
-- Table structure for table `users`
--
-- Creation: Aug 01, 2025 at 06:04 PM
-- Last update: Aug 01, 2025 at 02:30 PM
--

CREATE TABLE `users` (
  `id` int(10) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `password` varchar(255) NOT NULL,
  `role` enum('admin','coordinator','judge','user','staff') NOT NULL DEFAULT 'user',
  `status` enum('active','inactive','pending') NOT NULL DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `last_login` datetime DEFAULT NULL,
  `profile_image` varchar(255) DEFAULT NULL,
  `facebook_id` varchar(100) DEFAULT NULL,
  `can_create_free_shows` tinyint(1) NOT NULL DEFAULT 0,
  `exempt_from_listing_fees` tinyint(1) NOT NULL DEFAULT 0,
  `phone` varchar(20) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `city` varchar(100) DEFAULT NULL,
  `state` varchar(50) DEFAULT NULL,
  `zip` varchar(20) DEFAULT NULL,
  `timezone` varchar(50) DEFAULT 'America/New_York' COMMENT 'User preferred timezone for displaying dates and times',
  `facebook_token` varchar(255) DEFAULT NULL COMMENT 'Facebook access token for API calls',
  `pwa_installed` tinyint(1) DEFAULT 0 COMMENT 'Whether user has installed the PWA',
  `pwa_standalone` tinyint(1) DEFAULT 0 COMMENT 'Whether user is using PWA in standalone mode',
  `push_supported` tinyint(1) DEFAULT 0 COMMENT 'Whether user device supports push notifications',
  `last_seen` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT 'Last time user was active'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- RELATIONSHIPS FOR TABLE `users`:
--

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `name`, `email`, `password`, `role`, `status`, `created_at`, `updated_at`, `last_login`, `profile_image`, `facebook_id`, `can_create_free_shows`, `exempt_from_listing_fees`, `phone`, `address`, `city`, `state`, `zip`, `timezone`, `facebook_token`, `pwa_installed`, `pwa_standalone`, `push_supported`, `last_seen`) VALUES
(1, 'System', 'system@localhost', '', '', 'active', '2025-07-13 00:20:09', '2025-07-13 00:20:09', NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, 'America/New_York', NULL, 0, 0, 0, '2025-07-13 00:20:09'),
(3, 'Brian Correll', '<EMAIL>', '$2y$12$IPLDGTgMNkTzATi7HJEQl.2zGXFJiigwbQU2LkT68nh1VYJi5Qnd.', 'admin', 'active', '2025-05-18 18:32:01', '2025-08-01 14:30:05', '2025-08-01 14:30:05', NULL, '2949931925176821', 0, 0, '7042023114', '475 Lemly Ln', 'Salisbury', 'NC', '28146', 'America/New_York', 'EAALHJFDy6GMBO7VClsPqnhxIpCEHg1Uolau2ro536zGdZAHXxkPRvGlF8Kppplbsbhf0jvZBn9DltaBTtZCx5bey1iG3ii9G1OWfqJ8VI5wnZCWu7XKQlkFP6nmjkG9alnKdZBZAw9okP55ee0WnZC1p3KmcgsZBeUe8HIZAqFWlP2773zdg7MMHUyG0jZCrFZANVdQds3fPfVORTxnXS5hZAZALCZB2ttxZA9BlpsMTkAbp2byzt8Wcn22glZ', 0, 0, 0, '2025-08-01 14:30:05'),
(4, 'test', '<EMAIL>', '$2y$12$q8LZZu3RtNaPMPwe0CmgDeBOVvtIdNJb5e4/Fw6CylRQkON.lUeaS', 'judge', 'active', '2025-05-18 19:50:52', '2025-05-19 19:59:46', NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, 'America/New_York', NULL, 0, 0, 0, '2025-07-02 20:52:20'),
(5, 'Admin', '<EMAIL>', '$2y$12$DtjAA/zLuwNjz/IhM3VVQeM/Q13JaEC/sNR4Io90gkgytNjW7LUSe', 'coordinator', 'active', '2025-05-19 15:57:03', '2025-07-20 12:26:12', '2025-05-19 16:21:32', NULL, NULL, 0, 0, '', '304 haney st', 'china grove', 'nc', '28023', 'America/New_York', NULL, 0, 0, 0, '2025-07-20 12:26:12'),
(12, 'jsfsdjkfksd', '<EMAIL>', '$2y$12$bKPvgS4i.9klNrEmGWDv5.aQKmM6jsVjHez06cGAt2MdjYhiaXASW', 'judge', 'active', '2025-05-29 14:50:50', '2025-06-06 00:27:17', NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, 'America/New_York', NULL, 0, 0, 0, '2025-07-02 20:52:20'),
(13, 'John Smith', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'coordinator', 'active', '2025-06-08 13:33:42', '2025-06-16 15:21:20', NULL, NULL, NULL, 0, 0, '704-857-3200', '123 any street', 'china grove', 'nc', '28023', 'America/New_York', NULL, 0, 0, 0, '2025-07-10 13:32:00'),
(53, 'Ronald Roberts', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'user', 'active', '2025-06-08 13:33:42', '2025-06-08 13:33:42', NULL, NULL, NULL, 0, 0, NULL, NULL, NULL, NULL, NULL, 'America/New_York', NULL, 0, 0, 0, '2025-07-10 13:32:00'),
(210, 'test', '<EMAIL>', '$2y$12$J0JoMNq62U8nGpxjuSwxZepA/96Q/t0hexqyV6rTT/R7NJ/JNguGS', 'user', 'active', '2025-06-13 14:10:53', '2025-06-13 14:10:53', NULL, NULL, NULL, 0, 0, '123-123-1234', NULL, NULL, NULL, NULL, 'America/New_York', NULL, 0, 0, 0, '2025-07-02 20:52:20'),
(211, 'Michelle Schenk Correll', '<EMAIL>', '$2y$12$BrM7rUee0/GbC/YcAPvopObRvqVVSqxnd2HCYNcoJFmSL1jy0K8uy', 'user', 'active', '2025-06-14 01:07:46', '2025-07-16 17:35:57', '2025-06-14 08:51:11', NULL, '10227653462268942', 0, 0, NULL, NULL, NULL, NULL, NULL, 'America/New_York', 'EAALHJFDy6GMBO7kPvI3lNqOmzyPf9vbNPdNYou8HRxNTvOYnQpZC6ZBAMpmWZA725BwgfHfKB9fBHj02mYMR1xStJNWYcqNkTxIMfZBIS0WnzdP8YhE6ymL8ymJ7H4xa9ZAsoo1i90Fde4FF22BOgRwHc0bbAuX4lJJpfWGVcwtP9mCan0fdx8whJxdHJuc5W2GUc2w2cKbZBflNQMSZAyysOOHlSfYGfXZBC2cCTRrgZB9DYklRlYFtuuIeY0', 0, 0, 0, '2025-07-16 17:35:57'),
(2425, 'Brian Correll', '<EMAIL>', '$2y$12$nbEV668H5s1QiP5nVaFFAuyWAePDufXz9T1ccyN4DXYzk/uEMPA5K', 'user', 'active', '2025-07-27 20:09:50', '2025-07-27 23:34:50', '2025-07-27 23:34:50', NULL, NULL, 0, 0, '', '', '', '', 'sales@gurusonwheels.', 'America/New_York', NULL, 0, 0, 0, '2025-07-27 23:34:50');
