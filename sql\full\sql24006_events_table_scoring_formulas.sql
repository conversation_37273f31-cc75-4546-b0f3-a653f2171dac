
-- --------------------------------------------------------

--
-- Table structure for table `scoring_formulas`
--
-- Creation: Jun 23, 2025 at 08:30 PM
--

CREATE TABLE `scoring_formulas` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `formula` text NOT NULL,
  `is_default` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- RELATIONSHIPS FOR TABLE `scoring_formulas`:
--

--
-- Dumping data for table `scoring_formulas`
--

INSERT INTO `scoring_formulas` (`id`, `name`, `description`, `formula`, `is_default`, `created_at`, `updated_at`) VALUES
(1, 'Standard Formula', 'Standard formula: Raw Score × Metric Weight × Age Weight', 'rawScore * (metricWeight * weightMultiplier) * (useAgeWeight ? ageWeight * ageWeightMultiplier : 1)', 1, '2025-06-05 09:13:44', '2025-06-10 15:30:32'),
(2, 'Simple Average', 'Simple average of all scores without weights', 'rawScore', 0, '2025-06-05 09:13:44', '2025-06-05 09:39:22'),
(3, 'Weighted Average', 'Weighted average without age adjustment', 'rawScore * (metricWeight * weightMultiplier)', 0, '2025-06-05 09:13:44', '2025-06-05 09:44:58'),
(4, 'Custom Exponential', 'Exponential scoring that rewards excellence', 'Math.pow(rawScore / maxScore, 1.5) * maxScore * (metricWeight * weightMultiplier) * (useAgeWeight ? ageWeight * ageWeightMultiplier : 1)', 0, '2025-06-05 09:13:44', '2025-06-05 13:32:09');
