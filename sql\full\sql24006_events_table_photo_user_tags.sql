
-- --------------------------------------------------------

--
-- Table structure for table `photo_user_tags`
--
-- Creation: Aug 01, 2025 at 01:07 PM
--

CREATE TABLE `photo_user_tags` (
  `id` int(10) UNSIGNED NOT NULL,
  `photo_id` int(10) UNSIGNED NOT NULL,
  `tagged_user_id` int(10) UNSIGNED NOT NULL,
  `tagged_by_user_id` int(10) UNSIGNED NOT NULL,
  `x_position` decimal(5,2) DEFAULT NULL,
  `y_position` decimal(5,2) DEFAULT NULL,
  `status` enum('pending','approved','rejected') DEFAULT 'pending',
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `approved_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_uca1400_ai_ci;

--
-- RELATIONSHIPS FOR TABLE `photo_user_tags`:
--   `photo_id`
--       `images` -> `id`
--   `tagged_user_id`
--       `users` -> `id`
--   `tagged_by_user_id`
--       `users` -> `id`
--
