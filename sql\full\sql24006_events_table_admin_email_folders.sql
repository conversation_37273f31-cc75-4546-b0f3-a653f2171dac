
-- --------------------------------------------------------

--
-- Table structure for table `admin_email_folders`
--
-- Creation: Jul 16, 2025 at 07:00 PM
--

CREATE TABLE `admin_email_folders` (
  `id` int(11) NOT NULL,
  `admin_user_id` int(11) NOT NULL COMMENT 'Admin who owns this folder',
  `name` varchar(100) NOT NULL COMMENT 'Folder name',
  `color` varchar(7) DEFAULT '#007bff' COMMENT 'Folder color (hex)',
  `sort_order` int(11) DEFAULT 0 COMMENT 'Display order',
  `is_system` tinyint(1) DEFAULT 0 COMMENT 'System folder (cannot be deleted)',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- RELATIONSHIPS FOR TABLE `admin_email_folders`:
--

--
-- Dumping data for table `admin_email_folders`
--

INSERT INTO `admin_email_folders` (`id`, `admin_user_id`, `name`, `color`, `sort_order`, `is_system`, `created_at`, `updated_at`) VALUES
(1, 3, 'Inbox', '#007bff', 1, 1, '2025-07-16 19:00:54', '2025-07-16 19:00:54'),
(2, 3, 'Sent', '#28a745', 2, 1, '2025-07-16 19:00:54', '2025-07-16 19:00:54'),
(3, 3, 'Archive', '#6c757d', 3, 1, '2025-07-16 19:00:54', '2025-07-16 19:00:54'),
(4, 3, 'Trash', '#dc3545', 4, 1, '2025-07-16 19:00:54', '2025-07-16 19:00:54');
