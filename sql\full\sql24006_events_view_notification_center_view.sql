
-- --------------------------------------------------------

--
-- Structure for view `notification_center_view`
--
DROP TABLE IF EXISTS `notification_center_view`;

CREATE ALGORITHM=UNDEFINED DEFINER=`sql24006_forward-limpet`@`162.244.94.12` SQL SECURITY DEFINER VIEW `notification_center_view`  AS SELECT `m`.`id` AS `id`, `m`.`to_user_id` AS `user_id`, CASE WHEN `m`.`message_type` = 'direct' THEN 'message' WHEN `m`.`message_type` = 'notification' THEN 'system' ELSE `m`.`message_type` END AS `notification_type`, 'messages' AS `source_table`, `m`.`id` AS `source_id`, `m`.`subject` AS `title`, `m`.`message` AS `message`, concat('/notification_center/viewMessage/',`m`.`id`) AS `action_url`, CASE WHEN `m`.`requires_reply` = 1 THEN 'Reply' ELSE 'View' END AS `action_text`, json_object('show_id',`m`.`show_id`,'from_user_id',`m`.`from_user_id`) AS `metadata`, `m`.`is_read` AS `is_read`, 0 AS `is_archived`, `m`.`created_at` AS `created_at`, `m`.`read_at` AS `read_at` FROM `messages` AS `m` WHERE `m`.`is_archived` = 0 ;
