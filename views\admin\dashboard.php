<?php require APPROOT . '/views/includes/header.php'; ?>



<div class="container py-4">
    <h1 class="mb-4">Admin Dashboard</h1>
    
    <div class="row mb-4">
        <div class="col-md-3 col-sm-6 mb-4">
            <div class="dashboard-stat compact-stat bg-primary text-white">
                <div class="stat-value"><?php echo $user_count; ?></div>
                <div class="stat-label">Users</div>
                <a href="<?php echo BASE_URL; ?>/admin/users" class="btn btn-light btn-sm">Manage Users</a>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-4">
            <div class="dashboard-stat compact-stat bg-success text-white">
                <div class="stat-value"><?php echo $show_count; ?></div>
                <div class="stat-label">Shows</div>
                <a href="<?php echo BASE_URL; ?>/admin/shows" class="btn btn-light btn-sm">Manage Shows</a>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-4">
            <div class="dashboard-stat compact-stat bg-info text-white">
                <div class="stat-value">
                    <i class="fas fa-user-tag"></i>
                </div>
                <div class="stat-label">User Roles</div>
                <a href="<?php echo BASE_URL; ?>/admin/roles" class="btn btn-light btn-sm">Manage Roles</a>
            </div>
        </div>
        <div class="col-md-3 col-sm-6 mb-4">
            <div class="dashboard-stat compact-stat bg-warning text-dark">
                <div class="stat-value">
                    <i class="fas fa-cog"></i>
                </div>
                <div class="stat-label">Settings</div>
                <a href="<?php echo BASE_URL; ?>/admin/settings" class="btn btn-dark btn-sm">System Settings</a>
            </div>
        </div>
    </div>
    
    <!-- Quick Links -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">Quick Links</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="<?php echo BASE_URL; ?>/payment/admin" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-tachometer-alt fa-2x mb-2"></i>
                                <span>Payment Dashboard</span>
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="<?php echo BASE_URL; ?>/payment/coordinatorSettings" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-money-check-alt fa-2x mb-2"></i>
                                <span>Default Coordinator Settings</span>
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="<?php echo BASE_URL; ?>/admin/users?role=coordinator" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-users-cog fa-2x mb-2"></i>
                                <span>Manage Coordinators</span>
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="<?php echo BASE_URL; ?>/admin/registrations" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-clipboard-check fa-2x mb-2"></i>
                                <span>Registration Dashboard</span>
                            </a>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="<?php echo BASE_URL; ?>/judging_conflict/dashboard" class="btn btn-outline-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                                <span>Judging Conflicts</span>
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="<?php echo BASE_URL; ?>/admin/judging" class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-trophy fa-2x mb-2"></i>
                                <span>Judging Management</span>
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="<?php echo BASE_URL; ?>/notification_center" class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-bell fa-2x mb-2"></i>
                                <span>Message Center</span>
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="<?php echo BASE_URL; ?>/calendar" class="btn btn-outline-secondary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-calendar fa-2x mb-2"></i>
                                <span>Event Calendar</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Developer Tools -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <h5 class="card-title mb-0">Developer Tools</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="<?php echo BASE_URL; ?>/admin/runScripts" class="btn btn-outline-dark w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-code fa-2x mb-2"></i>
                                <span>Run Maintenance Scripts</span>
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="<?php echo BASE_URL; ?>/admin/settings_developer" class="btn btn-outline-dark w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-tools fa-2x mb-2"></i>
                                <span>Developer Settings</span>
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="<?php echo BASE_URL; ?>/admin/view_logs" class="btn btn-outline-dark w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-clipboard-list fa-2x mb-2"></i>
                                <span>System Logs</span>
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="<?php echo BASE_URL; ?>/admin/cleanup" class="btn btn-outline-dark w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                                <i class="fas fa-broom fa-2x mb-2"></i>
                                <span>System Cleanup</span>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row">
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">Upcoming Shows</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($upcoming_shows)) : ?>
                        <p class="text-muted">No upcoming shows.</p>
                    <?php else : ?>
                        <div class="list-group">
                            <?php foreach ($upcoming_shows as $show) : ?>
                                <a href="<?php echo BASE_URL; ?>/admin/editShow/<?php echo $show->id; ?>" class="list-group-item list-group-item-action">
                                    <div class="d-flex w-100 justify-content-between">
                                        <h5 class="mb-1"><?php echo $show->name; ?></h5>
                                        <small>
                                            <?php if ($show->status == 'published') : ?>
                                                <span class="badge bg-success">Published</span>
                                            <?php elseif ($show->status == 'draft') : ?>
                                                <span class="badge bg-secondary">Draft</span>
                                            <?php elseif ($show->status == 'completed') : ?>
                                                <span class="badge bg-info">Completed</span>
                                            <?php else : ?>
                                                <span class="badge bg-danger">Cancelled</span>
                                            <?php endif; ?>
                                        </small>
                                    </div>
                                    <p class="mb-1"><?php echo $show->location; ?></p>
                                    <small>
                                        <?php echo formatDateTimeForUser($show->start_date, $_SESSION['user_id'] ?? null, 'M j, Y'); ?> - 
                                        <?php echo formatDateTimeForUser($show->end_date, $_SESSION['user_id'] ?? null, 'M j, Y'); ?>
                                    </small>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
                <div class="card-footer">
                    <a href="<?php echo BASE_URL; ?>/admin/shows" class="btn btn-primary">View All Shows</a>
                    <a href="<?php echo BASE_URL; ?>/admin/addShow" class="btn btn-outline-primary">Add New Show</a>
                </div>
            </div>
        </div>
        
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-header bg-success text-white">
                    <h5 class="card-title mb-0">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="d-grid">
                                <a href="<?php echo BASE_URL; ?>/admin/users" class="btn btn-outline-primary btn-lg">
                                    <i class="fas fa-users mb-2 d-block" style="font-size: 2rem;"></i>
                                    Manage Users
                                </a>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="d-grid">
                                <a href="<?php echo BASE_URL; ?>/admin/roles" class="btn btn-outline-info btn-lg">
                                    <i class="fas fa-user-tag mb-2 d-block" style="font-size: 2rem;"></i>
                                    Manage Roles
                                </a>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="d-grid">
                                <a href="<?php echo BASE_URL; ?>/admin/addShow" class="btn btn-outline-success btn-lg">
                                    <i class="fas fa-calendar-plus mb-2 d-block" style="font-size: 2rem;"></i>
                                    Create Show
                                </a>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="d-grid">
                                <a href="<?php echo BASE_URL; ?>/admin/reports" class="btn btn-outline-warning btn-lg">
                                    <i class="fas fa-chart-bar mb-2 d-block" style="font-size: 2rem;"></i>
                                    System Reports
                                </a>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="d-grid">
                                <a href="<?php echo BASE_URL; ?>/form_designer/templates" class="btn btn-outline-danger btn-lg">
                                    <i class="fas fa-wpforms mb-2 d-block" style="font-size: 2rem;"></i>
                                    Form Builder
                                </a>
                            </div>
                        </div>
                        <div class="col-md-4 mb-3">
                            <div class="d-grid">
                                <a href="<?php echo BASE_URL; ?>/show_roles/adminOverview" class="btn btn-outline-purple btn-lg">
                                    <i class="fas fa-users-cog mb-2 d-block" style="font-size: 2rem;"></i>
                                    Show Role Manager
                                </a>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-danger text-white">
                    <h5 class="card-title mb-0">Visual Design Tools</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-wpforms fa-4x mb-3 text-primary"></i>
                                    <h5 class="card-title">Form Builder</h5>
                                    <p class="card-text">Create and customize forms for show registrations and vehicle submissions.</p>
                                    <div class="d-grid gap-2">
                                        <a href="<?php echo BASE_URL; ?>/form_designer/templates" class="btn btn-primary">
                                            <i class="fas fa-edit me-2"></i> Open Form Builder
                                        </a>
                                        <a href="<?php echo BASE_URL; ?>/admin_field_types" class="btn btn-outline-secondary">
                                            <i class="fas fa-cog me-2"></i> Manage Field Types
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card h-100">
                                <div class="card-body text-center">
                                    <i class="fas fa-images fa-4x mb-3 text-success"></i>
                                    <h5 class="card-title">Image Manager</h5>
                                    <p class="card-text">Edit, crop, resize, and enhance images for your shows and vehicles.</p>
                                    <a href="<?php echo BASE_URL; ?>/admin/imageManager" class="btn btn-success btn-lg">
                                        <i class="fas fa-image me-2"></i> Open Image Manager
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="card-title mb-0">System Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <h6>Application</h6>
                            <ul class="list-group mb-3">
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    Version
                                    <span class="badge bg-primary"><?php echo APP_VERSION; ?></span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    Install Date
                                    <span><?php echo formatDateTimeForUser(INSTALL_DATE, $_SESSION['user_id'] ?? null, 'M j, Y'); ?></span>
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-4">
                            <h6>Database</h6>
                            <ul class="list-group mb-3">
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    Host
                                    <span><?php echo DB_HOST; ?></span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    Database
                                    <span><?php echo DB_NAME; ?></span>
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-4">
                            <h6>Server</h6>
                            <ul class="list-group mb-3">
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    PHP Version
                                    <span><?php echo phpversion(); ?></span>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    Server
                                    <span><?php echo $_SERVER['SERVER_SOFTWARE']; ?></span>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Extra padding at the bottom -->
    <div class="pb-5 mb-5"></div>
</div>

<?php require APPROOT . '/views/includes/footer.php'; ?>