
-- --------------------------------------------------------

--
-- Table structure for table `photo_comments`
--
-- Creation: Aug 01, 2025 at 06:04 PM
-- Last update: Aug 01, 2025 at 01:59 PM
--

CREATE TABLE `photo_comments` (
  `id` int(10) UNSIGNED NOT NULL,
  `photo_id` int(10) UNSIGNED NOT NULL,
  `user_id` int(10) UNSIGNED NOT NULL,
  `comment` text NOT NULL,
  `parent_comment_id` int(10) UNSIGNED DEFAULT NULL,
  `mentions` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`mentions`)),
  `is_approved` tinyint(1) DEFAULT 1,
  `is_edited` tinyint(1) DEFAULT 0,
  `edited_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_uca1400_ai_ci;

--
-- RELATIONSHIPS FOR TABLE `photo_comments`:
--   `photo_id`
--       `images` -> `id`
--   `user_id`
--       `users` -> `id`
--   `parent_comment_id`
--       `photo_comments` -> `id`
--

--
-- Dumping data for table `photo_comments`
--

INSERT INTO `photo_comments` (`id`, `photo_id`, `user_id`, `comment`, `parent_comment_id`, `mentions`, `is_approved`, `is_edited`, `edited_at`, `created_at`, `updated_at`) VALUES
(3, 589, 3, 'tst', NULL, '[]', 1, 0, NULL, '2025-08-01 13:50:28', '2025-08-01 13:50:28'),
(4, 589, 3, 'sdjkfhsdjkfjksd', NULL, '[]', 1, 0, NULL, '2025-08-01 13:50:34', '2025-08-01 13:50:34'),
(6, 589, 3, 'fgdfgdfg', NULL, '[]', 1, 0, NULL, '2025-08-01 13:59:12', '2025-08-01 13:59:12');
