
-- --------------------------------------------------------

--
-- Table structure for table `user_toast_notifications`
--
-- Creation: Jul 12, 2025 at 04:48 PM
--

CREATE TABLE `user_toast_notifications` (
  `id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `event_id` int(11) DEFAULT NULL,
  `event_type` enum('calendar_event','car_show','test') DEFAULT NULL,
  `is_read` tinyint(1) DEFAULT 0,
  `notification_center_id` int(11) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- RELATIONSHIPS FOR TABLE `user_toast_notifications`:
--

--
-- Dumping data for table `user_toast_notifications`
--

INSERT INTO `user_toast_notifications` (`id`, `user_id`, `title`, `message`, `event_id`, `event_type`, `is_read`, `notification_center_id`, `created_at`) VALUES
(73, 1, 'New Message', 'From System: 🎉 Test Message Subject', 33, '', 0, NULL, '2025-07-15 13:10:55'),
(74, 1, 'New Message', 'From System: Test Message', 34, '', 0, NULL, '2025-07-15 13:11:24'),
(75, 1, 'New Message', 'From System: Test Message', 35, '', 0, NULL, '2025-07-15 13:16:30'),
(76, 1, 'New Message', 'From System: Test Message', 36, '', 0, NULL, '2025-07-15 13:16:38'),
(77, 1, 'New Message', 'From System: Test Message', 37, '', 0, NULL, '2025-07-15 13:16:39'),
(78, 1, 'New Message', 'From System: Test Message', 38, '', 0, NULL, '2025-07-15 13:23:47'),
(108, 211, 'New Message', 'From System: Contact Form: test', 68, '', 0, NULL, '2025-07-15 22:25:14'),
(110, 211, 'New Message', 'From System: Contact Form: fghfghfgh', 70, '', 0, NULL, '2025-07-16 00:37:37'),
(111, 1, 'New Message', 'From Brian Correll: Re: Contact Form: fghfghfgh', 71, '', 0, NULL, '2025-07-16 00:38:19'),
(113, 211, 'New Message', 'From System: Contact Form: kjflgjldfjgkldfjklg', 73, '', 0, NULL, '2025-07-16 01:01:00'),
(114, 1, 'New Message', 'From Brian Correll: Re: Contact Form: kjflgjldfjgkldfjklg', 74, '', 0, NULL, '2025-07-16 01:12:18'),
(115, 1, 'New Message', 'From Brian Correll: Re: Contact Form: kjflgjldfjgkldfjklg', 75, '', 0, NULL, '2025-07-16 01:14:33'),
(117, 211, 'New Message', 'From System: Contact Form: test email', 77, '', 0, NULL, '2025-07-16 12:00:16'),
(118, 1, 'New Message', 'From Brian Correll: Re: Contact Form: test email', 78, '', 0, NULL, '2025-07-16 12:01:33'),
(120, 211, 'New Message', 'From System: Contact Form: fsdgsdfsdf', 80, '', 0, NULL, '2025-07-16 12:22:02'),
(121, 1, 'New Message', 'From Brian Correll: Re: Contact Form: fsdgsdfsdf', 81, '', 0, NULL, '2025-07-16 13:17:52'),
(123, 211, 'New Message', 'From System: Contact Form: test contact form', 84, '', 0, NULL, '2025-07-16 14:02:47'),
(124, 1, 'New Message', 'From Brian Correll: Re: Contact Form: test contact form', 85, '', 0, NULL, '2025-07-16 14:04:36'),
(126, 211, 'New Message', 'From System: Contact Form: ofdgkldfjklk', 87, '', 0, NULL, '2025-07-16 14:48:04'),
(128, 211, 'New Message', 'From System: Contact Form: kdjfgljfdklgjkldfjkl', 89, '', 0, NULL, '2025-07-16 14:55:54'),
(130, 211, 'New Message', 'From System: Contact Form: sdfsdfsdf', 91, '', 0, NULL, '2025-07-16 15:07:28'),
(131, 1, 'New Message', 'From Brian Correll: Re: Contact Form: sdfsdfsdf', 92, '', 0, NULL, '2025-07-16 15:10:19'),
(133, 211, 'New Message', 'From System: Contact Form: gdfgdfgfd', 94, '', 0, NULL, '2025-07-16 15:15:08'),
(135, 211, 'New Message', 'From System: Contact Form: gdfgdfgfd', 96, '', 0, NULL, '2025-07-16 15:18:32'),
(137, 211, 'New Message', 'From System: Contact Form: gdfgdfgdfg', 98, '', 0, NULL, '2025-07-16 15:19:19'),
(139, 211, 'New Message', 'From System: Contact Form: Test Notification from Events System', 100, '', 0, NULL, '2025-07-16 15:26:00'),
(141, 211, 'New Message', 'From System: Contact Form: fgjfghfgh', 102, '', 0, NULL, '2025-07-16 15:40:37'),
(143, 211, 'New Message', 'From System: Contact Form: dfgdfgdfg', 104, '', 0, NULL, '2025-07-16 15:42:30'),
(145, 211, 'New Message', 'From System: Contact Form: Test Toast Message', 106, '', 0, NULL, '2025-07-16 15:44:32'),
(147, 211, 'New Message', 'From System: Contact Form: hfghfhfghfghf', 108, '', 0, NULL, '2025-07-16 15:47:32'),
(149, 211, 'New Message', 'From System: Contact Form: Test Toast Message', 110, '', 0, NULL, '2025-07-16 15:50:06'),
(151, 211, 'New Message', 'From System: Contact Form: gdfgdfgdfgd', 112, '', 0, NULL, '2025-07-16 16:23:22'),
(153, 211, 'New Message', 'From System: Contact Form: gfdgdfgdfg', 114, '', 0, NULL, '2025-07-16 16:30:46'),
(156, 211, 'New Message', 'From System: Contact Form: dfgdfgdf', 117, '', 0, NULL, '2025-07-16 16:41:42'),
(158, 211, 'New Message', 'From System: Contact Form: Test Toast Message', 119, '', 0, NULL, '2025-07-16 17:18:16'),
(160, 211, 'New Message', 'From System: Contact Form: Test Notification from Events System', 121, '', 0, NULL, '2025-07-16 17:35:07'),
(164, 1, 'New Message', 'From System: Test Email Message 2025-07-17 16:49:47', 443, '', 0, NULL, '2025-07-17 16:49:47');
