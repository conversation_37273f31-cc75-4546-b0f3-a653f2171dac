
-- --------------------------------------------------------

--
-- Table structure for table `database_updates`
--
-- Creation: Jun 23, 2025 at 08:30 PM
--

CREATE TABLE `database_updates` (
  `id` int(11) NOT NULL,
  `version` varchar(20) NOT NULL,
  `description` varchar(255) NOT NULL,
  `applied_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- RELATIONSHIPS FOR TABLE `database_updates`:
--

--
-- Dumping data for table `database_updates`
--

INSERT INTO `database_updates` (`id`, `version`, `description`, `applied_at`) VALUES
(1, '2.19.88', 'Fixed shows table structure to ensure start_date, end_date, and status columns exist', '2025-05-29 11:46:37'),
(2, '2.19.89', 'Created judge_assignments table if it did not exist', '2025-05-29 11:49:51'),
(3, '2.19.90', 'Modified judge_assignments table to allow NULL values for category_id', '2025-06-04 20:32:22');
