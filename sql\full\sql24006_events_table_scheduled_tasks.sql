
-- --------------------------------------------------------

--
-- Table structure for table `scheduled_tasks`
--
-- Creation: Jun 23, 2025 at 08:30 PM
--

CREATE TABLE `scheduled_tasks` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `script_path` varchar(255) NOT NULL,
  `parameters` text DEFAULT NULL,
  `current_parameters` text DEFAULT NULL,
  `trigger_type` enum('event','time','interval') NOT NULL,
  `event_name` varchar(255) DEFAULT NULL,
  `event_parameter` varchar(255) DEFAULT NULL,
  `schedule_time` time DEFAULT NULL,
  `interval_hours` int(11) DEFAULT NULL,
  `timeout_seconds` int(11) DEFAULT 900 COMMENT 'Maximum execution time in seconds',
  `last_run` datetime DEFAULT NULL,
  `last_error` text DEFAULT NULL,
  `next_run` datetime DEFAULT NULL,
  `status` enum('active','inactive','running','error') NOT NULL DEFAULT 'active',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- RELATIONSHIPS FOR TABLE `scheduled_tasks`:
--

--
-- Dumping data for table `scheduled_tasks`
--

INSERT INTO `scheduled_tasks` (`id`, `name`, `description`, `script_path`, `parameters`, `current_parameters`, `trigger_type`, `event_name`, `event_parameter`, `schedule_time`, `interval_hours`, `timeout_seconds`, `last_run`, `last_error`, `next_run`, `status`, `created_at`, `updated_at`) VALUES
(2, 'Calculate scores on Show Completion', 'Automatically calculates category winners when a show is marked as completed', '/scripts/run_all_scoring.php', '--show_id={event_parameter}', '9', 'event', 'show_completed', '', '00:00:00', 0, 900, '2025-06-09 13:22:21', NULL, NULL, 'active', '2025-06-09 13:18:48', '2025-06-09 17:22:21');
