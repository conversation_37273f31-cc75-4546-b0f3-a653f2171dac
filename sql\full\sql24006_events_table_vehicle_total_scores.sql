
-- --------------------------------------------------------

--
-- Table structure for table `vehicle_total_scores`
--
-- Creation: Jun 23, 2025 at 08:30 PM
--

CREATE TABLE `vehicle_total_scores` (
  `id` int(11) NOT NULL,
  `show_id` int(11) NOT NULL,
  `vehicle_id` int(11) NOT NULL,
  `registration_id` int(11) NOT NULL,
  `total_score` decimal(10,2) NOT NULL DEFAULT 0.00,
  `age_weight` decimal(5,2) DEFAULT NULL,
  `raw_score` decimal(10,2) DEFAULT NULL,
  `weighted_score` decimal(10,2) DEFAULT NULL,
  `normalized_score` decimal(10,2) DEFAULT NULL,
  `formula_id` int(11) DEFAULT NULL,
  `formula_name` varchar(255) DEFAULT NULL,
  `weight_multiplier` decimal(10,2) DEFAULT NULL,
  `display_number` varchar(50) DEFAULT NULL,
  `calculation_date` datetime NOT NULL DEFAULT current_timestamp(),
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- RELATIONSHIPS FOR TABLE `vehicle_total_scores`:
--

--
-- Dumping data for table `vehicle_total_scores`
--

INSERT INTO `vehicle_total_scores` (`id`, `show_id`, `vehicle_id`, `registration_id`, `total_score`, `age_weight`, `raw_score`, `weighted_score`, `normalized_score`, `formula_id`, `formula_name`, `weight_multiplier`, `display_number`, `calculation_date`, `created_at`, `updated_at`) VALUES
(1, 5, 2, 37, 80.18, 1.20, NULL, NULL, NULL, NULL, NULL, NULL, NULL, '2025-06-10 17:00:55', '2025-06-10 17:00:55', '2025-06-10 17:00:55');
