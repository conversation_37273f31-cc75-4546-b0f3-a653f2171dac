/**
 * Photo Engagement System
 * Handles likes, comments, shares, favorites, and tags
 */

class PhotoEngagement {
    constructor() {
        this.baseUrl = window.BASE_URL || '';
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadEngagementData();
    }
    
    bindEvents() {
        // Like button clicks
        document.addEventListener('click', (e) => {
            if (e.target.closest('.like-btn')) {
                e.preventDefault();
                const btn = e.target.closest('.like-btn');
                const photoId = btn.dataset.photoId;
                this.toggleLike(photoId, btn);
            }
        });
        
        // Favorite button clicks
        document.addEventListener('click', (e) => {
            if (e.target.closest('.favorite-btn')) {
                e.preventDefault();
                const btn = e.target.closest('.favorite-btn');
                const photoId = btn.dataset.photoId;
                this.toggleFavorite(photoId, btn);
            }
        });
        
        // Comment form submissions
        document.addEventListener('submit', (e) => {
            if (e.target.classList.contains('comment-form')) {
                e.preventDefault();
                this.submitComment(e.target);
            }
        });
        
        // Share button clicks
        document.addEventListener('click', (e) => {
            if (e.target.closest('.share-btn')) {
                e.preventDefault();
                const btn = e.target.closest('.share-btn');
                const photoId = btn.dataset.photoId;
                const platform = btn.dataset.platform;
                this.sharePhoto(photoId, platform);
            }
        });
        
        // Show comments toggle
        document.addEventListener('click', (e) => {
            if (e.target.closest('.show-comments-btn')) {
                e.preventDefault();
                const btn = e.target.closest('.show-comments-btn');
                const photoId = btn.dataset.photoId;
                this.toggleComments(photoId);
            }
        });

        // Edit comment button clicks
        document.addEventListener('click', (e) => {
            if (e.target.closest('.edit-comment-btn')) {
                e.preventDefault();
                const btn = e.target.closest('.edit-comment-btn');
                const commentId = btn.dataset.commentId;
                this.editComment(commentId);
            }
        });

        // Delete comment button clicks
        document.addEventListener('click', (e) => {
            if (e.target.closest('.delete-comment-btn')) {
                e.preventDefault();
                const btn = e.target.closest('.delete-comment-btn');
                const commentId = btn.dataset.commentId;
                const photoId = btn.dataset.photoId;
                this.deleteComment(commentId, photoId);
            }
        });

        // Cancel edit button clicks
        document.addEventListener('click', (e) => {
            if (e.target.closest('.cancel-edit-btn')) {
                e.preventDefault();
                const btn = e.target.closest('.cancel-edit-btn');
                const commentId = btn.dataset.commentId;
                this.cancelEdit(commentId);
            }
        });

        // Save edit button clicks
        document.addEventListener('click', (e) => {
            if (e.target.closest('.save-edit-btn')) {
                e.preventDefault();
                const btn = e.target.closest('.save-edit-btn');
                const commentId = btn.dataset.commentId;
                this.saveEdit(commentId);
            }
        });
    }
    
    async toggleLike(photoId, button) {
        if (!this.isLoggedIn()) {
            this.showLoginPrompt();
            return;
        }
        
        try {
            // Optimistic UI update
            const icon = button.querySelector('i');
            const countSpan = button.querySelector('.like-count');
            const isLiked = button.classList.contains('liked');
            
            // Toggle visual state immediately
            if (isLiked) {
                button.classList.remove('liked');
                icon.classList.remove('fas');
                icon.classList.add('far');
            } else {
                button.classList.add('liked');
                icon.classList.remove('far');
                icon.classList.add('fas');
                this.animateHeart(button);
            }
            
            const response = await fetch(`${this.baseUrl}/engagement/toggleLike`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ photo_id: photoId })
            });
            
            const data = await response.json();
            
            if (data.success) {
                // Update count
                if (countSpan) {
                    countSpan.textContent = data.like_count;
                }
                
                // Update button state to match server response
                if (data.liked) {
                    button.classList.add('liked');
                    icon.classList.remove('far');
                    icon.classList.add('fas');
                } else {
                    button.classList.remove('liked');
                    icon.classList.remove('fas');
                    icon.classList.add('far');
                }
            } else {
                // Revert optimistic update on error
                if (isLiked) {
                    button.classList.add('liked');
                    icon.classList.remove('far');
                    icon.classList.add('fas');
                } else {
                    button.classList.remove('liked');
                    icon.classList.remove('fas');
                    icon.classList.add('far');
                }
                this.showError('Failed to update like');
            }
        } catch (error) {
            console.error('Like error:', error);
            this.showError('Failed to update like');
        }
    }
    
    async toggleFavorite(photoId, button) {
        if (!this.isLoggedIn()) {
            this.showLoginPrompt();
            return;
        }
        
        try {
            const icon = button.querySelector('i');
            const isFavorited = button.classList.contains('favorited');
            
            // Optimistic UI update
            if (isFavorited) {
                button.classList.remove('favorited');
                icon.classList.remove('fas');
                icon.classList.add('far');
            } else {
                button.classList.add('favorited');
                icon.classList.remove('far');
                icon.classList.add('fas');
                this.animateBookmark(button);
            }
            
            const response = await fetch(`${this.baseUrl}/engagement/toggleFavorite`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ photo_id: photoId })
            });
            
            const data = await response.json();
            
            if (data.success) {
                // Update button state to match server response
                if (data.favorited) {
                    button.classList.add('favorited');
                    icon.classList.remove('far');
                    icon.classList.add('fas');
                } else {
                    button.classList.remove('favorited');
                    icon.classList.remove('fas');
                    icon.classList.add('far');
                }
            } else {
                // Revert optimistic update on error
                if (isFavorited) {
                    button.classList.add('favorited');
                    icon.classList.remove('far');
                    icon.classList.add('fas');
                } else {
                    button.classList.remove('favorited');
                    icon.classList.remove('fas');
                    icon.classList.add('far');
                }
                this.showError('Failed to update favorite');
            }
        } catch (error) {
            console.error('Favorite error:', error);
            this.showError('Failed to update favorite');
        }
    }
    
    async submitComment(form) {
        if (!this.isLoggedIn()) {
            this.showLoginPrompt();
            return;
        }
        
        const formData = new FormData(form);
        const photoId = formData.get('photo_id');
        const comment = formData.get('comment').trim();
        const parentCommentId = formData.get('parent_comment_id') || null;
        
        if (!comment) {
            this.showError('Please enter a comment');
            return;
        }
        
        try {
            const submitBtn = form.querySelector('button[type="submit"]');
            const originalText = submitBtn.textContent;
            submitBtn.disabled = true;
            submitBtn.textContent = 'Posting...';
            
            const response = await fetch(`${this.baseUrl}/engagement/addComment`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    photo_id: photoId,
                    comment: comment,
                    parent_comment_id: parentCommentId
                })
            });
            
            const data = await response.json();

            if (data.success) {
                // Clear form
                form.reset();

                // Add comment to UI
                this.addCommentToUI(data.comment, parentCommentId);

                // Update comment count
                this.updateCommentCount(photoId);

                this.showSuccess('Comment posted!');
            } else {
                this.showError(data.error || 'Failed to post comment');
            }
            
            submitBtn.disabled = false;
            submitBtn.textContent = originalText;
            
        } catch (error) {
            console.error('Comment error:', error);
            this.showError('Failed to post comment');
        }
    }
    
    async sharePhoto(photoId, platform) {
        try {
            // Track the share
            await fetch(`${this.baseUrl}/engagement/trackShare`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    photo_id: photoId,
                    platform: platform
                })
            });

            // Get full share URL with domain
            const shareUrl = this.getFullShareUrl(photoId);

            // Handle different platforms
            switch (platform) {
                case 'facebook':
                    this.shareToFacebook(shareUrl);
                    break;
                case 'twitter':
                    this.shareToTwitter(shareUrl);
                    break;
                case 'whatsapp':
                    this.shareToWhatsApp(shareUrl);
                    break;
                case 'copy_link':
                    this.copyToClipboard(shareUrl);
                    break;
                default:
                    this.copyToClipboard(shareUrl);
            }

        } catch (error) {
            console.error('Share error:', error);
            this.showError('Failed to share photo');
        }
    }

    getFullShareUrl(photoId) {
        // Ensure we have a full URL with protocol and domain
        let baseUrl = this.baseUrl;

        // If baseUrl is relative, make it absolute
        if (baseUrl.startsWith('/') || !baseUrl.includes('://')) {
            const protocol = window.location.protocol;
            const host = window.location.host;
            baseUrl = `${protocol}//${host}${baseUrl.startsWith('/') ? baseUrl : '/' + baseUrl}`;
        }

        // Remove trailing slash and add photo share path
        baseUrl = baseUrl.replace(/\/$/, '');
        return `${baseUrl}/photo/share/${photoId}`;
    }
    
    // Helper methods
    shareToFacebook(url) {
        const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;
        window.open(facebookUrl, '_blank', 'width=600,height=400');
    }

    shareToTwitter(url) {
        const text = 'Check out this awesome photo from the car show!';
        const twitterUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}`;
        window.open(twitterUrl, '_blank', 'width=600,height=400');
    }

    shareToWhatsApp(url) {
        const text = 'Check out this awesome photo from the car show! ';
        const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(text + url)}`;
        window.open(whatsappUrl, '_blank');
    }

    async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            this.showSuccess('Link copied to clipboard!');
        } catch (error) {
            // Fallback for older browsers
            const textArea = document.createElement('textarea');
            textArea.value = text;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            this.showSuccess('Link copied to clipboard!');
        }
    }

    animateHeart(button) {
        button.style.transform = 'scale(1.2)';
        button.style.transition = 'transform 0.2s ease';
        setTimeout(() => {
            button.style.transform = 'scale(1)';
        }, 200);
    }

    animateBookmark(button) {
        button.style.transform = 'scale(1.1)';
        button.style.transition = 'transform 0.2s ease';
        setTimeout(() => {
            button.style.transform = 'scale(1)';
        }, 200);
    }

    async toggleComments(photoId) {
        const commentsContainer = document.querySelector(`#comments-${photoId}`);

        if (commentsContainer.style.display === 'none' || !commentsContainer.style.display) {
            // Load and show comments
            await this.loadComments(photoId);
            commentsContainer.style.display = 'block';
        } else {
            // Hide comments
            commentsContainer.style.display = 'none';
        }
    }

    async loadComments(photoId) {
        try {
            const response = await fetch(`${this.baseUrl}/engagement/getComments/${photoId}`);
            const data = await response.json();

            if (data.success) {
                this.renderComments(photoId, data.comments);
            }
        } catch (error) {
            console.error('Load comments error:', error);
        }
    }

    renderComments(photoId, comments) {
        const container = document.querySelector(`#comments-list-${photoId}`);
        if (!container) return;

        container.innerHTML = '';

        comments.forEach(comment => {
            const commentElement = this.createCommentElement(comment);
            container.appendChild(commentElement);
        });
    }

    createCommentElement(comment) {
        const div = document.createElement('div');
        div.className = 'comment mb-3';

        // Create the main flex container
        const flexContainer = document.createElement('div');
        flexContainer.className = 'd-flex';

        // Create avatar element
        const avatarElement = this.createAvatarElement(comment.user_name, comment.profile_image_url);
        flexContainer.appendChild(avatarElement);

        // Create content container
        const contentContainer = document.createElement('div');
        contentContainer.className = 'flex-grow-1';

        // Create comment content with edit/delete options
        const editedText = comment.is_edited ? ' <small class="text-muted">(edited)</small>' : '';
        const actionButtons = this.createCommentActionButtons(comment);

        contentContainer.innerHTML = `
            <div class="comment-content bg-light rounded p-2" id="comment-content-${comment.id}">
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <strong>${comment.user_name}</strong>${editedText}
                        <p class="mb-0 comment-text" id="comment-text-${comment.id}">${this.formatComment(comment.comment)}</p>
                    </div>
                    ${actionButtons}
                </div>
            </div>
            <div class="d-flex justify-content-between align-items-center">
                <small class="text-muted">${comment.formatted_date || this.formatDate(comment.created_at)}</small>
                ${comment.is_edited && comment.formatted_edited_date ?
                    `<small class="text-muted">Edited ${comment.formatted_edited_date}</small>` : ''}
            </div>
            ${comment.replies ? comment.replies.map(reply => this.createCommentElement(reply)).join('') : ''}
        `;

        flexContainer.appendChild(contentContainer);
        div.appendChild(flexContainer);

        return div;
    }

    createAvatarElement(userName, profileImageUrl) {
        if (profileImageUrl && profileImageUrl !== '' && profileImageUrl !== null) {
            // Try to use profile image first
            const img = document.createElement('img');
            img.src = profileImageUrl;
            img.alt = userName;
            img.className = 'rounded-circle me-2';
            img.style.cssText = 'width: 32px; height: 32px; object-fit: cover; border: 1px solid #dee2e6;';

            // If image fails to load, replace with initials
            img.onerror = () => {
                const initialsDiv = this.createInitialsAvatar(userName);
                img.parentNode.replaceChild(initialsDiv, img);
            };

            return img;
        } else {
            // No profile image, use initials directly
            return this.createInitialsAvatar(userName);
        }
    }

    createInitialsAvatar(userName) {
        const div = document.createElement('div');
        div.className = 'rounded-circle me-2 d-flex align-items-center justify-content-center';
        div.style.cssText = 'width: 32px; height: 32px; background: linear-gradient(45deg, #007bff, #0056b3); color: white; font-weight: bold; font-size: 12px;';
        div.textContent = this.getInitials(userName);
        return div;
    }

    getInitials(name) {
        if (!name) return '?';
        const words = name.trim().split(' ');
        if (words.length === 1) {
            return words[0].charAt(0).toUpperCase();
        }
        return (words[0].charAt(0) + words[words.length - 1].charAt(0)).toUpperCase();
    }

    createCommentActionButtons(comment) {
        if (!comment.can_edit && !comment.can_delete) {
            return '';
        }

        let buttons = '<div class="btn-group btn-group-sm" role="group">';

        if (comment.can_edit) {
            buttons += `<button class="btn btn-link btn-sm p-1 edit-comment-btn"
                               data-comment-id="${comment.id}"
                               title="Edit comment">
                            <i class="fas fa-edit text-muted" style="font-size: 0.75rem;"></i>
                        </button>`;
        }

        if (comment.can_delete) {
            buttons += `<button class="btn btn-link btn-sm p-1 delete-comment-btn"
                               data-comment-id="${comment.id}"
                               data-photo-id="${comment.photo_id}"
                               title="Delete comment">
                            <i class="fas fa-trash text-muted" style="font-size: 0.75rem;"></i>
                        </button>`;
        }

        buttons += '</div>';
        return buttons;
    }

    async editComment(commentId) {
        const commentTextElement = document.getElementById(`comment-text-${commentId}`);
        const currentText = commentTextElement.textContent;

        // Create edit form
        const editForm = `
            <div class="edit-form mt-2">
                <div class="input-group input-group-sm">
                    <input type="text"
                           class="form-control"
                           id="edit-input-${commentId}"
                           value="${currentText.replace(/"/g, '&quot;')}"
                           maxlength="500">
                    <button class="btn btn-success save-edit-btn"
                            data-comment-id="${commentId}"
                            type="button">
                        <i class="fas fa-check"></i>
                    </button>
                    <button class="btn btn-secondary cancel-edit-btn"
                            data-comment-id="${commentId}"
                            type="button">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        `;

        // Hide original text and show edit form
        commentTextElement.style.display = 'none';
        commentTextElement.insertAdjacentHTML('afterend', editForm);

        // Focus on input
        document.getElementById(`edit-input-${commentId}`).focus();
    }

    cancelEdit(commentId) {
        const commentTextElement = document.getElementById(`comment-text-${commentId}`);
        const editForm = commentTextElement.parentNode.querySelector('.edit-form');

        // Remove edit form and show original text
        if (editForm) {
            editForm.remove();
        }
        commentTextElement.style.display = 'block';
    }

    async saveEdit(commentId) {
        const input = document.getElementById(`edit-input-${commentId}`);
        const newText = input.value.trim();

        if (!newText) {
            this.showError('Comment cannot be empty');
            return;
        }

        try {
            const response = await fetch(`${this.baseUrl}/engagement/editComment`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    comment_id: commentId,
                    comment: newText
                })
            });

            const data = await response.json();

            if (data.success) {
                // Update the comment text
                const commentTextElement = document.getElementById(`comment-text-${commentId}`);
                commentTextElement.innerHTML = this.formatComment(newText);

                // Add edited indicator
                const userNameElement = commentTextElement.parentNode.querySelector('strong');
                if (!userNameElement.innerHTML.includes('(edited)')) {
                    userNameElement.innerHTML += ' <small class="text-muted">(edited)</small>';
                }

                this.cancelEdit(commentId);
                this.showSuccess('Comment updated successfully');
            } else {
                this.showError(data.error || 'Failed to update comment');
            }
        } catch (error) {
            console.error('Edit comment error:', error);
            this.showError('Failed to update comment');
        }
    }

    async deleteComment(commentId, photoId) {
        if (!confirm('Are you sure you want to delete this comment?')) {
            return;
        }

        try {
            const response = await fetch(`${this.baseUrl}/engagement/deleteComment`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    comment_id: commentId
                })
            });

            const data = await response.json();

            if (data.success) {
                // Remove comment from UI
                const commentElement = document.getElementById(`comment-content-${commentId}`).closest('.comment');
                if (commentElement) {
                    commentElement.remove();
                }

                // Update comment count
                this.updateCommentCount(photoId, -1);

                this.showSuccess('Comment deleted successfully');
            } else {
                this.showError(data.error || 'Failed to delete comment');
            }
        } catch (error) {
            console.error('Delete comment error:', error);
            this.showError('Failed to delete comment');
        }
    }

    formatComment(comment) {
        // Convert @mentions to links
        return comment.replace(/@(\w+)/g, '<span class="text-primary">@$1</span>');
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diff = now - date;

        if (diff < 60000) return 'Just now';
        if (diff < 3600000) return `${Math.floor(diff / 60000)}m ago`;
        if (diff < 86400000) return `${Math.floor(diff / 3600000)}h ago`;
        return date.toLocaleDateString();
    }

    addCommentToUI(comment, parentCommentId) {
        // Find the comments list for this photo
        const photoId = comment.photo_id;
        const commentsList = document.querySelector(`#comments-list-${photoId}`);

        if (!commentsList) {
            return;
        }

        // Create the comment element
        const commentElement = this.createCommentElement(comment);

        if (parentCommentId) {
            // This is a reply - find the parent comment and add after it
            const parentComment = commentsList.querySelector(`#comment-content-${parentCommentId}`);
            if (parentComment) {
                const parentContainer = parentComment.closest('.comment');
                parentContainer.appendChild(commentElement);
            } else {
                // Fallback: add to end of list
                commentsList.appendChild(commentElement);
            }
        } else {
            // This is a top-level comment - add to end of list
            commentsList.appendChild(commentElement);
        }
    }

    updateCommentCount(photoId, change = 1) {
        // Update comment count display
        const countElement = document.querySelector(`#comment-count-${photoId}`);
        if (countElement) {
            const currentCount = parseInt(countElement.textContent) || 0;
            const newCount = Math.max(0, currentCount + change);
            countElement.textContent = newCount;
        }
    }

    async loadEngagementData() {
        // Load engagement data for all photos on the page
        const photoElements = document.querySelectorAll('[data-photo-id]');

        for (const element of photoElements) {
            const photoId = element.dataset.photoId;
            await this.loadPhotoEngagement(photoId);
        }
    }

    async loadPhotoEngagement(photoId) {
        try {
            const response = await fetch(`${this.baseUrl}/engagement/getEngagementData/${photoId}`);
            const data = await response.json();

            if (data.success) {
                this.updateEngagementUI(photoId, data.stats, data.user_engagement);
            }
        } catch (error) {
            console.error('Load engagement error:', error);
        }
    }

    updateEngagementUI(photoId, stats, userEngagement) {
        // Update like button
        const likeBtn = document.querySelector(`[data-photo-id="${photoId}"] .like-btn`);
        if (likeBtn) {
            const icon = likeBtn.querySelector('i');
            const count = likeBtn.querySelector('.like-count');

            if (userEngagement.liked) {
                likeBtn.classList.add('liked');
                icon.classList.remove('far');
                icon.classList.add('fas');
            }

            if (count) {
                count.textContent = stats.likes_count || 0;
            }
        }

        // Update favorite button
        const favoriteBtn = document.querySelector(`[data-photo-id="${photoId}"] .favorite-btn`);
        if (favoriteBtn) {
            const icon = favoriteBtn.querySelector('i');

            if (userEngagement.favorited) {
                favoriteBtn.classList.add('favorited');
                icon.classList.remove('far');
                icon.classList.add('fas');
            }
        }

        // Update comment count
        const commentCount = document.querySelector(`#comment-count-${photoId}`);
        if (commentCount) {
            commentCount.textContent = stats.comments_count || 0;
        }
    }

    isLoggedIn() {
        // Check if user is logged in (implementation depends on your auth system)
        return document.body.dataset.loggedIn === 'true' ||
               document.querySelector('meta[name="user-logged-in"]')?.content === 'true';
    }

    showLoginPrompt() {
        if (window.pwaFeatures) {
            window.pwaFeatures.showNotification('Login Required', 'Please log in to interact with photos', 'info');
        } else {
            alert('Please log in to interact with photos');
        }
    }

    showSuccess(message) {
        if (window.pwaFeatures) {
            window.pwaFeatures.showNotification('Success', message, 'success');
        } else {
            console.log('Success:', message);
        }
    }

    showError(message) {
        if (window.pwaFeatures) {
            window.pwaFeatures.showNotification('Error', message, 'error');
        } else {
            console.error('Error:', message);
        }
    }
}

// Initialize engagement system when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.photoEngagement = new PhotoEngagement();
});
