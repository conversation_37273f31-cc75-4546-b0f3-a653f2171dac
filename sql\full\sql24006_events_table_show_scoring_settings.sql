
-- --------------------------------------------------------

--
-- Table structure for table `show_scoring_settings`
--
-- Creation: Jun 23, 2025 at 08:30 PM
--

CREATE TABLE `show_scoring_settings` (
  `id` int(11) NOT NULL,
  `show_id` int(11) NOT NULL,
  `formula_id` int(11) DEFAULT NULL,
  `custom_formula` text DEFAULT NULL,
  `weight_multiplier` decimal(5,2) DEFAULT 100.00,
  `use_age_weight` tinyint(1) DEFAULT 1,
  `age_weight_multiplier` decimal(5,2) DEFAULT 1.00,
  `normalize_scores` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- RELATIONSHIPS FOR TABLE `show_scoring_settings`:
--

--
-- Dumping data for table `show_scoring_settings`
--

INSERT INTO `show_scoring_settings` (`id`, `show_id`, `formula_id`, `custom_formula`, `weight_multiplier`, `use_age_weight`, `age_weight_multiplier`, `normalize_scores`, `created_at`, `updated_at`) VALUES
(1, 5, 1, '', 100.00, 1, 1.00, 1, '2025-06-05 13:13:44', '2025-06-05 18:14:14'),
(2, 7, 1, NULL, 100.00, 1, 1.00, 1, '2025-06-08 13:33:42', '2025-06-08 13:33:42'),
(3, 8, 1, NULL, 100.00, 1, 1.00, 1, '2025-06-08 13:37:24', '2025-06-08 13:37:24'),
(4, 9, 1, '', 100.00, 1, 1.00, 1, '2025-06-08 13:38:33', '2025-06-08 17:00:30'),
(5, 11, 1, NULL, 100.00, 1, 1.00, 1, '2025-06-09 00:01:07', '2025-06-09 00:01:07'),
(6, 12, 1, NULL, 100.00, 1, 1.00, 1, '2025-06-09 00:02:45', '2025-06-09 00:02:45'),
(7, 13, 1, NULL, 100.00, 1, 1.00, 1, '2025-06-09 00:03:01', '2025-06-09 00:03:01');
