<?php
/**
 * Image Editor Controller
 * 
 * This controller handles all image editing related functionality.
 */
class ImageEditorController extends Controller {
    private $imageEditorModel;
    private $auth;
    private $db;
    
    /**
     * Generate CSRF token
     * 
     * @return string CSRF token
     */
    private function generateCsrfToken() {
        // Use the global function from csrf_helper.php
        return generateCsrfToken();
    }
    
    /**
     * Verify CSRF token
     * 
     * @param string $token Optional token to verify
     * @param string $source Source of the token (post, get, header)
     * @return bool
     */
    protected function verifyCsrfToken($token = null, $source = 'post') {
        // Use the global function from csrf_helper.php
        return verifyCsrfToken($token, $source);
    }
    
    /**
     * Constructor
     */
    public function __construct() {
        // Check if user is logged in
        $this->auth = new Auth();
        if (!$this->auth->isLoggedIn()) {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Initialize database connection
        $this->db = new Database();
        
        $this->imageEditorModel = $this->model('ImageEditorModel');
        
        // Store referer URL in session for back button
        $this->storeRefererUrl();
    }
    
    /**
     * Store referer URL in session for back button
     */
    private function storeRefererUrl() {
        if (isset($_SERVER['HTTP_REFERER'])) {
            $referer = $_SERVER['HTTP_REFERER'];
            
            // Only store referer if it's from our site and not from another image editor page
            if (strpos($referer, BASE_URL) === 0 && 
                strpos($referer, BASE_URL . '/image_editor/') === false) {
                $_SESSION['image_editor_referer'] = $referer;
            }
        }
    }
    
    /**
     * Default index method
     */
    public function index() {
        $this->redirect('image_editor/browse');
    }
    
    /**
     * Form designer image selector
     * 
     * @param int $showId Show ID (optional)
     */
    public function form_selector($showId = null) {
        // Check if this is an AJAX request or if it's loaded in an iframe
        $isAjax = isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
                  strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
                  
        // Also treat as AJAX if iframe parameter is set
        if (isset($_GET['iframe']) && $_GET['iframe'] == '1') {
            $isAjax = true;
        }
        
        // Get images based on show ID if provided
        $images = [];
        if ($showId) {
            $images = $this->imageEditorModel->getImagesByEntity('show', $showId);
        } else {
            // Get all images if no show ID provided
            $images = $this->imageEditorModel->getRecentImages(50); // Limit to 50 most recent
        }
        
        // Generate CSRF token
        $csrfToken = $this->generateCsrfToken();
        
        $data = [
            'title' => 'Select Images',
            'images' => $images,
            'show_id' => $showId,
            'is_ajax' => $isAjax,
            'csrf_token' => $csrfToken
        ];
        
        // The is_ajax flag is already set in the data array
        // Just use a single view call for both cases
        $this->view('image_editor/form_selector', $data);
    }
    
    /**
     * AJAX image upload for form designer
     */
    public function upload_ajax() {
        // Debug logging for AJAX detection
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log("ImageEditorController::upload_ajax - X-Requested-With header: " . ($_SERVER['HTTP_X_REQUESTED_WITH'] ?? 'not set'));
            error_log("ImageEditorController::upload_ajax - Request method: " . $_SERVER['REQUEST_METHOD']);
        }

        // Check if this is an AJAX request
        $isAjax = isset($_SERVER['HTTP_X_REQUESTED_WITH']) &&
                  strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';

        if (!$isAjax) {
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("ImageEditorController::upload_ajax - Not an AJAX request, redirecting to regular upload");
            }
            $this->redirect('image_editor/upload');
            return;
        }
        
        // Verify CSRF token
        if (!verifyCsrfToken($_POST[CSRF_TOKEN_NAME] ?? '')) {
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("ImageEditorController::upload_ajax - CSRF token verification failed");
                error_log("ImageEditorController::upload_ajax - Token received: " . ($_POST[CSRF_TOKEN_NAME] ?? 'not set'));
                error_log("ImageEditorController::upload_ajax - Session token: " . ($_SESSION[CSRF_TOKEN_NAME] ?? 'not set'));
            }
            echo json_encode([
                'success' => false,
                'message' => 'Invalid security token. Please refresh the page and try again.'
            ]);
            return;
        }
        
        // Check if file was uploaded
        if (!isset($_FILES['image']) || $_FILES['image']['error'] !== UPLOAD_ERR_OK) {
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("ImageEditorController::upload_ajax - File upload check failed");
                error_log("ImageEditorController::upload_ajax - FILES array: " . print_r($_FILES, true));
                if (isset($_FILES['image'])) {
                    error_log("ImageEditorController::upload_ajax - Upload error code: " . $_FILES['image']['error']);
                }
            }
            echo json_encode([
                'success' => false,
                'message' => 'No image uploaded or upload error occurred.'
            ]);
            return;
        }
        
        // Get form data
        $title = $_POST['title'] ?? 'Untitled Image';
        $description = $_POST['description'] ?? '';
        $entityType = $_POST['entity_type'] ?? 'form';
        $entityId = $_POST['entity_id'] ?? 0;

        // Determine upload directory based on entity type (same logic as regular upload)
        if ($entityType == 'event_photo') {
            $uploadDir = 'uploads/event_photos/';
        } else {
            $uploadDir = 'uploads/' . $entityType . 's/';
        }

        // Debug logging for upload attempts
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log("ImageEditorController::upload_ajax - Entity type: $entityType, Entity ID: $entityId, Upload dir: $uploadDir");
        }

        // For events, enforce one-image policy (delete existing images before uploading new one)
        if ($entityType == 'event') {
            // Get existing images for this event
            $existingImages = $this->imageEditorModel->getImagesByEntity('event', $entityId);

            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("ImageEditorController::upload_ajax - Found " . count($existingImages) . " existing images for event $entityId");
            }

            // Delete each existing image
            foreach ($existingImages as $image) {
                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                    error_log("ImageEditorController::upload_ajax - Deleting existing event image ID: " . $image->id);
                }
                $this->imageEditorModel->deleteImage($image->id);
            }
        }

        // Upload the image using the correct method
        $result = $this->imageEditorModel->processImageUpload($_FILES['image'], $entityType, $entityId, $uploadDir, $_SESSION['user_id'] ?? null);

        if ($result !== false) {
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("ImageEditorController::upload_ajax - Upload successful, image ID: " . $result['id']);
            }
            echo json_encode([
                'success' => true,
                'message' => 'Image uploaded successfully.',
                'image' => $result
            ]);
        } else {
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("ImageEditorController::upload_ajax - Upload failed for entity_type: $entityType, entity_id: $entityId");
            }
            echo json_encode([
                'success' => false,
                'message' => 'Failed to upload image. Please check file format and size.'
            ]);
        }
    }

    /**
     * Delete image via AJAX
     */
    public function deleteImage() {
        // Check if this is an AJAX request
        $isAjax = isset($_SERVER['HTTP_X_REQUESTED_WITH']) &&
                  strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';

        if (!$isAjax) {
            $this->redirect('image_editor/browse');
            return;
        }

        // Verify CSRF token
        if (!verifyCsrfToken($_POST[CSRF_TOKEN_NAME] ?? '')) {
            echo json_encode([
                'success' => false,
                'message' => 'Invalid security token. Please refresh the page and try again.'
            ]);
            return;
        }

        // Get image ID
        $imageId = isset($_POST['image_id']) ? intval($_POST['image_id']) : 0;

        if ($imageId <= 0) {
            echo json_encode([
                'success' => false,
                'message' => 'Invalid image ID.'
            ]);
            return;
        }

        // Get image details for permission check
        $image = $this->imageEditorModel->getImageById($imageId);

        if (!$image) {
            echo json_encode([
                'success' => false,
                'message' => 'Image not found.'
            ]);
            return;
        }

        // Check permissions - user must own the image or be admin
        $userId = $_SESSION['user_id'] ?? 0;
        $isAdmin = isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin';

        if ($image->user_id != $userId && !$isAdmin) {
            echo json_encode([
                'success' => false,
                'message' => 'You do not have permission to delete this image.'
            ]);
            return;
        }

        // Delete the image
        if ($this->imageEditorModel->deleteImage($imageId)) {
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("ImageEditorController::deleteImage - Successfully deleted image ID: $imageId");
            }
            echo json_encode([
                'success' => true,
                'message' => 'Image deleted successfully.'
            ]);
        } else {
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("ImageEditorController::deleteImage - Failed to delete image ID: $imageId");
            }
            echo json_encode([
                'success' => false,
                'message' => 'Failed to delete image.'
            ]);
        }
    }

    /**
     * Browse images
     * 
     * @param string|null $entityType Entity type (show, vehicle) for permission filtering
     * @param int|null $entityId Entity ID for permission filtering
     */
    public function browse($entityType = null, $entityId = null) {
        // Get query parameters
        $page = isset($_GET['page']) ? intval($_GET['page']) : 1;
        $search = isset($_GET['search']) ? trim($_GET['search']) : '';
        $type = isset($_GET['type']) ? trim($_GET['type']) : '';
        $showMyImages = isset($_GET['my_images']) && $_GET['my_images'] == '1';
        
        // Set items per page
        $items_per_page = 12;
        
        // Get current user ID and role
        $userId = $this->auth->getCurrentUserId();
        $userRole = $this->auth->getCurrentUserRole();
        
        // Check permissions based on entity type and ID
        $filterUserId = null;
        $filterEntityType = null;
        $filterEntityId = null;
        
        // If "Show only my images" is enabled for admin, filter by current user ID
        // This is a HARD filter that will override any other filtering logic
        $forceUserFilter = false;
        
        if ($showMyImages && $userRole === 'admin') {
            $filterUserId = $userId;
            $forceUserFilter = true; // Mark that we're forcing the user filter
            
            // Add debug info to session for troubleshooting (only in development)
            if (defined('ENVIRONMENT') && ENVIRONMENT === 'development') {
                $_SESSION['debug_filter'] = [
                    'enabled' => true,
                    'user_id' => $userId,
                    'force_filter' => true,
                    'timestamp' => gmdate('Y-m-d H:i:s')
                ];
            }
        } else {
            if (defined('ENVIRONMENT') && ENVIRONMENT === 'development') {
                $_SESSION['debug_filter'] = [
                    'enabled' => false,
                    'force_filter' => false,
                    'timestamp' => gmdate('Y-m-d H:i:s')
                ];
            }
        }
        
        // If entity type and ID are provided, check permissions
        if ($entityType !== null && $entityId !== null) {
            // For vehicle images, check if user owns the vehicle
            if ($entityType === 'vehicle') {
                $vehicleModel = $this->model('VehicleModel');
                $vehicle = $vehicleModel->getVehicleById($entityId);
                
                if ($vehicle) {
                    // If user is the owner or admin, allow access
                    if ($vehicle->owner_id == $userId || $userRole === 'admin') {
                        $filterEntityType = $entityType;
                        $filterEntityId = $entityId;
                    } else {
                        // User doesn't have permission to view this vehicle's images
                        $this->redirect('home/access_denied');
                        return;
                    }
                } else {
                    // Vehicle not found
                    $this->redirect('home/not_found');
                    return;
                }
            }
            
            // For show images, check if user is coordinator or admin
            if ($entityType === 'show') {
                $showModel = $this->model('ShowModel');
                $show = $showModel->getShowById($entityId);
                
                if ($show) {
                    // If user is coordinator, admin, or has permission for this show, allow access
                    if ($show->coordinator_id == $userId || $userRole === 'admin' || $userRole === 'coordinator') {
                        $filterEntityType = $entityType;
                        $filterEntityId = $entityId;
                    } else {
                        // User doesn't have permission to view this show's images
                        $this->redirect('home/access_denied');
                        return;
                    }
                } else {
                    // Show not found
                    $this->redirect('home/not_found');
                    return;
                }
            }
        } else {
            // No entity specified, apply default permissions
            
            // If we're forcing the user filter (admin with "Show only my images" enabled),
            // then $filterUserId is already set and we don't need to do anything here
            
            // Otherwise, apply the default logic: admins see all, others see only their own
            if (!$forceUserFilter) {
                $filterUserId = ($userRole === 'admin') ? null : $userId;
            }
        }
        
        // Get images with pagination
        $result = $this->imageEditorModel->getImages(
            $page, 
            $items_per_page, 
            $search, 
            $type, 
            $filterUserId,
            $filterEntityType,
            $filterEntityId
        );
        
        $data = [
            'title' => 'Browse Images',
            'images' => $result['images'],
            'pagination' => (object) [
                'current_page' => $page,
                'total_pages' => $result['total_pages'],
                'total_items' => $result['total_items']
            ],
            'search' => $search,
            'type' => $type,
            'csrf_token' => $this->generateCsrfToken(),
            'user_role' => $userRole,
            'entity_type' => $entityType,
            'entity_id' => $entityId,
            'show_my_images' => $showMyImages
        ];
        
        $this->view('image_editor/browse', $data);
    }
    
    /**
     * Vehicle image editor
     * 
     * @param int $vehicleId Vehicle ID
     */
    public function vehicle($vehicleId) {
        // Check if user is logged in
        if (!isset($_SESSION['user_id'])) {
            $this->redirect('users/login');
            return;
        }
        
        // Get current user ID and role
        $userId = $this->auth->getCurrentUserId();
        $userRole = $this->auth->getCurrentUserRole();
        
        // Load vehicle model
        $vehicleModel = $this->model('VehicleModel');
        
        // Get vehicle
        $vehicle = $vehicleModel->getVehicleById($vehicleId);
        
        // Check if vehicle exists
        if (!$vehicle) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user has permission to view this vehicle's images
        if ($vehicle->owner_id != $userId && $userRole != 'admin') {
            $this->redirect('home/access_denied');
            return;
        }
        
        // Get vehicle images
        $images = $this->imageEditorModel->getImagesByEntity('vehicle', $vehicleId);
        
        $data = [
            'title' => 'Vehicle Image Editor',
            'vehicle' => $vehicle,
            'images' => $images,
            'csrf_token' => $this->generateCsrfToken()
        ];
        
        $this->view('image_editor/vehicle', $data);
    }
    
    /**
     * Set primary image
     * 
     * @param int $imageId Image ID
     * @param string|null $entityType Entity type (optional)
     * @param int|null $entityId Entity ID (optional)
     */
    public function setPrimary($imageId, $entityType = null, $entityId = null) {
        // Check if user is logged in
        if (!isset($_SESSION['user_id'])) {
            $this->redirect('users/login');
            return;
        }
        
        // Get image to determine entity type and ID if not provided
        $image = $this->imageEditorModel->getImageById($imageId);
        
        if (!$image) {
            // Image not found, redirect to browse page
            $this->redirect('image_editor/browse');
            return;
        }
        
        // Use image entity type and ID if not provided
        if ($entityType === null) {
            $entityType = $image->entity_type;
        }
        
        if ($entityId === null) {
            $entityId = $image->entity_id;
        }
        
        // Set primary image in the images table
        $result = $this->imageEditorModel->setPrimaryImage($imageId);
        
        // If entity type is vehicle, update vehicle primary image
        if ($entityType == 'vehicle') {
            // Load vehicle model
            $vehicleModel = $this->model('VehicleModel');
            
            // Update vehicle primary image
            $updateResult = $vehicleModel->updateVehiclePrimaryImage($entityId, $image->file_name);
            
            if (!$updateResult) {
                // Log error but continue
                error_log('Failed to update vehicle primary image for vehicle ID: ' . $entityId);
            }
        }
        
        // If entity type is show, update show featured image ID
        if ($entityType == 'show') {
            // Load show model
            $showModel = $this->model('ShowModel');
            
            // Update show featured image ID using the dedicated method
            $updateResult = $showModel->updateFeaturedImage($entityId, $imageId);
            
            if (!$updateResult) {
                // Log error but continue
                error_log('Failed to update show featured image ID for show ID: ' . $entityId);
            }
        }
        
        // Set a success flash message
        if (function_exists('setFlashMessage')) {
            setFlashMessage('success', 'Primary image has been updated successfully.');
        }
        
        // Redirect back
        $this->redirect('image_editor/' . $entityType . '/' . $entityId);
    }
    
    /**
     * Show image editor
     * 
     * @param int $showId Show ID
     */
    public function show($showId) {
        // Check if user is logged in and has admin or coordinator role
        if (!isset($_SESSION['user_id']) || 
            ($_SESSION['user_role'] != 'admin' && $_SESSION['user_role'] != 'coordinator')) {
            $this->redirect('users/login');
            return;
        }
        
        // Load show model
        $showModel = $this->model('ShowModel');
        
        // Get show
        $show = $showModel->getShowById($showId);
        
        // Check if show exists
        if (!$show) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Get show images
        $images = $this->imageEditorModel->getImagesByEntity('show', $showId);
        
        $data = [
            'title' => 'Show Image Editor',
            'show' => $show,
            'images' => $images,
            'csrf_token' => $this->generateCsrfToken()
        ];
        
        $this->view('image_editor/show', $data);
    }
    
    /**
     * Set banner image for show
     * 
     * @param int $imageId Image ID
     * @param string|null $entityType Entity type (optional)
     * @param int|null $entityId Entity ID (optional)
     */
    public function setBanner($imageId, $entityType = null, $entityId = null) {
        // Check if user is logged in and has admin or coordinator role
        if (!isset($_SESSION['user_id']) || 
            ($_SESSION['user_role'] != 'admin' && $_SESSION['user_role'] != 'coordinator')) {
            $this->redirect('users/login');
            return;
        }
        
        // Set primary image
        $result = $this->imageEditorModel->setPrimaryImage($imageId);
        
        // Get image to determine entity type and ID if not provided
        $image = $this->imageEditorModel->getImageById($imageId);
        
        if (!$image) {
            // Image not found, redirect to browse page
            $this->redirect('image_editor/browse');
            return;
        }
        
        // Use image entity type and ID if not provided
        if ($entityType === null) {
            $entityType = $image->entity_type;
        }
        
        if ($entityId === null) {
            $entityId = $image->entity_id;
        }
        
        // If entity type is show, update show banner image
        if ($entityType == 'show') {
            // Load show model
            $showModel = $this->model('ShowModel');
            
            if ($image) {
                // Update show banner image
                $showModel->updateShowBannerImage($entityId, $image->file_name);
            }
        }
        
        // Redirect back
        $this->redirect('image_editor/' . $entityType . '/' . $entityId);
    }
    
    /**
     * Edit image
     * 
     * @param int|null $id Image ID or null to show the image selection page
     */
    public function edit($id = null) {
        // If no ID is provided, redirect to the browse page
        if ($id === null) {
            $this->redirect('image_editor/browse');
            return;
        }
        // Get image
        $image = $this->imageEditorModel->getImageById($id);

        // Debug logging
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log("ImageEditor::edit - Looking for image ID: $id");
            if ($image) {
                error_log("ImageEditor::edit - Found image: " . json_encode($image));
            } else {
                error_log("ImageEditor::edit - Image not found with ID: $id");
            }
        }

        if (!$image) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user has permission to edit this image
        $userId = $this->auth->getCurrentUserId();
        $userRole = $this->auth->getCurrentUserRole();
        
        // Allow admins to edit any image
        if ($userRole != 'admin') {
            // For vehicle images, check if user owns the vehicle
            if ($image->entity_type == 'vehicle') {
                $vehicleModel = $this->model('VehicleModel');
                $vehicle = $vehicleModel->getVehicleById($image->entity_id);
                
                if (!$vehicle || $vehicle->owner_id != $userId) {
                    $this->redirect('home/access_denied');
                    return;
                }
            }
            
            // For show images, check if user is the coordinator
            if ($image->entity_type == 'show') {
                $showModel = $this->model('ShowModel');
                $show = $showModel->getShowById($image->entity_id);

                if (!$show || ($show->coordinator_id != $userId && $userRole != 'coordinator')) {
                    $this->redirect('home/access_denied');
                    return;
                }
            }

            // For event photos, check if user uploaded the image or has appropriate role
            if ($image->entity_type == 'event_photo') {
                // Allow if user uploaded the image, or if user is admin/coordinator/judge/staff
                if ($image->user_id != $userId && !in_array($userRole, ['admin', 'coordinator', 'judge', 'staff'])) {
                    $this->redirect('home/access_denied');
                    return;
                }
            }
        }

        // Store the original referrer for navigation purposes (only if not already set)
        if (!isset($_SESSION['image_editor_original_referrer_' . $id])) {
            if (isset($_SERVER['HTTP_REFERER'])) {
                $referer = $_SERVER['HTTP_REFERER'];
                // Make sure the referer is from our site and not an image editor page
                $invalidReferrerPages = [
                    'image_editor/edit',
                    'image_editor/crop',
                    'image_editor/resize',
                    'image_editor/rotate',
                    'image_editor/filter',
                    'image_editor/text',
                    'image_editor/draw',
                    'image_editor/optimize',
                    'image_editor/delete',
                    'image_editor/browse'
                ];

                $isValidReferrer = true;
                if (strpos($referer, BASE_URL) === 0) {
                    foreach ($invalidReferrerPages as $invalidPage) {
                        if (strpos($referer, $invalidPage) !== false) {
                            $isValidReferrer = false;
                            break;
                        }
                    }
                } else {
                    $isValidReferrer = false;
                }

                if ($isValidReferrer) {
                    $_SESSION['image_editor_original_referrer_' . $id] = $referer;
                    // Debug logging
                    if (defined('DEBUG_MODE') && DEBUG_MODE) {
                        error_log("ImageEditor::edit - Stored original referrer for image $id: $referer");
                    }
                } else {
                    // Debug logging
                    if (defined('DEBUG_MODE') && DEBUG_MODE) {
                        error_log("ImageEditor::edit - Skipped invalid referrer for image $id: $referer");
                    }
                }
            }
        } else {
            // Debug logging
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("ImageEditor::edit - Original referrer already exists for image $id: " . $_SESSION['image_editor_original_referrer_' . $id]);
            }
        }

        // Get image info
        $imageInfo = getimagesize($image->file_path);
        
        $data = [
            'title' => 'Edit Image',
            'image' => $image,
            'image_url' => BASE_URL . '/' . $image->file_path,
            'width' => $imageInfo[0],
            'height' => $imageInfo[1],
            'csrf_token' => $this->generateCsrfToken(),
            'cache_buster' => time()
        ];
        
        $this->view('image_editor/edit', $data);
    }
    
    /**
     * Crop image
     * 
     * @param int|null $id Image ID or null to show the image selection page
     */
    public function crop($id = null) {
        // If no ID is provided, redirect to the browse page
        if ($id === null) {
            $this->redirect('image_editor/browse');
            return;
        }
        // Get image
        $image = $this->imageEditorModel->getImageById($id);
        
        if (!$image) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user has permission to edit this image
        $userId = $this->auth->getCurrentUserId();
        $userRole = $this->auth->getCurrentUserRole();
        
        // Allow admins to edit any image
        if ($userRole != 'admin') {
            // For vehicle images, check if user owns the vehicle
            if ($image->entity_type == 'vehicle') {
                $vehicleModel = $this->model('VehicleModel');
                $vehicle = $vehicleModel->getVehicleById($image->entity_id);
                
                if (!$vehicle || $vehicle->owner_id != $userId) {
                    $this->redirect('home/access_denied');
                    return;
                }
            }
            
            // For show images, check if user is the coordinator
            if ($image->entity_type == 'show') {
                $showModel = $this->model('ShowModel');
                $show = $showModel->getShowById($image->entity_id);
                
                if (!$show || ($show->coordinator_id != $userId && $userRole != 'coordinator')) {
                    $this->redirect('home/access_denied');
                    return;
                }
            }
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Get form data
            $x = isset($_POST['x']) ? intval($_POST['x']) : 0;
            $y = isset($_POST['y']) ? intval($_POST['y']) : 0;
            $width = isset($_POST['width']) ? intval($_POST['width']) : 0;
            $height = isset($_POST['height']) ? intval($_POST['height']) : 0;
            
            // Validate dimensions
            if ($width <= 0 || $height <= 0) {
                $this->redirect('home/error/Invalid%20dimensions');
                return;
            }
            
            // Crop image
            if ($this->imageEditorModel->cropImage($image->file_path, $x, $y, $width, $height)) {
                // Update image dimensions in database
                $imageData = [
                    'file_name' => $image->file_name,
                    'file_path' => $image->file_path,
                    'file_size' => filesize($image->file_path),
                    'file_type' => $image->file_type,
                    'width' => $width,
                    'height' => $height,
                    'is_primary' => $image->is_primary
                ];
                
                $this->imageEditorModel->updateImage($id, $imageData);
                
                // Redirect back to edit page
                $this->redirect('image_editor/edit/' . $id);
            } else {
                $this->redirect('home/error/Failed%20to%20crop%20image');
            }
        } else {
            // Get image info
            $imageInfo = getimagesize($image->file_path);
            
            $data = [
                'title' => 'Crop Image',
                'image' => $image,
                'image_url' => BASE_URL . '/' . $image->file_path,
                'width' => $imageInfo[0],
                'height' => $imageInfo[1],
                'csrf_token' => $this->generateCsrfToken()
            ];
            
            $this->view('image_editor/crop', $data);
        }
    }
    
    /**
     * Resize image
     * 
     * @param int|null $id Image ID or null to show the image selection page
     */
    public function resize($id = null) {
        // If no ID is provided, redirect to the browse page
        if ($id === null) {
            $this->redirect('image_editor/browse');
            return;
        }
        // Get image
        $image = $this->imageEditorModel->getImageById($id);
        
        if (!$image) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user has permission to edit this image
        $userId = $this->auth->getCurrentUserId();
        $userRole = $this->auth->getCurrentUserRole();
        
        // Allow admins to edit any image
        if ($userRole != 'admin') {
            // For vehicle images, check if user owns the vehicle
            if ($image->entity_type == 'vehicle') {
                $vehicleModel = $this->model('VehicleModel');
                $vehicle = $vehicleModel->getVehicleById($image->entity_id);
                
                if (!$vehicle || $vehicle->owner_id != $userId) {
                    $this->redirect('home/access_denied');
                    return;
                }
            }
            
            // For show images, check if user is the coordinator
            if ($image->entity_type == 'show') {
                $showModel = $this->model('ShowModel');
                $show = $showModel->getShowById($image->entity_id);
                
                if (!$show || ($show->coordinator_id != $userId && $userRole != 'coordinator')) {
                    $this->redirect('home/access_denied');
                    return;
                }
            }
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Get form data
            $width = isset($_POST['width']) ? intval($_POST['width']) : 0;
            $height = isset($_POST['height']) ? intval($_POST['height']) : 0;
            $crop = isset($_POST['crop']) && $_POST['crop'] == '1';
            
            // Validate dimensions
            if ($width <= 0 || $height <= 0) {
                $this->redirect('home/error/Invalid%20dimensions');
                return;
            }
            
            // Resize image
            if ($this->imageEditorModel->resizeImage($image->file_path, $width, $height, $crop)) {
                // Update image dimensions in database
                $imageData = [
                    'file_name' => $image->file_name,
                    'file_path' => $image->file_path,
                    'file_size' => filesize($image->file_path),
                    'file_type' => $image->file_type,
                    'width' => $width,
                    'height' => $height,
                    'is_primary' => $image->is_primary
                ];
                
                $this->imageEditorModel->updateImage($id, $imageData);
                
                // Redirect back to edit page
                $this->redirect('image_editor/edit/' . $id);
            } else {
                $this->redirect('home/error/Failed%20to%20resize%20image');
            }
        } else {
            // Get image info
            $imageInfo = getimagesize($image->file_path);
            
            $data = [
                'title' => 'Resize Image',
                'image' => $image,
                'image_url' => BASE_URL . '/' . $image->file_path,
                'width' => $imageInfo[0],
                'height' => $imageInfo[1],
                'csrf_token' => $this->generateCsrfToken()
            ];
            
            $this->view('image_editor/resize', $data);
        }
    }
    
    /**
     * Rotate image
     * 
     * @param int|null $id Image ID or null to show the image selection page
     */
    public function rotate($id = null) {
        // If no ID is provided, redirect to the browse page
        if ($id === null) {
            $this->redirect('image_editor/browse');
            return;
        }
        // Get image
        $image = $this->imageEditorModel->getImageById($id);
        
        if (!$image) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user has permission to edit this image
        $userId = $this->auth->getCurrentUserId();
        $userRole = $this->auth->getCurrentUserRole();
        
        // Allow admins to edit any image
        if ($userRole != 'admin') {
            // For vehicle images, check if user owns the vehicle
            if ($image->entity_type == 'vehicle') {
                $vehicleModel = $this->model('VehicleModel');
                $vehicle = $vehicleModel->getVehicleById($image->entity_id);
                
                if (!$vehicle || $vehicle->owner_id != $userId) {
                    $this->redirect('home/access_denied');
                    return;
                }
            }
            
            // For show images, check if user is the coordinator
            if ($image->entity_type == 'show') {
                $showModel = $this->model('ShowModel');
                $show = $showModel->getShowById($image->entity_id);
                
                if (!$show || ($show->coordinator_id != $userId && $userRole != 'coordinator')) {
                    $this->redirect('home/access_denied');
                    return;
                }
            }
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Get form data
            $degrees = isset($_POST['degrees']) ? intval($_POST['degrees']) : 0;
            
            // Validate degrees
            if ($degrees != 90 && $degrees != 180 && $degrees != 270) {
                $this->redirect('home/error/Invalid%20rotation%20angle');
                return;
            }
            
            // Rotate image
            if ($this->imageEditorModel->rotateImage($image->file_path, $degrees)) {
                // Get new dimensions
                $imageInfo = getimagesize($image->file_path);
                
                // Update image dimensions in database
                $imageData = [
                    'file_name' => $image->file_name,
                    'file_path' => $image->file_path,
                    'file_size' => filesize($image->file_path),
                    'file_type' => $image->file_type,
                    'width' => $imageInfo[0],
                    'height' => $imageInfo[1],
                    'is_primary' => $image->is_primary
                ];
                
                $this->imageEditorModel->updateImage($id, $imageData);
                
                // Redirect back to edit page
                $this->redirect('image_editor/edit/' . $id);
            } else {
                $this->redirect('home/error/Failed%20to%20rotate%20image');
            }
        } else {
            // Get image info
            $imageInfo = getimagesize($image->file_path);
            
            $data = [
                'title' => 'Rotate Image',
                'image' => $image,
                'image_url' => BASE_URL . '/' . $image->file_path,
                'width' => $imageInfo[0],
                'height' => $imageInfo[1],
                'csrf_token' => $this->generateCsrfToken()
            ];
            
            $this->view('image_editor/rotate', $data);
        }
    }
    
    /**
     * Apply filter to image
     * 
     * @param int|null $id Image ID or null to show the image selection page
     */
    public function filter($id = null) {
        // If no ID is provided, redirect to the browse page
        if ($id === null) {
            $this->redirect('image_editor/browse');
            return;
        }
        // Get image
        $image = $this->imageEditorModel->getImageById($id);
        
        if (!$image) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user has permission to edit this image
        $userId = $this->auth->getCurrentUserId();
        $userRole = $this->auth->getCurrentUserRole();
        
        // Allow admins to edit any image
        if ($userRole != 'admin') {
            // For vehicle images, check if user owns the vehicle
            if ($image->entity_type == 'vehicle') {
                $vehicleModel = $this->model('VehicleModel');
                $vehicle = $vehicleModel->getVehicleById($image->entity_id);
                
                if (!$vehicle || $vehicle->owner_id != $userId) {
                    $this->redirect('home/access_denied');
                    return;
                }
            }
            
            // For show images, check if user is the coordinator
            if ($image->entity_type == 'show') {
                $showModel = $this->model('ShowModel');
                $show = $showModel->getShowById($image->entity_id);
                
                if (!$show || ($show->coordinator_id != $userId && $userRole != 'coordinator')) {
                    $this->redirect('home/access_denied');
                    return;
                }
            }
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Get form data
            $filter = isset($_POST['filter']) ? $_POST['filter'] : '';
            
            // Validate filter
            $validFilters = ['grayscale', 'sepia', 'negative', 'brightness', 'contrast', 'edgedetect', 'emboss', 'blur', 'sharpen'];
            
            if (!in_array($filter, $validFilters)) {
                $this->redirect('home/error/Invalid%20filter');
                return;
            }
            
            // Apply filter
            if ($this->imageEditorModel->applyFilter($image->file_path, $filter)) {
                // Redirect back to edit page
                $this->redirect('image_editor/edit/' . $id);
            } else {
                $this->redirect('home/error/Failed%20to%20apply%20filter');
            }
        } else {
            // Get image info
            $imageInfo = getimagesize($image->file_path);
            
            $data = [
                'title' => 'Apply Filter',
                'image' => $image,
                'image_url' => BASE_URL . '/' . $image->file_path,
                'width' => $imageInfo[0],
                'height' => $imageInfo[1],
                'filters' => [
                    'grayscale' => 'Grayscale',
                    'sepia' => 'Sepia',
                    'negative' => 'Negative',
                    'brightness' => 'Brightness',
                    'contrast' => 'Contrast',
                    'edgedetect' => 'Edge Detect',
                    'emboss' => 'Emboss',
                    'blur' => 'Blur',
                    'sharpen' => 'Sharpen'
                ],
                'csrf_token' => $this->generateCsrfToken()
            ];
            
            $this->view('image_editor/filter', $data);
        }
    }
    
    /**
     * Add text to image
     * 
     * @param int|null $id Image ID or null to show the image selection page
     */
    public function text($id = null) {
        // If no ID is provided, redirect to the browse page
        if ($id === null) {
            $this->redirect('image_editor/browse');
            return;
        }
        // Get image
        $image = $this->imageEditorModel->getImageById($id);
        
        if (!$image) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user has permission to edit this image
        $userId = $this->auth->getCurrentUserId();
        $userRole = $this->auth->getCurrentUserRole();
        
        // Allow admins to edit any image
        if ($userRole != 'admin') {
            // For vehicle images, check if user owns the vehicle
            if ($image->entity_type == 'vehicle') {
                $vehicleModel = $this->model('VehicleModel');
                $vehicle = $vehicleModel->getVehicleById($image->entity_id);
                
                if (!$vehicle || $vehicle->owner_id != $userId) {
                    $this->redirect('home/access_denied');
                    return;
                }
            }
            
            // For show images, check if user is the coordinator
            if ($image->entity_type == 'show') {
                $showModel = $this->model('ShowModel');
                $show = $showModel->getShowById($image->entity_id);
                
                if (!$show || ($show->coordinator_id != $userId && $userRole != 'coordinator')) {
                    $this->redirect('home/access_denied');
                    return;
                }
            }
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Get form data
            $text = isset($_POST['text']) ? $_POST['text'] : '';
            $x = isset($_POST['x']) ? intval($_POST['x']) : 0;
            $y = isset($_POST['y']) ? intval($_POST['y']) : 0;
            $fontSize = isset($_POST['font_size']) ? intval($_POST['font_size']) : 20;
            $color = isset($_POST['color']) ? $_POST['color'] : '#ffffff';
            $angle = isset($_POST['angle']) ? intval($_POST['angle']) : 0;
            
            // Validate text
            if (empty($text)) {
                $this->redirect('home/error/Text%20cannot%20be%20empty');
                return;
            }
            
            // Convert hex color to RGB
            $colorRgb = sscanf($color, "#%02x%02x%02x");
            
            // Add text to image
            $options = [
                'font_size' => $fontSize,
                'color' => $colorRgb,
                'angle' => $angle
            ];
            
            if ($this->imageEditorModel->addTextToImage($image->file_path, $text, $x, $y, $options)) {
                // Redirect back to edit page
                $this->redirect('image_editor/edit/' . $id);
            } else {
                $this->redirect('home/error/Failed%20to%20add%20text');
            }
        } else {
            // Get image info
            $imageInfo = getimagesize($image->file_path);
            
            $data = [
                'title' => 'Add Text',
                'image' => $image,
                'image_url' => BASE_URL . '/' . $image->file_path,
                'width' => $imageInfo[0],
                'height' => $imageInfo[1],
                'csrf_token' => $this->generateCsrfToken()
            ];
            
            $this->view('image_editor/text', $data);
        }
    }
    
    /**
     * Draw on image
     * 
     * @param int|null $id Image ID or null to show the image selection page
     */
    public function draw($id = null) {
        // If no ID is provided, redirect to the browse page
        if ($id === null) {
            $this->redirect('image_editor/browse');
            return;
        }
        // Get image
        $image = $this->imageEditorModel->getImageById($id);
        
        if (!$image) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user has permission to edit this image
        $userId = $this->auth->getCurrentUserId();
        $userRole = $this->auth->getCurrentUserRole();
        
        // Allow admins to edit any image
        if ($userRole != 'admin') {
            // For vehicle images, check if user owns the vehicle
            if ($image->entity_type == 'vehicle') {
                $vehicleModel = $this->model('VehicleModel');
                $vehicle = $vehicleModel->getVehicleById($image->entity_id);
                
                if (!$vehicle || $vehicle->owner_id != $userId) {
                    $this->redirect('home/access_denied');
                    return;
                }
            }
            
            // For show images, check if user is the coordinator
            if ($image->entity_type == 'show') {
                $showModel = $this->model('ShowModel');
                $show = $showModel->getShowById($image->entity_id);
                
                if (!$show || ($show->coordinator_id != $userId && $userRole != 'coordinator')) {
                    $this->redirect('home/access_denied');
                    return;
                }
            }
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Get form data
            $shapes = isset($_POST['shapes']) ? json_decode($_POST['shapes'], true) : [];
            
            // Validate shapes
            if (empty($shapes)) {
                $this->redirect('home/error/No%20shapes%20to%20draw');
                return;
            }
            
            // Draw shapes on image
            if ($this->imageEditorModel->drawOnImage($image->file_path, $shapes)) {
                // Redirect back to edit page
                $this->redirect('image_editor/edit/' . $id);
            } else {
                $this->redirect('home/error/Failed%20to%20draw%20on%20image');
            }
        } else {
            // Get image info
            $imageInfo = getimagesize($image->file_path);
            
            $data = [
                'title' => 'Draw on Image',
                'image' => $image,
                'image_url' => BASE_URL . '/' . $image->file_path,
                'width' => $imageInfo[0],
                'height' => $imageInfo[1],
                'csrf_token' => $this->generateCsrfToken(),
                'cache_buster' => time()
            ];
            
            $this->view('image_editor/draw', $data);
        }
    }
    
    /**
     * Optimize image
     * 
     * @param mixed $param1 First parameter (can be image ID or 'batch')
     * @param mixed $param2 Second parameter (can be entity type if param1 is 'batch')
     * @param mixed $param3 Third parameter (can be entity ID if param1 is 'batch')
     * @param mixed $param4 Fourth parameter (unused)
     */
    public function optimize($param1 = null, $param2 = null, $param3 = null, $param4 = null) {
        // Debug logging
        error_log("ImageEditorController::optimize called with params: $param1, $param2, $param3, $param4");
        
        // Determine the actual parameters based on the first parameter
        $id = null;
        $mode = null;
        $entityType = null;
        $entityId = null;
        
        // If first parameter is 'batch', then we're in batch mode
        if ($param1 === 'batch') {
            $mode = 'batch';
            $entityType = $param2;
            $entityId = $param3;
            error_log("Detected batch mode: entityType=$entityType, entityId=$entityId");
        } else {
            // Otherwise, first parameter is the image ID
            $id = $param1;
            error_log("Detected single image mode: id=$id");
        }
        
        // If no ID is provided and not in batch mode, redirect to the browse page
        if ($id === null && $mode === null) {
            error_log("No ID or mode provided, redirecting to browse page");
            $this->redirect('image_editor/browse');
            return;
        }
        
        // If in batch mode, handle batch optimization
        if ($mode === 'batch' && $entityType !== null && $entityId !== null) {
            error_log("Batch optimization mode for $entityType ID $entityId");
            // Check if user has permission to optimize images for this entity
            $userId = $this->auth->getCurrentUserId();
            $userRole = $this->auth->getCurrentUserRole();
            
            // For vehicle images, check if user owns the vehicle
            if ($entityType == 'vehicle') {
                $vehicleModel = $this->model('VehicleModel');
                $vehicle = $vehicleModel->getVehicleById($entityId);
                
                if (!$vehicle || ($vehicle->owner_id != $userId && $userRole != 'admin')) {
                    $this->redirect('home/access_denied');
                    return;
                }
            }
            
            // For show images, check if user is the coordinator or admin
            if ($entityType == 'show') {
                $showModel = $this->model('ShowModel');
                $show = $showModel->getShowById($entityId);
                
                if (!$show || (($show->coordinator_id != $userId && $userRole != 'coordinator') && $userRole != 'admin')) {
                    $this->redirect('home/access_denied');
                    return;
                }
            }
            
            // Get images for this entity that the user has permission to access
            // For admin users, get all images for the entity
            error_log("User role: $userRole, User ID: $userId");
            
            if ($userRole == 'admin') {
                error_log("Admin user - getting all images for $entityType ID $entityId");
                $images = $this->imageEditorModel->getImagesByEntity($entityType, $entityId);
            } else {
                error_log("Non-admin user - getting permitted images for $entityType ID $entityId");
                // For non-admin users, get only images belonging to this entity
                // Use getImagesByEntity to get ONLY images for this entity
                $images = $this->imageEditorModel->getImagesByEntity($entityType, $entityId);
                
                // Filter out images the user doesn't have permission to access
                if ($entityType === 'vehicle') {
                    // For vehicles, check if user is owner or admin
                    $vehicleModel = $this->model('VehicleModel');
                    $vehicle = $vehicleModel->getVehicleById($entityId);
                    
                    if ($vehicle && $vehicle->owner_id != $userId && $userRole !== 'admin') {
                        // User doesn't have permission, return empty array
                        $images = [];
                    }
                } else if ($entityType === 'show') {
                    // For shows, check if user is coordinator or admin
                    $showModel = $this->model('ShowModel');
                    $show = $showModel->getShowById($entityId);
                    
                    if ($show && $show->coordinator_id != $userId && $userRole !== 'admin' && $userRole !== 'coordinator') {
                        // User doesn't have permission, return empty array
                        $images = [];
                    }
                }
                
                error_log("Found " . count($images) . " images with permission");
            }
            
            // Optimize each image
            $optimizedCount = 0;
            error_log("Starting optimization of " . count($images) . " images");
            
            foreach ($images as $image) {
                // Get image path
                $imagePath = $image->file_path;
                error_log("Processing image ID " . $image->id . ", path: $imagePath");
                
                // Check if file exists
                if (!file_exists($imagePath)) {
                    error_log("Image file does not exist: $imagePath");
                    continue;
                }
                
                // Optimize image
                error_log("Optimizing image: $imagePath");
                $result = $this->imageEditorModel->optimizeImage($imagePath, 85);
                if ($result) {
                    error_log("Image optimized successfully");
                    $optimizedCount++;
                } else {
                    error_log("Failed to optimize image");
                }
            }
            
            // Set flash message
            error_log("Setting flash message: $optimizedCount images optimized successfully");
            if (method_exists($this, 'setFlashMessage')) {
                $this->setFlashMessage('success', $optimizedCount . ' images optimized successfully.');
            } else if (function_exists('setFlashMessage')) {
                setFlashMessage('success', $optimizedCount . ' images optimized successfully.');
            } else {
                // Fallback: store message in session directly
                $_SESSION['flash_messages']['success'] = $optimizedCount . ' images optimized successfully.';
                error_log("Used fallback method to set flash message");
            }
            
            // Redirect based on entity type
            if ($entityType == 'vehicle') {
                $this->redirect('image_editor/vehicle/' . $entityId);
            } elseif ($entityType == 'show') {
                $this->redirect('image_editor/show/' . $entityId);
            } else {
                $this->redirect('image_editor/browse');
            }
            
            return;
        }
        // Get image
        $image = $this->imageEditorModel->getImageById($id);
        
        if (!$image) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user has permission to edit this image
        $userId = $this->auth->getCurrentUserId();
        $userRole = $this->auth->getCurrentUserRole();
        
        // Allow admins to edit any image
        if ($userRole != 'admin') {
            // For vehicle images, check if user owns the vehicle
            if ($image->entity_type == 'vehicle') {
                $vehicleModel = $this->model('VehicleModel');
                $vehicle = $vehicleModel->getVehicleById($image->entity_id);
                
                if (!$vehicle || $vehicle->owner_id != $userId) {
                    $this->redirect('home/access_denied');
                    return;
                }
            }
            
            // For show images, check if user is the coordinator
            if ($image->entity_type == 'show') {
                $showModel = $this->model('ShowModel');
                $show = $showModel->getShowById($image->entity_id);
                
                if (!$show || ($show->coordinator_id != $userId && $userRole != 'coordinator')) {
                    $this->redirect('home/access_denied');
                    return;
                }
            }
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Get form data
            $quality = isset($_POST['quality']) ? intval($_POST['quality']) : 85;
            
            // Validate quality
            if ($quality < 1 || $quality > 100) {
                $this->redirect('home/error/Invalid%20quality%20value');
                return;
            }
            
            // Optimize image
            if ($this->imageEditorModel->optimizeImage($image->file_path, $quality)) {
                // Update image size in database
                $imageData = [
                    'file_name' => $image->file_name,
                    'file_path' => $image->file_path,
                    'file_size' => filesize($image->file_path),
                    'file_type' => $image->file_type,
                    'width' => $image->width,
                    'height' => $image->height,
                    'is_primary' => $image->is_primary
                ];
                
                $this->imageEditorModel->updateImage($id, $imageData);
                
                // Redirect back to edit page
                $this->redirect('image_editor/edit/' . $id);
            } else {
                $this->redirect('home/error/Failed%20to%20optimize%20image');
            }
        } else {
            // Get image info
            $imageInfo = getimagesize($image->file_path);
            
            $data = [
                'title' => 'Optimize Image',
                'image' => $image,
                'image_url' => BASE_URL . '/' . $image->file_path,
                'width' => $imageInfo[0],
                'height' => $imageInfo[1],
                'file_size' => filesize($image->file_path),
                'csrf_token' => $this->generateCsrfToken()
            ];
            
            $this->view('image_editor/optimize', $data);
        }
    }
    
    /**
     * Upload image
     * 
     * @param string|null $entityType Entity type (vehicle, show, etc.) or null for general upload
     * @param int|null $entityId Entity ID or null for general upload
     */
    public function upload($entityType = null, $entityId = null) {
        // Get current user ID and role
        $userId = $this->auth->getCurrentUserId();
        $userRole = $this->auth->getCurrentUserRole();
        
        // Store the HTTP referer for "return to previous page" functionality
        if (isset($_SERVER['HTTP_REFERER'])) {
            // Only store referer if it's from our site and not the upload page itself
            if (strpos($_SERVER['HTTP_REFERER'], BASE_URL) === 0 && 
                strpos($_SERVER['HTTP_REFERER'], '/image_editor/upload') === false) {
                $_SESSION['image_editor_referer'] = $_SERVER['HTTP_REFERER'];
                error_log("Stored referer URL: " . $_SERVER['HTTP_REFERER']);
            }
        }
        
        // Get image settings
        $settingsModel = $this->model('SettingsModel');
        $imageSettings = [
            'image_quality' => $settingsModel->getSetting('image_image_quality', 80),
            'thumbnail_size' => $settingsModel->getSetting('image_thumbnail_size', 200),
            'max_upload_size' => $settingsModel->getSetting('image_max_upload_size', 5),
            'allowed_extensions' => $settingsModel->getSetting('image_allowed_extensions', 'jpg,jpeg,png,gif'),
            'optimize_images' => $settingsModel->getSetting('image_optimize_images', '0') === '1'
        ];
        
        // If no entity type is provided, show the general upload page
        if ($entityType === null) {
            $data = [
                'title' => 'Upload Images',
                'entity_type' => 'general',
                'entity_id' => 0,
                'entity_name' => 'General Library',
                'csrf_token' => $this->generateCsrfToken(),
                'settings' => $imageSettings
            ];
            
            $this->view('image_editor/upload', $data);
            return;
        }
        
        // Check if user has permission to upload images for this entity
        // For vehicle images, check if user owns the vehicle
        if ($entityType == 'vehicle') {
            $vehicleModel = $this->model('VehicleModel');
            $vehicle = $vehicleModel->getVehicleById($entityId);

            if (!$vehicle || ($vehicle->owner_id != $userId && $userRole != 'admin')) {
                $this->redirect('home/access_denied');
                return;
            }
        }

        // For event photos, check if user is logged in (location verification handled in PWA)
        if ($entityType == 'event_photo') {
            // Event photos are open to all logged-in users
            // Location verification is handled by the PWA camera system
            // Entity ID is just the integer ID (5, 21, etc.)

            $eventIdNum = (int)$entityId;

            // We need to determine if this is an event or show
            // Check shows first, then events
            $showModel = $this->model('ShowModel');
            $show = $showModel->getShowById($eventIdNum);

            if ($show) {
                $entityName = $show->name;
                $_POST['event_photo_event_type'] = 'show';
                $_POST['event_photo_event_id'] = $eventIdNum;
            } else {
                // Try as an event
                $calendarModel = $this->model('CalendarModel');
                $event = $calendarModel->getEventById($eventIdNum);

                if ($event) {
                    $entityName = $event->title;
                    $_POST['event_photo_event_type'] = 'event';
                    $_POST['event_photo_event_id'] = $eventIdNum;
                } else {
                    $this->redirect('home/error/Event%20or%20Show%20not%20found');
                    return;
                }
            }
        }
        
        // For show images, check if user is the coordinator
        if ($entityType == 'show') {
            $showModel = $this->model('ShowModel');
            $show = $showModel->getShowById($entityId);
            
            if (!$show || (($show->coordinator_id != $userId && $userRole != 'coordinator') && $userRole != 'admin')) {
                $this->redirect('home/access_denied');
                return;
            }
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Check if file was uploaded
            if (isset($_FILES['image']) && $_FILES['image']['error'] == 0) {
                // Process image upload
                if ($entityType == 'event_photo') {
                    $uploadDir = 'uploads/event_photos/';
                } else {
                    $uploadDir = 'uploads/' . $entityType . 's/';
                }
                
                // For user profile images, enforce one-image policy
                if ($entityType == 'user') {
                    // Delete any existing profile images for this user
                    $this->db->query('SELECT id FROM images WHERE entity_type = :entity_type AND entity_id = :entity_id');
                    $this->db->bind(':entity_type', 'user');
                    $this->db->bind(':entity_id', $entityId);
                    $existingImages = $this->db->resultSet();
                    
                    // Delete each existing image
                    foreach ($existingImages as $image) {
                        $this->imageEditorModel->deleteImage($image->id);
                    }
                }
                
                // Debug logging for event photos
                if (defined('DEBUG_MODE') && DEBUG_MODE && $entityType == 'event_photo') {
                    error_log("ImageUpload: Uploading event photo with entity_type='$entityType' and entity_id='$entityId'");
                }

                // Pass the current user ID to the upload method
                $imageData = $this->imageEditorModel->processImageUpload(
                    $_FILES['image'],
                    $entityType,
                    $entityId,
                    $uploadDir,
                    $userId,
                    ($entityType == 'user') // Set as primary image for user profile images
                );
                
                if ($imageData) {
                    // Set flash message for success
                    $this->setFlashMessage('upload', 'Image uploaded successfully!', 'success');

                    // Check if user wants to upload another image or return to browser
                    $afterUpload = isset($_POST['after_upload']) ? $_POST['after_upload'] : 'upload';

                    if ($afterUpload == 'upload') {
                        // Redirect back to upload page
                        if ($entityType == 'vehicle') {
                            $this->redirect('image_editor/upload/vehicle/' . $entityId);
                        } elseif ($entityType == 'show') {
                            $this->redirect('image_editor/upload/show/' . $entityId);
                        } elseif ($entityType == 'event_photo') {
                            $this->redirect('image_editor/upload/event_photo/' . $entityId);
                        } else {
                            $this->redirect('image_editor/upload');
                        }
                    } elseif ($afterUpload == 'browser') {
                        // Check if this is a PWA camera upload (has User-Agent indicating mobile app)
                        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
                        $isPWAUpload = strpos($userAgent, 'Mobile') !== false ||
                                      isset($_POST['pwa_camera_upload']) ||
                                      isset($_SERVER['HTTP_X_REQUESTED_WITH']);

                        if ($isPWAUpload && isset($imageData['id'])) {
                            // For PWA camera uploads, redirect directly to edit the uploaded image
                            $this->redirect('image_editor/edit/' . $imageData['id']);
                        } else {
                            // Check if we have a stored referer URL
                            if (isset($_SESSION['image_editor_referer'])) {
                                // Redirect to the stored referer URL
                                $refererUrl = $_SESSION['image_editor_referer'];
                                // Clear the stored referer to prevent issues with future uploads
                                unset($_SESSION['image_editor_referer']);

                                header('Location: ' . $refererUrl);
                                exit;
                            } else {
                                // Fallback to entity-specific pages if no referer is available
                                if ($entityType == 'vehicle') {
                                    $this->redirect('image_editor/vehicle/' . $entityId);
                                } elseif ($entityType == 'show') {
                                    $this->redirect('image_editor/show/' . $entityId);
                                } elseif ($entityType == 'event_photo') {
                                    // For event photos, redirect back to the event gallery
                                    // We need to determine if this is an event or show
                                    if (isset($_POST['event_photo_event_type']) && isset($_POST['event_photo_event_id'])) {
                                        $eventType = $_POST['event_photo_event_type'];
                                        $eventId = $_POST['event_photo_event_id'];
                                        $this->redirect('image_editor/eventGallery/' . $eventType . '/' . $eventId);
                                    } else {
                                        $this->redirect('image_editor/browse');
                                    }
                                } else {
                                    $this->redirect('image_editor/browse');
                                }
                            }
                        }
                    } else {
                        // Check if we have a stored referer URL
                        if (isset($_SESSION['image_editor_referer'])) {
                            // Redirect to the stored referer URL
                            $refererUrl = $_SESSION['image_editor_referer'];
                            // Clear the stored referer to prevent issues with future uploads
                            unset($_SESSION['image_editor_referer']);

                            header('Location: ' . $refererUrl);
                            exit;
                        } else {
                            // Fallback to entity-specific pages if no referer is available
                            if ($entityType == 'vehicle') {
                                $this->redirect('image_editor/vehicle/' . $entityId);
                            } elseif ($entityType == 'show') {
                                $this->redirect('image_editor/show/' . $entityId);
                            } elseif ($entityType == 'event_photo') {
                                // For event photos, redirect back to the event gallery
                                if (isset($_POST['event_photo_event_type']) && isset($_POST['event_photo_event_id'])) {
                                    $eventType = $_POST['event_photo_event_type'];
                                    $eventId = $_POST['event_photo_event_id'];
                                    $this->redirect('image_editor/eventGallery/' . $eventType . '/' . $eventId);
                                } else {
                                    $this->redirect('image_editor/browse');
                                }
                            } else {
                                $this->redirect('image_editor/browse');
                            }
                        }
                    }
                } else {
                    $this->setFlashMessage('upload', 'Failed to upload image. Please try again.', 'danger');
                    $this->redirect('image_editor/upload/' . ($entityType ? $entityType . '/' . $entityId : ''));
                }
            } else {
                $this->redirect('home/error/No%20file%20uploaded');
            }
        } else {
            // Get entity info
            $entityName = '';
            
            if ($entityType == 'vehicle') {
                $vehicleModel = $this->model('VehicleModel');
                $vehicle = $vehicleModel->getVehicleById($entityId);
                
                if ($vehicle) {
                    $entityName = $vehicle->year . ' ' . $vehicle->make . ' ' . $vehicle->model;
                } else {
                    $entityName = 'Unknown Vehicle (ID: ' . $entityId . ')';
                }
            } elseif ($entityType == 'show') {
                $showModel = $this->model('ShowModel');
                $show = $showModel->getShowById($entityId);
                
                if ($show) {
                    $entityName = $show->name;
                } else {
                    $entityName = 'Unknown Show (ID: ' . $entityId . ')';
                }
            } elseif ($entityType == 'event_photo') {
                // Entity name was already set during permission check above
                if (!isset($entityName) || empty($entityName)) {
                    // Try to get the name again if it wasn't set properly
                    $eventIdNum = (int)$entityId;
                    $showModel = $this->model('ShowModel');
                    $show = $showModel->getShowById($eventIdNum);

                    if ($show) {
                        $entityName = $show->name;
                    } else {
                        $calendarModel = $this->model('CalendarModel');
                        $event = $calendarModel->getEventById($eventIdNum);
                        if ($event) {
                            $entityName = $event->title;
                        } else {
                            $entityName = 'Event Photo';
                        }
                    }
                }
            } else {
                $entityName = 'General Image';
            }
            
            $data = [
                'title' => 'Upload Image',
                'entity_type' => $entityType,
                'entity_id' => $entityId,
                'entity_name' => $entityName,
                'csrf_token' => $this->generateCsrfToken(),
                'settings' => $imageSettings
            ];

            // Add category labels for event photos
            if ($entityType == 'event_photo') {
                $data['category_labels'] = $this->getEventPhotoCategoryLabels();

                // Determine event type from URL or context
                // Check if we came from a show or event page
                $referrer = $_SERVER['HTTP_REFERER'] ?? '';
                if (strpos($referrer, '/show/') !== false || strpos($referrer, '/shows/') !== false) {
                    $data['event_type'] = 'show';
                } else {
                    $data['event_type'] = 'event'; // Default to event
                }
            }

            $this->view('image_editor/upload', $data);
        }
    }
    
    /**
     * Delete all images for an entity
     * 
     * @param string $entityType Entity type (vehicle, show, etc.)
     * @param int $entityId Entity ID
     */
    public function deleteAll($entityType, $entityId) {
        // Check if user has permission to delete images for this entity
        $userId = $this->auth->getCurrentUserId();
        $userRole = $this->auth->getCurrentUserRole();
        
        // For vehicle images, check if user owns the vehicle
        if ($entityType == 'vehicle') {
            $vehicleModel = $this->model('VehicleModel');
            $vehicle = $vehicleModel->getVehicleById($entityId);
            
            if (!$vehicle || ($vehicle->owner_id != $userId && $userRole != 'admin')) {
                $this->redirect('home/access_denied');
                return;
            }
        }
        
        // For show images, check if user is admin
        if ($entityType == 'show' && $userRole != 'admin') {
            $this->redirect('home/access_denied');
            return;
        }
        
        // If form was submitted, delete all images
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Delete all images for this entity
            // Use the existing ImageEditorModel instead of trying to load ImageModel
            $images = $this->imageEditorModel->getImagesByEntity($entityType, $entityId);
            
            foreach ($images as $image) {
                // Delete image file
                $filePath = APPROOT . '/uploads/' . $image->file_name;
                if (file_exists($filePath)) {
                    unlink($filePath);
                }
                
                // Delete thumbnail
                $thumbnailPath = APPROOT . '/uploads/thumbnails/' . $image->file_name;
                if (file_exists($thumbnailPath)) {
                    unlink($thumbnailPath);
                }
                
                // Delete image record
                $this->imageEditorModel->deleteImage($image->id);
            }
            
            // Redirect based on entity type
            if ($entityType == 'vehicle') {
                $this->redirect('image_editor/vehicle/' . $entityId);
            } elseif ($entityType == 'show') {
                $this->redirect('image_editor/show/' . $entityId);
            } else {
                $this->redirect('image_editor/browse');
            }
        } else {
            // Show confirmation page
            $entityName = '';
            
            if ($entityType == 'vehicle') {
                $vehicleModel = $this->model('VehicleModel');
                $vehicle = $vehicleModel->getVehicleById($entityId);
                $entityName = $vehicle ? $vehicle->year . ' ' . $vehicle->make . ' ' . $vehicle->model : '';
            } elseif ($entityType == 'show') {
                $showModel = $this->model('ShowModel');
                $show = $showModel->getShowById($entityId);
                $entityName = $show ? $show->name : '';
            }
            
            $data = [
                'title' => 'Delete All Images',
                'entity_type' => $entityType,
                'entity_id' => $entityId,
                'entity_name' => $entityName,
                'csrf_token' => $this->generateCsrfToken()
            ];
            
            $this->view('image_editor/delete_all', $data);
        }
    }
    
    /**
     * Delete image
     * 
     * @param int|null $id Image ID or null to show the image selection page
     */
    public function delete($id = null) {
        // If no ID is provided, redirect to the browse page
        if ($id === null) {
            $this->redirect('image_editor/browse');
            return;
        }
        // Get image
        $image = $this->imageEditorModel->getImageById($id);
        
        if (!$image) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user has permission to delete this image
        $userId = $this->auth->getCurrentUserId();
        $userRole = $this->auth->getCurrentUserRole();
        
        // Allow admins to delete any image
        if ($userRole != 'admin') {
            // For vehicle images, check if user owns the vehicle
            if ($image->entity_type == 'vehicle') {
                $vehicleModel = $this->model('VehicleModel');
                $vehicle = $vehicleModel->getVehicleById($image->entity_id);
                
                if (!$vehicle || $vehicle->owner_id != $userId) {
                    $this->redirect('home/access_denied');
                    return;
                }
            }
            
            // For show images, check if user is the coordinator
            if ($image->entity_type == 'show') {
                $showModel = $this->model('ShowModel');
                $show = $showModel->getShowById($image->entity_id);
                
                if (!$show || ($show->coordinator_id != $userId && $userRole != 'coordinator')) {
                    $this->redirect('home/access_denied');
                    return;
                }
            }
        }
        
        // Check if form was submitted
        if ($_SERVER['REQUEST_METHOD'] == 'POST') {
            // Validate CSRF token
            if (!$this->verifyCsrfToken()) {
                $this->redirect('home/error/Invalid%20request');
                return;
            }
            
            // Delete image
            if ($this->imageEditorModel->deleteImage($id)) {
                // Set default redirect to image browser
                $redirectUrl = BASE_URL . '/image_editor/browse';

                // First check if we have the original referrer stored in session
                if (isset($_SESSION['image_editor_original_referrer_' . $id])) {
                    $redirectUrl = $_SESSION['image_editor_original_referrer_' . $id];
                    // Debug logging
                    if (defined('DEBUG_MODE') && DEBUG_MODE) {
                        error_log("ImageEditor::delete POST - Using original referrer for image $id: $redirectUrl");
                    }
                    // Clean up the session variable
                    unset($_SESSION['image_editor_original_referrer_' . $id]);
                }
                // Then check if we have a referrer from the form
                elseif (isset($_POST['referrer'])) {
                    $referrer = $_POST['referrer'];
                    // Make sure the referrer is from our site
                    if (strpos($referrer, BASE_URL) === 0) {
                        // Check if the referrer is a page we should NOT return to after deletion
                        $invalidRedirectPages = [
                            'image_editor/delete',
                            'image_editor/edit',
                            'image_editor/view',
                            'image/view'
                        ];

                        $isInvalidRedirect = false;
                        foreach ($invalidRedirectPages as $invalidPage) {
                            if (strpos($referrer, $invalidPage) !== false) {
                                $isInvalidRedirect = true;
                                break;
                            }
                        }

                        // Only use the referrer if it's not an invalid redirect page
                        if (!$isInvalidRedirect) {
                            $redirectUrl = $referrer;
                        }
                    }
                }
                // Finally check if we have a referer header to redirect back to
                elseif (isset($_SERVER['HTTP_REFERER'])) {
                    $referer = $_SERVER['HTTP_REFERER'];
                    // Make sure the referer is from our site
                    if (strpos($referer, BASE_URL) === 0) {
                        // Check if the referrer is a page we should NOT return to after deletion
                        $invalidRedirectPages = [
                            'image_editor/delete',
                            'image_editor/edit',
                            'image_editor/view',
                            'image/view'
                        ];

                        $isInvalidRedirect = false;
                        foreach ($invalidRedirectPages as $invalidPage) {
                            if (strpos($referer, $invalidPage) !== false) {
                                $isInvalidRedirect = true;
                                break;
                            }
                        }

                        // Only use the referrer if it's not an invalid redirect page
                        if (!$isInvalidRedirect) {
                            $redirectUrl = $referer;
                        }
                    }
                }
                
                // Set success message
                $this->setFlashMessage('success', 'Image deleted successfully.');
                
                // Redirect to the determined URL
                header('Location: ' . $redirectUrl);
                exit;
            } else {
                $this->setFlashMessage('error', 'Failed to delete image.', 'danger');
                $this->redirect('image_editor/browse');
            }
        } else {
            // Get entity info
            $entityName = '';
            
            if ($image->entity_type == 'vehicle') {
                $vehicleModel = $this->model('VehicleModel');
                $vehicle = $vehicleModel->getVehicleById($image->entity_id);
                
                if ($vehicle) {
                    $entityName = $vehicle->year . ' ' . $vehicle->make . ' ' . $vehicle->model;
                } else {
                    $entityName = 'Unknown Vehicle (ID: ' . $image->entity_id . ')';
                }
            } elseif ($image->entity_type == 'show') {
                $showModel = $this->model('ShowModel');
                $show = $showModel->getShowById($image->entity_id);
                
                if ($show) {
                    $entityName = $show->name;
                } else {
                    $entityName = 'Unknown Show (ID: ' . $image->entity_id . ')';
                }
            } else {
                $entityName = 'General Image';
            }
            
            // Check if we have the original referrer stored in session
            $originalReferrer = null;
            if (isset($_SESSION['image_editor_original_referrer_' . $id])) {
                $originalReferrer = $_SESSION['image_editor_original_referrer_' . $id];
                // Debug logging
                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                    error_log("ImageEditor::delete - Found original referrer for image $id: $originalReferrer");
                }
            } else {
                // Debug logging
                if (defined('DEBUG_MODE') && DEBUG_MODE) {
                    error_log("ImageEditor::delete - No original referrer found for image $id");
                }
            }

            $data = [
                'title' => 'Delete Image',
                'image' => $image,
                'image_url' => BASE_URL . '/' . $image->file_path,
                'entity_name' => $entityName,
                'csrf_token' => $this->generateCsrfToken(),
                'original_referrer' => $originalReferrer
            ];
            
            $this->view('image_editor/delete', $data);
        }
    }
    
    /**
     * Set primary image by ID
     * 
     * @param int $id Image ID
     */
    public function setPrimaryById($id) {
        // Get image
        $image = $this->imageEditorModel->getImageById($id);
        
        if (!$image) {
            $this->redirect('home/not_found');
            return;
        }
        
        // Check if user has permission to set this image as primary
        $userId = $this->auth->getCurrentUserId();
        $userRole = $this->auth->getCurrentUserRole();
        
        // Allow admins to set any image as primary
        if ($userRole != 'admin') {
            // For vehicle images, check if user owns the vehicle
            if ($image->entity_type == 'vehicle') {
                $vehicleModel = $this->model('VehicleModel');
                $vehicle = $vehicleModel->getVehicleById($image->entity_id);
                
                if (!$vehicle || $vehicle->owner_id != $userId) {
                    $this->redirect('home/access_denied');
                    return;
                }
            }
            
            // For show images, check if user is the coordinator
            if ($image->entity_type == 'show') {
                $showModel = $this->model('ShowModel');
                $show = $showModel->getShowById($image->entity_id);
                
                if (!$show || ($show->coordinator_id != $userId && $userRole != 'coordinator')) {
                    $this->redirect('home/access_denied');
                    return;
                }
            }
        }
        
        // Set image as primary
        if ($this->imageEditorModel->setPrimaryImage($id)) {
            // Redirect based on entity type
            if ($image->entity_type == 'vehicle') {
                $this->redirect('user/vehicle_images/' . $image->entity_id);
            } elseif ($image->entity_type == 'show') {
                $this->redirect('show/images/' . $image->entity_id);
            } else {
                $this->redirect('home');
            }
        } else {
            $this->redirect('home/error/Failed%20to%20set%20primary%20image');
        }
    }

    /**
     * Event Photo Gallery - Show photos for a specific event or show
     *
     * @param string $eventType 'event' or 'show'
     * @param int $eventId Event or Show ID
     */
    public function eventGallery($eventType = null, $eventId = null) {
        // Validate parameters
        if (!$eventType || !$eventId || !in_array($eventType, ['event', 'show'])) {
            $this->redirect('home/error/Invalid%20event%20reference');
            return;
        }

        // Get event/show info
        $eventInfo = null;
        if ($eventType === 'event') {
            $eventModel = $this->model('EventModel');
            $eventInfo = $eventModel->getEventById($eventId);
        } else {
            $showModel = $this->model('ShowModel');
            $eventInfo = $showModel->getShowById($eventId);
        }

        if (!$eventInfo) {
            $this->redirect('home/error/Event%20not%20found');
            return;
        }

        // Get event photos using existing image system
        $imageModel = $this->model('ImageEditorModel');

        // Use 'event_photo' entity_type with integer entity_id
        $dbEntityId = (int)$eventId; // integer ID

        // Debug logging
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log("EventGallery: Looking for images with entity_type='event_photo' and entity_id='$dbEntityId'");
        }

        $images = $imageModel->getImagesByEntity('event_photo', $dbEntityId);

        // Debug logging
        if (defined('DEBUG_MODE') && DEBUG_MODE) {
            error_log("EventGallery: Found " . count($images) . " event photos with entity_type='event_photo'");

            // Also check for other entity types to see what's actually in the database
            $showImages = $imageModel->getImagesByEntity('show', $dbEntityId);
            error_log("EventGallery: Found " . count($showImages) . " images with entity_type='show'");

            $otherImages = $imageModel->getImagesByEntity('other', $dbEntityId);
            error_log("EventGallery: Found " . count($otherImages) . " images with entity_type='other'");
        }

        // Get event photo metadata for each image
        $photosWithMetadata = [];
        foreach ($images as $image) {
            // Debug logging
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log("EventGallery: Processing image ID: {$image->id}, entity_type: {$image->entity_type}, entity_id: {$image->entity_id}");
            }

            $metadata = $this->getEventPhotoMetadata($image->id);
            $photosWithMetadata[] = (object) array_merge((array) $image, (array) $metadata);
        }

        // Get the correct name property based on event type
        $eventName = ($eventType === 'event') ? $eventInfo->title : $eventInfo->name;

        // Get admin-configured category labels
        $categoryLabels = $this->getEventPhotoCategoryLabels();

        $data = [
            'title' => $eventName . ' - Photo Gallery',
            'event_info' => $eventInfo,
            'event_type' => $eventType,
            'event_id' => $eventId,
            'event_name' => $eventName, // Add this for the view
            'photos' => $photosWithMetadata,
            'can_upload' => isLoggedIn(), // Users can upload if logged in (location verified by PWA)
            'category_labels' => $categoryLabels
        ];

        $this->view('image_editor/event_gallery', $data);
    }

    /**
     * Get event photo metadata
     *
     * @param int $imageId Image ID
     * @return object|null Metadata object
     */
    private function getEventPhotoMetadata($imageId) {
        try {
            // Select specific fields to avoid overwriting image.id with metadata.id
            $this->db->query('SELECT category, caption, privacy_level, latitude, longitude,
                             event_type, event_id, created_at as metadata_created_at
                             FROM event_photo_metadata WHERE image_id = :image_id');
            $this->db->bind(':image_id', $imageId);
            return $this->db->single();
        } catch (Exception $e) {
            return null;
        }
    }

    /**
     * Get admin-configured category labels
     */
    private function getEventPhotoCategoryLabels() {
        try {
            $this->db->query('SELECT setting_name, setting_value FROM event_photo_settings
                             WHERE setting_name LIKE "category_label_%"');
            $results = $this->db->resultSet();

            $labels = [
                'vehicle' => '🚗 Vehicle',
                'atmosphere' => '🎪 Atmosphere',
                'awards' => '🏆 Awards',
                'vendors' => '🍔 Vendors',
                'people' => '👥 People'
            ];

            // Override with admin settings if they exist
            foreach ($results as $setting) {
                $key = str_replace('category_label_', '', $setting->setting_name);
                if (isset($labels[$key])) {
                    $labels[$key] = $setting->setting_value;
                }
            }

            return $labels;
        } catch (Exception $e) {
            // Return defaults if there's an error
            return [
                'vehicle' => '🚗 Vehicle',
                'atmosphere' => '🎪 Atmosphere',
                'awards' => '🏆 Awards',
                'vendors' => '🍔 Vendors',
                'people' => '👥 People'
            ];
        }
    }

    /**
     * Get event photo details for editing
     *
     * @param int $photoId Photo ID
     */
    public function getEventPhotoDetails($photoId) {
        header('Content-Type: application/json');

        // Check if user is logged in
        if (!isLoggedIn()) {
            echo json_encode(['success' => false, 'message' => 'Not logged in']);
            return;
        }

        $userId = $this->auth->getCurrentUserId();
        $userRole = $this->auth->getCurrentUserRole();

        try {
            // Get photo with metadata
            $this->db->query('SELECT i.*, epm.category, epm.caption, epm.privacy_level, epm.event_type, epm.event_id
                             FROM images i
                             LEFT JOIN event_photo_metadata epm ON i.id = epm.image_id
                             WHERE i.id = :photo_id AND i.entity_type = "event_photo"');
            $this->db->bind(':photo_id', $photoId);
            $photo = $this->db->single();

            if (!$photo) {
                echo json_encode(['success' => false, 'message' => 'Photo not found']);
                return;
            }

            // Check if user can edit this photo (owner or admin)
            if ($photo->user_id != $userId && $userRole != 'admin') {
                echo json_encode(['success' => false, 'message' => 'Permission denied']);
                return;
            }

            echo json_encode([
                'success' => true,
                'photo' => [
                    'id' => $photo->id,
                    'category' => $photo->category,
                    'caption' => $photo->caption,
                    'privacy_level' => $photo->privacy_level ?: 'public'
                ]
            ]);

        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => 'Database error']);
        }
    }

    /**
     * Update event photo details
     */
    public function updateEventPhotoDetails() {
        header('Content-Type: application/json');

        // Check if user is logged in
        if (!isLoggedIn()) {
            echo json_encode(['success' => false, 'message' => 'Not logged in']);
            return;
        }

        // Check if this is a POST request
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            echo json_encode(['success' => false, 'message' => 'Invalid request method']);
            return;
        }

        $userId = $this->auth->getCurrentUserId();
        $userRole = $this->auth->getCurrentUserRole();
        $photoId = $_POST['photo_id'] ?? null;

        if (!$photoId) {
            echo json_encode(['success' => false, 'message' => 'Photo ID required']);
            return;
        }

        try {
            // Get photo to check ownership
            $this->db->query('SELECT user_id FROM images WHERE id = :photo_id AND entity_type = "event_photo"');
            $this->db->bind(':photo_id', $photoId);
            $photo = $this->db->single();

            if (!$photo) {
                echo json_encode(['success' => false, 'message' => 'Photo not found']);
                return;
            }

            // Check if user can edit this photo (owner or admin)
            if ($photo->user_id != $userId && $userRole != 'admin') {
                echo json_encode(['success' => false, 'message' => 'Permission denied']);
                return;
            }

            // Validate and sanitize input
            $category = $_POST['category'] ?? '';
            $caption = $_POST['caption'] ?? '';
            $privacyLevel = $_POST['privacy_level'] ?? 'public';

            // Validate category
            $validCategories = ['vehicle', 'atmosphere', 'awards', 'vendors', 'people'];
            if (!in_array($category, $validCategories)) {
                echo json_encode(['success' => false, 'message' => 'Invalid category']);
                return;
            }

            // Validate privacy level
            $validPrivacyLevels = ['public', 'event_only', 'private'];
            if (!in_array($privacyLevel, $validPrivacyLevels)) {
                echo json_encode(['success' => false, 'message' => 'Invalid privacy level']);
                return;
            }

            // Limit caption length
            if (strlen($caption) > 500) {
                $caption = substr($caption, 0, 500);
            }

            // Update event photo metadata
            $this->db->query('UPDATE event_photo_metadata
                             SET category = :category, caption = :caption, privacy_level = :privacy_level
                             WHERE image_id = :image_id');
            $this->db->bind(':category', $category);
            $this->db->bind(':caption', $caption);
            $this->db->bind(':privacy_level', $privacyLevel);
            $this->db->bind(':image_id', $photoId);

            if ($this->db->execute()) {
                echo json_encode(['success' => true, 'message' => 'Photo details updated successfully']);
            } else {
                echo json_encode(['success' => false, 'message' => 'Failed to update photo details']);
            }

        } catch (Exception $e) {
            echo json_encode(['success' => false, 'message' => 'Database error']);
        }
    }
    
    /**
     * Get event images for main image selector
     * 
     * @param int $eventId Event ID
     */
    public function getEventImages($eventId) {
        header('Content-Type: application/json');
        
        // Check if user is logged in
        if (!isLoggedIn()) {
            echo json_encode(['success' => false, 'message' => 'Not logged in']);
            return;
        }
        
        try {
            // Get images for this event
            $images = $this->imageEditorModel->getImagesByEntity('event', $eventId);
            
            // Format images for response
            $formattedImages = [];
            foreach ($images as $image) {
                $formattedImages[] = [
                    'id' => $image->id,
                    'file_name' => $image->file_name,
                    'file_path' => $image->file_path,
                    'thumbnail_path' => $image->thumbnail_path,
                    'file_size' => $image->file_size,
                    'width' => $image->width,
                    'height' => $image->height,
                    'is_primary' => $image->is_primary
                ];
            }
            
            echo json_encode([
                'success' => true,
                'images' => $formattedImages
            ]);
            
        } catch (Exception $e) {
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log('Error getting event images: ' . $e->getMessage());
            }
            echo json_encode(['success' => false, 'message' => 'Error loading images']);
        }
    }
}