
-- --------------------------------------------------------

--
-- Table structure for table `show_role_assignments`
--
-- Creation: Jul 17, 2025 at 04:25 PM
--

CREATE TABLE `show_role_assignments` (
  `id` int(11) NOT NULL,
  `show_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `assigned_role` enum('coordinator','judge','staff') NOT NULL,
  `assigned_by` int(11) NOT NULL COMMENT 'User ID of coordinator/admin who made the assignment',
  `request_id` int(11) DEFAULT NULL COMMENT 'Reference to the original request',
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `assigned_at` datetime NOT NULL DEFAULT current_timestamp(),
  `expires_at` datetime NOT NULL COMMENT 'Show end date + 1 week for automatic cleanup',
  `auto_cleanup_date` datetime NOT NULL COMMENT 'Date when this assignment will be automatically removed',
  `created_at` datetime NOT NULL DEFAULT current_timestamp(),
  `updated_at` datetime NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_uca1400_ai_ci COMMENT='Stores active show-specific role assignments';

--
-- RELATIONSHIPS FOR TABLE `show_role_assignments`:
--

--
-- Dumping data for table `show_role_assignments`
--

INSERT INTO `show_role_assignments` (`id`, `show_id`, `user_id`, `assigned_role`, `assigned_by`, `request_id`, `is_active`, `assigned_at`, `expires_at`, `auto_cleanup_date`, `created_at`, `updated_at`) VALUES
(1, 5, 3, 'staff', 3, NULL, 0, '2025-07-10 21:45:34', '2025-07-08 23:59:59', '2025-07-14 23:59:59', '2025-07-10 21:45:34', '2025-07-10 21:46:08'),
(2, 113, 3, 'judge', 2406, 1, 1, '2025-07-10 21:52:36', '2025-11-27 23:59:59', '2025-12-03 23:59:59', '2025-07-10 21:52:36', '2025-07-10 21:52:36'),
(7, 9, 3, 'judge', 3, NULL, 1, '2025-07-11 11:37:15', '0000-00-00 00:00:00', '0000-00-00 00:00:00', '2025-07-11 11:37:15', '2025-07-11 11:37:15');
