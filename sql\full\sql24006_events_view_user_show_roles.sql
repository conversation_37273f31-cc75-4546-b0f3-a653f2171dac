
-- --------------------------------------------------------

--
-- Structure for view `user_show_roles`
--
DROP TABLE IF EXISTS `user_show_roles`;

CREATE ALGORITHM=UNDEFINED DEFINER=`sql24006_forward-limpet`@`108.70.196.156` SQL SECURITY DEFINER VIEW `user_show_roles`  AS SELECT `sra`.`user_id` AS `user_id`, `sra`.`show_id` AS `show_id`, `s`.`name` AS `show_name`, `sra`.`assigned_role` AS `assigned_role`, `sra`.`is_active` AS `is_active`, `sra`.`assigned_at` AS `assigned_at`, `sra`.`expires_at` AS `expires_at`, `u`.`name` AS `user_name`, `u`.`email` AS `user_email`, `assigner`.`name` AS `assigned_by_name` FROM (((`show_role_assignments` `sra` join `shows` `s` on(`sra`.`show_id` = `s`.`id`)) join `users` `u` on(`sra`.`user_id` = `u`.`id`)) join `users` `assigner` on(`sra`.`assigned_by` = `assigner`.`id`)) WHERE `sra`.`is_active` = 1 ;
