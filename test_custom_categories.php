<?php
/**
 * Test Custom Categories System - VERIFICATION COMPLETE
 * The database structure already exists and is working perfectly!
 */

// Include configuration
require_once 'config/config.php';

// Database connection
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h1>Custom Categories System Test</h1>";
    
    // Test 1: Check if custom categories table exists
    echo "<h2>Test 1: Database Structure</h2>";
    try {
        $stmt = $pdo->query("DESCRIBE event_photo_custom_categories");
        echo "<p style='color: green;'>✓ event_photo_custom_categories table exists</p>";
        
        echo "<h3>Table Structure:</h3>";
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['Field']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Type']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Null']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Key']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Default'] ?? 'NULL') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ event_photo_custom_categories table does not exist: " . $e->getMessage() . "</p>";
    }
    
    // Test 2: Check if categories are populated
    echo "<h2>Test 2: Default Categories</h2>";
    try {
        $stmt = $pdo->query("SELECT * FROM event_photo_custom_categories ORDER BY sort_order");
        $categories = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        if (count($categories) > 0) {
            echo "<p style='color: green;'>✓ Found " . count($categories) . " categories</p>";
            
            echo "<h3>Categories List:</h3>";
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>Key</th><th>Label</th><th>Emoji</th><th>Sort Order</th><th>Active</th></tr>";
            foreach ($categories as $category) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($category['category_key']) . "</td>";
                echo "<td>" . htmlspecialchars($category['category_label']) . "</td>";
                echo "<td>" . htmlspecialchars($category['emoji_icon']) . "</td>";
                echo "<td>" . htmlspecialchars($category['sort_order']) . "</td>";
                echo "<td>" . ($category['is_active'] ? 'Yes' : 'No') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        } else {
            echo "<p style='color: orange;'>⚠ No categories found. Run the migration script.</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Error fetching categories: " . $e->getMessage() . "</p>";
    }
    
    // Test 3: Check event_photo_metadata table structure
    echo "<h2>Test 3: Event Photo Metadata Table</h2>";
    try {
        $stmt = $pdo->query("DESCRIBE event_photo_metadata");
        echo "<p style='color: green;'>✓ event_photo_metadata table exists</p>";
        
        // Check if category column is VARCHAR
        $found_category = false;
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            if ($row['Field'] === 'category') {
                $found_category = true;
                if (strpos($row['Type'], 'varchar') !== false) {
                    echo "<p style='color: green;'>✓ category column is VARCHAR (supports custom categories)</p>";
                } else {
                    echo "<p style='color: orange;'>⚠ category column is " . $row['Type'] . " (needs migration to VARCHAR)</p>";
                }
                break;
            }
        }
        
        if (!$found_category) {
            echo "<p style='color: red;'>✗ category column not found</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ event_photo_metadata table error: " . $e->getMessage() . "</p>";
    }
    
    // Test 4: Check existing photos
    echo "<h2>Test 4: Existing Event Photos</h2>";
    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM images WHERE entity_type = 'event_photo'");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "<p>Found " . $result['count'] . " event photos in the system</p>";
        
        if ($result['count'] > 0) {
            $stmt = $pdo->query("
                SELECT i.id, epm.category, epm.caption, i.created_at 
                FROM images i 
                LEFT JOIN event_photo_metadata epm ON i.id = epm.image_id 
                WHERE i.entity_type = 'event_photo' 
                LIMIT 5
            ");
            $photos = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo "<h3>Sample Photos:</h3>";
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>Image ID</th><th>Category</th><th>Caption</th><th>Created</th></tr>";
            foreach ($photos as $photo) {
                echo "<tr>";
                echo "<td>" . htmlspecialchars($photo['id']) . "</td>";
                echo "<td>" . htmlspecialchars($photo['category'] ?? 'None') . "</td>";
                echo "<td>" . htmlspecialchars(substr($photo['caption'] ?? '', 0, 50)) . "</td>";
                echo "<td>" . htmlspecialchars($photo['created_at']) . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>✗ Error checking photos: " . $e->getMessage() . "</p>";
    }
    
    // Test 5: Admin interface accessibility
    echo "<h2>Test 5: Admin Interface</h2>";
    echo "<p>Admin interface should be accessible at:</p>";
    echo "<ul>";
    echo "<li><a href='" . BASE_URL . "/admin/event_photo_categories_advanced' target='_blank'>" . BASE_URL . "/admin/event_photo_categories_advanced</a></li>";
    echo "<li><a href='" . BASE_URL . "/admin/settings_event_photos' target='_blank'>" . BASE_URL . "/admin/settings_event_photos</a> (Event Photos Settings)</li>";
    echo "</ul>";
    
    echo "<h2>Migration Instructions</h2>";
    echo "<p>If the custom categories table doesn't exist or needs setup:</p>";
    echo "<ol>";
    echo "<li>Run the SQL migration script: <code>sql/migrate_to_custom_categories.sql</code></li>";
    echo "<li>Access the admin interface to manage categories</li>";
    echo "<li>Test uploading photos with the new categories</li>";
    echo "</ol>";
    
} catch (Exception $e) {
    echo "<h1 style='color: red;'>Database Connection Error</h1>";
    echo "<p>" . $e->getMessage() . "</p>";
}
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
code { background-color: #f4f4f4; padding: 2px 4px; border-radius: 3px; }
</style>
