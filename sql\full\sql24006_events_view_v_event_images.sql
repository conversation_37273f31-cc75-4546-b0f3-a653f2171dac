
-- --------------------------------------------------------

--
-- Structure for view `v_event_images`
--
DROP TABLE IF EXISTS `v_event_images`;

CREATE ALGORITHM=UNDEFINED DEFINER=`sql24006_forward-limpet`@`localhost` SQL SECURITY DEFINER VIEW `v_event_images`  AS SELECT `ei`.`id` AS `id`, `ei`.`event_id` AS `event_id`, `ei`.`user_id` AS `user_id`, `ei`.`file_name` AS `file_name`, `ei`.`file_type` AS `file_type`, `ei`.`file_size` AS `file_size`, `ei`.`width` AS `width`, `ei`.`height` AS `height`, `ei`.`alt_text` AS `alt_text`, `ei`.`caption` AS `caption`, `ei`.`display_order` AS `display_order`, `ei`.`is_featured` AS `is_featured`, `ei`.`created_at` AS `created_at`, `ei`.`updated_at` AS `updated_at`, coalesce(`e`.`title`,'Unknown Event') AS `event_title`, coalesce(`e`.`description`,'') AS `event_description`, coalesce(`u`.`name`,'Unknown User') AS `uploader_name` FROM ((`calendar_event_images` `ei` left join `calendar_events` `e` on(`ei`.`event_id` = `e`.`id`)) left join `users` `u` on(`ei`.`user_id` = `u`.`id`)) ORDER BY `ei`.`event_id` ASC, `ei`.`display_order` ASC, `ei`.`created_at` ASC ;
