
-- --------------------------------------------------------

--
-- Table structure for table `awards`
--
-- Creation: Jul 10, 2025 at 01:46 PM
--

CREATE TABLE `awards` (
  `id` int(11) NOT NULL,
  `show_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `type` enum('category','special','overall') NOT NULL DEFAULT 'special',
  `category_id` int(11) DEFAULT NULL,
  `registration_id` int(11) DEFAULT NULL,
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- RELATIONSHIPS FOR TABLE `awards`:
--
