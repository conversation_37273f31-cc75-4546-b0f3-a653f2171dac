
-- --------------------------------------------------------

--
-- Table structure for table `default_categories`
--
-- Creation: Jun 23, 2025 at 08:30 PM
--

CREATE TABLE `default_categories` (
  `id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `registration_fee` decimal(10,2) DEFAULT 0.00,
  `max_entries` int(11) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

--
-- RELATIONSHIPS FOR TABLE `default_categories`:
--

--
-- Dumping data for table `default_categories`
--

INSERT INTO `default_categories` (`id`, `name`, `description`, `registration_fee`, `max_entries`, `created_at`, `updated_at`) VALUES
(6, 'Classic Cars (Pre-1960)', 'Vehicles manufactured before 1960', 0.00, 0, '2025-05-27 19:08:23', '2025-05-29 20:20:34'),
(7, 'Muscle Cars (1960-1979)', 'American muscle cars from the golden era', 0.00, 0, '2025-05-27 19:08:23', '2025-05-29 20:21:02'),
(8, 'Modern Classics (1980-1999)', 'Vehicles from the 80s and 90s', 0.00, 0, '2025-05-27 19:08:23', '2025-05-29 20:20:47'),
(9, 'Contemporary (2000-Present)', 'Modern vehicles from 2000 to present', 0.00, 0, '2025-05-27 19:08:23', '2025-05-29 20:20:38'),
(10, 'European Imports', 'Vehicles manufactured in Europe', 0.00, 0, '2025-05-27 19:08:23', '2025-05-29 20:20:42'),
(11, 'Asian Imports', 'Vehicles manufactured in Asia', 0.00, 0, '2025-05-27 19:08:23', '2025-05-29 20:20:29'),
(12, 'Trucks &amp; SUVs', 'Pickup trucks, SUVs, and utility vehicles', 0.00, 0, '2025-05-27 19:08:23', '2025-05-29 20:21:07'),
(13, 'Modified/Custom', 'Vehicles with significant modifications', 0.00, 0, '2025-05-27 19:08:23', '2025-05-29 20:20:52'),
(14, 'Motorcycles', 'All types of motorcycles', 0.00, 0, '2025-05-27 19:08:23', '2025-05-29 20:20:57');
