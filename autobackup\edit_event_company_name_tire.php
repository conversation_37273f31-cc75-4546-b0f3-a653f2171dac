<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container">
    <div class="row mb-4">
        <div class="col-md-6">
            <h1>Edit Event</h1>
        </div>
        <div class="col-md-6 text-end">
            <div class="btn-group">
                <a href="<?php echo URLROOT; ?>/calendar/event/<?php echo $data['id']; ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left me-2"></i> Back to Event
                </a>
            </div>
        </div>
    </div>
    
    <?php flash('calendar_message'); ?>
    
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">Edit Event Details</h5>
        </div>
        <div class="card-body">
            <form action="<?php echo URLROOT; ?>/calendar/editEvent/<?php echo $data['id']; ?>" method="post" id="editEventForm" enctype="multipart/form-data" novalidate>
                <?php echo csrfTokenField(); ?>
                
                <div class="row mb-3">
                    <div class="col-md-8">
                        <label for="title" class="form-label">Event Title <span class="text-danger">*</span></label>
                        <input type="text" class="form-control <?php echo (!empty($data['title_err'])) ? 'is-invalid' : ''; ?>" id="title" name="title" value="<?php echo $data['title']; ?>" required>
                        <div class="invalid-feedback"><?php echo $data['title_err']; ?></div>
                    </div>
                    <div class="col-md-4">
                        <label for="calendar_id" class="form-label">Calendar <span class="text-danger">*</span></label>
                        <select class="form-select <?php echo (!empty($data['calendar_id_err'])) ? 'is-invalid' : ''; ?>" id="calendar_id" name="calendar_id" required>
                            <option value="">Select Calendar</option>
                            <?php foreach ($data['calendars'] as $calendar): ?>
                            <option value="<?php echo $calendar->id; ?>" <?php echo ($data['calendar_id'] == $calendar->id) ? 'selected' : ''; ?> data-color="<?php echo $calendar->color; ?>">
                                <?php echo $calendar->name; ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                        <div class="invalid-feedback"><?php echo $data['calendar_id_err']; ?></div>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-12">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" rows="4"><?php echo htmlspecialchars($data['description']); ?></textarea>
                        <div class="form-text">Provide a detailed description of the event. You can format text and add images.</div>
                    </div>
                </div>
                
                <!-- Main Event Image Section -->
                <div class="row mb-3">
                    <div class="col-md-12">
                        <label class="form-label">Main Event Image</label>
                        <div class="card">
                            <div class="card-body">
                                <!-- Current Image Display -->
                                <div id="current-image-section" <?php echo empty($data['main_image']) ? 'style="display: none;"' : ''; ?>>
                                    <div class="row align-items-center">
                                        <div class="col-md-3">
                                            <img id="current-image-preview" 
                                                 src="<?php echo !empty($data['main_image']) ? URLROOT . '/' . $data['main_image']->thumbnail_path : ''; ?>" 
                                                 alt="Current main image" 
                                                 class="img-fluid rounded shadow-sm"
                                                 style="max-height: 120px;">
                                        </div>
                                        <div class="col-md-6">
                                            <h6 class="mb-1">Current Main Image</h6>
                                            <p class="text-muted mb-2" id="current-image-name">
                                                <?php echo !empty($data['main_image']) ? $data['main_image']->file_name : ''; ?>
                                            </p>
                                            <small class="text-muted">
                                                <?php 
                                                if (!empty($data['main_image'])) {
                                                    echo 'Size: ' . number_format($data['main_image']->file_size / 1024, 1) . ' KB | ';
                                                    echo 'Dimensions: ' . $data['main_image']->width . 'x' . $data['main_image']->height;
                                                }
                                                ?>
                                            </small>
                                        </div>
                                        <div class="col-md-3 text-end">
                                            <button type="button" class="btn btn-outline-danger btn-sm" id="remove-main-image">
                                                <i class="fas fa-trash-alt me-1"></i> Remove
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Upload New Image Section -->
                                <div id="upload-image-section" <?php echo !empty($data['main_image']) ? 'style="display: none;"' : ''; ?>>
                                    <div class="text-center py-4">
                                        <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                        <h6>Upload Main Event Image</h6>
                                        <p class="text-muted mb-3">Choose a high-quality image that represents your event</p>
                                        <button type="button" class="btn btn-primary" id="select-main-image">
                                            <i class="fas fa-images me-2"></i> Select Image
                                        </button>
                                        <div class="mt-2">
                                            <small class="text-muted">
                                                Accepts JPG and PNG files up to 10MB. Images will be automatically optimized for web performance.
                                            </small>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Change Image Button (when image exists) -->
                                <div id="change-image-section" <?php echo empty($data['main_image']) ? 'style="display: none;"' : ''; ?>>
                                    <div class="text-center mt-3">
                                        <button type="button" class="btn btn-outline-primary btn-sm" id="change-main-image">
                                            <i class="fas fa-exchange-alt me-1"></i> Change Image
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Hidden field to store selected image ID -->
                        <input type="hidden" id="main_image_id" name="main_image_id" value="<?php echo $data['main_image_id'] ?? ''; ?>">
                        
                        <div class="form-text">
                            The main image will be displayed prominently on the event page and used for social media sharing.
                        </div>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-3">
                        <label for="start_date" class="form-label">Start Date & Time <span class="text-danger">*</span></label>
                        <input type="datetime-local" class="form-control <?php echo (!empty($data['start_date_err'])) ? 'is-invalid' : ''; ?>" id="start_date" name="start_date" value="<?php echo $data['start_date']; ?>" required>
                        <div class="form-text">
                            <?php 
                            $userTimezone = getUserTimezone($_SESSION['user_id'] ?? null);
                            $timezoneAbbr = getTimezoneAbbreviation($userTimezone);
                            echo "Enter time in your timezone ($timezoneAbbr)";
                            ?>
                        </div>
                        <div class="invalid-feedback"><?php echo $data['start_date_err']; ?></div>
                    </div>
                    <div class="col-md-3">
                        <label for="end_date" class="form-label">End Date & Time <span class="text-danger">*</span></label>
                        <input type="datetime-local" class="form-control <?php echo (!empty($data['end_date_err'])) ? 'is-invalid' : ''; ?>" id="end_date" name="end_date" value="<?php echo $data['end_date']; ?>" required>
                        <div class="form-text">
                            <?php 
                            $userTimezone = getUserTimezone($_SESSION['user_id'] ?? null);
                            $timezoneAbbr = getTimezoneAbbreviation($userTimezone);
                            echo "Enter time in your timezone ($timezoneAbbr)";
                            ?>
                        </div>
                        <div class="invalid-feedback"><?php echo $data['end_date_err']; ?></div>
                    </div>
                    <div class="col-md-3">
                        <div class="form-check mt-4">
                            <input class="form-check-input" type="checkbox" id="all_day" name="all_day" <?php echo ($data['all_day']) ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="all_day">
                                All Day Event
                            </label>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <label for="color" class="form-label">Event Color</label>
                        <input type="color" class="form-control form-control-color" id="color" name="color" value="<?php echo $data['color'] ?: '#3788d8'; ?>" title="Choose event color">
                        <div class="form-text">Leave empty to use calendar color.</div>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="location" class="form-label">Location Name</label>
                        <input type="text" class="form-control" id="location" name="location" value="<?php echo $data['location']; ?>">
                    </div>
                    
                    <!-- Address Fields -->
                    <div class="col-md-12 mt-3">
                        <h6>Address Details (for Map View)</h6>
                    </div>
                    
                    <div class="col-md-12">
                        <label for="address1" class="form-label">Address Line 1</label>
                        <input type="text" class="form-control" id="address1" name="address1" value="<?php echo isset($data['address1']) ? $data['address1'] : ''; ?>">
                    </div>
                    
                    <div class="col-md-12">
                        <label for="address2" class="form-label">Address Line 2</label>
                        <input type="text" class="form-control" id="address2" name="address2" value="<?php echo isset($data['address2']) ? $data['address2'] : ''; ?>">
                    </div>
                    
                    <div class="col-md-4">
                        <label for="city" class="form-label">City</label>
                        <input type="text" class="form-control" id="city" name="city" value="<?php echo isset($data['city']) ? $data['city'] : ''; ?>">
                    </div>
                    
                    <div class="col-md-4">
                        <label for="state" class="form-label">State</label>
                        <input type="text" class="form-control" id="state" name="state" value="<?php echo isset($data['state']) ? $data['state'] : ''; ?>">
                    </div>
                    
                    <div class="col-md-4">
                        <label for="zipcode" class="form-label">ZIP Code</label>
                        <input type="text" class="form-control" id="zipcode" name="zipcode" value="<?php echo isset($data['zipcode']) ? $data['zipcode'] : ''; ?>">
                    </div>
                    
                    <!-- Hidden fields for coordinates -->
                    <input type="hidden" id="lat" name="lat" value="<?php echo isset($data['lat']) ? $data['lat'] : ''; ?>">
                    <input type="hidden" id="lng" name="lng" value="<?php echo isset($data['lng']) ? $data['lng'] : ''; ?>">
                    <div class="col-md-6">
                        <label for="venue_search" class="form-label">Venue</label>
                        <div class="mb-2">
                            <div class="input-group">
                                <input type="text" class="form-control" id="venue_search" placeholder="Start typing to search venues..." autocomplete="off">
                                <button class="btn btn-outline-secondary" type="button" id="clearVenueBtn">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                            <div id="venue_search_results" class="dropdown-menu w-100" style="max-height: 300px; overflow-y: auto;"></div>
                            <div class="form-text">Type at least 3 characters to search venues. Results will include city and state.</div>
                        </div>
                        
                        <!-- Hidden field to store the selected venue ID -->
                        <input type="hidden" id="venue_id" name="venue_id" value="<?php echo isset($data['venue_id']) ? $data['venue_id'] : ''; ?>">
                        
                        <!-- Display selected venue information -->
                        <div id="selected_venue_info" class="card p-2 mb-2 <?php echo empty($data['venue_id']) ? 'd-none' : ''; ?>">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <strong id="selected_venue_name"><?php 
                                    if (!empty($data['venue_id'])) {
                                        foreach ($data['venues'] as $venue) {
                                            if ($venue->id == $data['venue_id']) {
                                                echo $venue->name;
                                                break;
                                            }
                                        }
                                    }
                                    ?></strong>
                                    <div id="selected_venue_address" class="small text-muted"><?php 
                                    if (!empty($data['venue_id'])) {
                                        foreach ($data['venues'] as $venue) {
                                            if ($venue->id == $data['venue_id']) {
                                                echo (!empty($venue->city) && !empty($venue->state)) ? $venue->city . ', ' . $venue->state : '';
                                                break;
                                            }
                                        }
                                    }
                                    ?></div>
                                </div>
                                <button type="button" class="btn btn-sm btn-outline-danger" id="removeVenueBtn">
                                    <i class="fas fa-trash-alt"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="mt-2">
                            <button type="button" class="btn btn-sm btn-outline-primary" id="createVenueModalBtn">
                                <i class="fas fa-plus-circle"></i> Create New Venue
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="url" class="form-label">URL</label>
                        <input type="url" class="form-control" id="url" name="url" value="<?php echo $data['url']; ?>">
                        <div class="form-text">Optional link to event website or registration page.</div>
                    </div>
                    <div class="col-md-6">
                        <!-- Show ID field is removed as events are now automatically created when shows are created -->
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="clubs" class="form-label">Associated Clubs</label>
                        <div id="club-search-container">
                            <!-- Club search interface will be inserted here by JavaScript -->
                        </div>
                        <div class="form-text">Search and select clubs to associate with this event.</div>
                    </div>
                    <div class="col-md-6">
                        <label for="privacy" class="form-label">Privacy</label>
                        <select class="form-select" id="privacy" name="privacy">
                            <option value="public" <?php echo ($data['privacy'] == 'public') ? 'selected' : ''; ?>>Public - Visible to everyone</option>
                            <option value="draft" <?php echo ($data['privacy'] == 'draft') ? 'selected' : ''; ?>>Draft - Hidden from public view</option>
                        </select>
                    </div>
                </div>
                
                <div class="row mb-3">
                    <div class="col-md-12">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_recurring" name="is_recurring" <?php echo ($data['is_recurring']) ? 'checked' : ''; ?>>
                            <label class="form-check-label" for="is_recurring">
                                Recurring Event
                            </label>
                        </div>
                    </div>
                </div>
                
                <div id="recurring-options" class="row mb-3" style="display: <?php echo ($data['is_recurring']) ? 'flex' : 'none'; ?>;">
                    <div class="col-md-6">
                        <label for="recurrence_pattern" class="form-label">Recurrence Pattern</label>
                        <select class="form-select" id="recurrence_pattern" name="recurrence_pattern">
                            <option value="daily" <?php echo ($data['recurrence_pattern'] == 'daily') ? 'selected' : ''; ?>>Daily</option>
                            <option value="weekly" <?php echo ($data['recurrence_pattern'] == 'weekly') ? 'selected' : ''; ?>>Weekly</option>
                            <option value="monthly" <?php echo ($data['recurrence_pattern'] == 'monthly') ? 'selected' : ''; ?>>Monthly</option>
                            <option value="yearly" <?php echo ($data['recurrence_pattern'] == 'yearly') ? 'selected' : ''; ?>>Yearly</option>
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label for="recurrence_end_date" class="form-label">End Recurrence</label>
                        <input type="date" class="form-control" id="recurrence_end_date" name="recurrence_end_date" value="<?php echo $data['recurrence_end_date'] ? substr($data['recurrence_end_date'], 0, 10) : ''; ?>">
                        <div class="form-text">Leave empty for indefinite recurrence.</div>
                    </div>
                </div>
                
                <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                    <a href="<?php echo URLROOT; ?>/calendar/event/<?php echo $data['id']; ?>" class="btn btn-secondary me-md-2">Cancel</a>
                    <button type="submit" class="btn btn-primary">Update Event</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- JavaScript for form interactions -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Toggle recurring options
        const isRecurringCheckbox = document.getElementById('is_recurring');
        const recurringOptions = document.getElementById('recurring-options');
        
        isRecurringCheckbox.addEventListener('change', function() {
            recurringOptions.style.display = this.checked ? 'flex' : 'none';
        });
        
        // Venue search functionality with autocomplete
        const venueSearchInput = document.getElementById('venue_search');
        const venueSearchResults = document.getElementById('venue_search_results');
        const venueIdInput = document.getElementById('venue_id');
        const selectedVenueInfo = document.getElementById('selected_venue_info');
        const selectedVenueName = document.getElementById('selected_venue_name');
        const selectedVenueAddress = document.getElementById('selected_venue_address');
        const clearVenueBtn = document.getElementById('clearVenueBtn');
        const removeVenueBtn = document.getElementById('removeVenueBtn');
        const locationInput = document.getElementById('location');
        
        // Address fields
        const address1Input = document.getElementById('address1');
        const address2Input = document.getElementById('address2');
        const cityInput = document.getElementById('city');
        const stateInput = document.getElementById('state');
        const zipcodeInput = document.getElementById('zipcode');
        const latInput = document.getElementById('lat');
        const lngInput = document.getElementById('lng');
        
        // Debounce function to limit API calls during typing
        function debounce(func, wait) {
            let timeout;
            return function(...args) {
                clearTimeout(timeout);
                timeout = setTimeout(() => func.apply(this, args), wait);
            };
        }
        
        // Search venues as user types (with debounce)
        venueSearchInput.addEventListener('input', debounce(function() {
            const searchTerm = this.value.trim();
            
            // Clear results if search term is too short
            if (searchTerm.length < 3) {
                venueSearchResults.innerHTML = '';
                venueSearchResults.classList.remove('show');
                return;
            }
            
            // Make an AJAX request to search for venues
            fetch('<?php echo URLROOT; ?>/calendar/searchVenues', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-Token': '<?php echo generateCsrfToken(); ?>'
                },
                body: 'search=' + encodeURIComponent(searchTerm) + '&local_only=true'
            })
            .then(response => response.json())
            .then(data => {
                // Clear existing results
                venueSearchResults.innerHTML = '';
                
                if (data.venues && data.venues.length > 0) {
                    // Add new results
                    data.venues.forEach(venue => {
                        const resultItem = document.createElement('a');
                        resultItem.classList.add('dropdown-item');
                        resultItem.href = '#';
                        resultItem.innerHTML = `<strong>${venue.name}</strong>` + 
                            (venue.city && venue.state ? `<br><small>${venue.city}, ${venue.state}</small>` : '');
                        
                        // Store venue data as attributes
                        resultItem.dataset.id = venue.id;
                        resultItem.dataset.name = venue.name;
                        resultItem.dataset.address = venue.address || '';
                        resultItem.dataset.city = venue.city || '';
                        resultItem.dataset.state = venue.state || '';
                        resultItem.dataset.zip = venue.zip || '';
                        resultItem.dataset.lat = venue.latitude || '';
                        resultItem.dataset.lng = venue.longitude || '';
                        
                        // Handle click on result item
                        resultItem.addEventListener('click', function(e) {
                            e.preventDefault();
                            selectVenue(this.dataset);
                            venueSearchResults.classList.remove('show');
                            venueSearchInput.value = ''; // Clear search input
                        });
                        
                        venueSearchResults.appendChild(resultItem);
                    });
                    
                    // Show results dropdown
                    venueSearchResults.classList.add('show');
                } else {
                    // No results found
                    const noResults = document.createElement('div');
                    noResults.classList.add('dropdown-item', 'text-muted');
                    noResults.textContent = 'No venues found. Try a different search or create a new venue.';
                    venueSearchResults.appendChild(noResults);
                    venueSearchResults.classList.add('show');
                }
            })
            .catch(error => {
                console.error('Error searching venues:', error);
                
                // Show error message
                venueSearchResults.innerHTML = '';
                const errorItem = document.createElement('div');
                errorItem.classList.add('dropdown-item', 'text-danger');
                errorItem.textContent = 'Error searching venues. Please try again.';
                venueSearchResults.appendChild(errorItem);
                venueSearchResults.classList.add('show');
            });
        }, 300)); // 300ms debounce delay
        
        // Function to select a venue and update form fields
        window.selectVenue = function(venueData) {
            console.log('selectVenue called with data:', venueData);
            console.log('venueIdInput element:', venueIdInput);
            
            // Update hidden venue ID input
            venueIdInput.value = venueData.id;
            
            // Update selected venue display
            selectedVenueName.textContent = venueData.name;
            selectedVenueAddress.textContent = venueData.city && venueData.state ? 
                `${venueData.city}, ${venueData.state}` : '';
            selectedVenueInfo.classList.remove('d-none');
            
            // Update location field with venue name
            locationInput.value = venueData.name;
            
            // Update address fields
            address1Input.value = venueData.address || '';
            cityInput.value = venueData.city || '';
            stateInput.value = venueData.state || '';
            zipcodeInput.value = venueData.zip || '';
            
            // Update coordinates if available
            if (venueData.lat) {
                latInput.value = venueData.lat;
            }
            if (venueData.lng) {
                lngInput.value = venueData.lng;
            }
            
            // Coordinates will be automatically handled by the server if missing
        }
        
        // Clear venue search input
        clearVenueBtn.addEventListener('click', function() {
            venueSearchInput.value = '';
            venueSearchResults.innerHTML = '';
            venueSearchResults.classList.remove('show');
        });
        
        // Remove selected venue
        removeVenueBtn.addEventListener('click', function() {
            // Clear venue ID
            venueIdInput.value = '';
            
            // Hide selected venue info
            selectedVenueInfo.classList.add('d-none');
        });
        
        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!venueSearchInput.contains(e.target) && !venueSearchResults.contains(e.target)) {
                venueSearchResults.classList.remove('show');
            }
        });
        
        // Coordinates are now automatically handled by the server
        // No client-side geocoding is needed
        
        // Calendar color
        const calendarSelect = document.getElementById('calendar_id');
        const colorInput = document.getElementById('color');
        
        calendarSelect.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            if (selectedOption.value && (!colorInput.value || confirm('Update color with calendar color?'))) {
                // If a calendar is selected and no color is set, use the calendar color
                const calendarColor = selectedOption.getAttribute('data-color');
                if (calendarColor) {
                    colorInput.value = calendarColor;
                }
            }
        });
    });
</script>

<!-- WYSIWYG Editor, Club Search, and Venue Modal CSS and JS -->
<link rel="stylesheet" href="<?php echo URLROOT; ?>/public/css/wysiwyg-editor.css">
<link rel="stylesheet" href="<?php echo URLROOT; ?>/public/css/club-search.css">
<link rel="stylesheet" href="<?php echo URLROOT; ?>/public/css/venue-modal.css">
<script src="<?php echo URLROOT; ?>/public/js/wysiwyg-editor.js"></script>
<script src="<?php echo URLROOT; ?>/public/js/club-search.js"></script>
<script src="<?php echo URLROOT; ?>/public/js/venue-modal.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Get event image settings from server
    fetch('<?php echo URLROOT; ?>/calendar/getEventImageSettings', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(settings => {
        // Store settings globally for WYSIWYG editor
        window.eventImageSettings = settings;
        
        // Initialize WYSIWYG editor if enabled
        if (settings.enableWysiwyg) {
            const descriptionField = document.getElementById('description');
            if (descriptionField) {
                new SecureWYSIWYGEditor('description', {
                    maxImages: settings.maxImages,
                    maxImageSize: settings.maxImageSize * 1024 * 1024, // Convert MB to bytes
                    allowedImageTypes: settings.allowedTypes
                });
            }
        }
    })
    .catch(error => {
        console.error('Error loading event image settings:', error);
        // Initialize with defaults if settings fail to load
        window.eventImageSettings = {
            maxImages: 2,
            maxImageSize: 2,
            allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'],
            enableWysiwyg: true
        };
        
        const descriptionField = document.getElementById('description');
        if (descriptionField) {
            new SecureWYSIWYGEditor('description', {
                maxImages: 2,
                maxImageSize: 2 * 1024 * 1024
            });
        }
    });
    
    // Initialize club search manager
    const clubSearchContainer = document.getElementById('club-search-container');
    if (clubSearchContainer) {
        window.clubSearchManager = new ClubSearchManager('club-search-container', {
            searchUrl: '<?php echo URLROOT; ?>/calendar/searchClubs',
            createUrl: '<?php echo URLROOT; ?>/calendar/createClubAjax'
        });
        
        // Load existing selected clubs
        const existingClubs = <?php 
            if (!empty($data['clubs'])) {
                $clubsData = [];
                foreach ($data['clubs'] as $clubId) {
                    foreach ($data['clubs_list'] as $club) {
                        if ($club->id == $clubId) {
                            $clubsData[] = [
                                'id' => $club->id,
                                'name' => $club->name,
                                'description' => isset($club->description) ? $club->description : ''
                            ];
                            break;
                        }
                    }
                }
                echo json_encode($clubsData);
            } else {
                echo '[]';
            }
            ?>;
        
        if (existingClubs.length > 0) {
            window.clubSearchManager.setSelectedClubs(existingClubs);
        }
    }
});
</script>

<!-- WYSIWYG Editor and Club Search CSS and JS -->
<link rel="stylesheet" href="<?php echo URLROOT; ?>/public/css/wysiwyg-editor.css">
<link rel="stylesheet" href="<?php echo URLROOT; ?>/public/css/club-search.css">
<script src="<?php echo URLROOT; ?>/public/js/wysiwyg-editor.js"></script>
<script src="<?php echo URLROOT; ?>/public/js/club-search.js"></script>

<!-- Image Selector Styles -->
<style>
.border-dashed {
    border: 2px dashed #dee2e6 !important;
    transition: border-color 0.3s ease;
}

.border-dashed:hover {
    border-color: #007bff !important;
}

.image-selector-card {
    cursor: pointer;
    transition: all 0.3s ease;
}

.image-selector-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.image-selector-card .card {
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.image-selector-card .card.border-primary {
    border-color: #007bff !important;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

.image-selector-card .card.bg-light {
    background-color: #f8f9fa !important;
}

/* Tire Progress Bar Styles */
.tire-progress-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
}

.tire-progress {
    position: relative;
    width: 120px;
    height: 120px;
    margin: 10px auto;
}

.tire-progress svg {
    transform: rotate(-90deg);
    width: 100%;
    height: 100%;
}

.tire-progress .tire-outer {
    fill: none;
    stroke: #2c3e50;
    stroke-width: 8;
    opacity: 0.3;
}

.tire-progress .tire-inner {
    fill: none;
    stroke: #34495e;
    stroke-width: 4;
    opacity: 0.2;
}

.tire-progress .tire-fill {
    fill: none;
    stroke: #e74c3c;
    stroke-width: 8;
    stroke-linecap: round;
    stroke-dasharray: 314.16; /* 2 * PI * 50 (radius) */
    stroke-dashoffset: 314.16;
    transition: stroke-dashoffset 0.3s ease;
}

.tire-progress .tire-center {
    fill: #34495e;
}

.tire-progress .tire-spokes {
    stroke: #7f8c8d;
    stroke-width: 2;
    opacity: 0.6;
}

.tire-progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 16px;
    font-weight: bold;
    color: #1338BE; /* Light blue for better visibility on black center */
    text-align: center;
    text-shadow: 0 0 3px rgba(255, 255, 255, 0.8); /* White glow for better readability */
}

.tire-progress-label {
    margin-top: 10px;
    font-size: 14px;
    color: #7f8c8d;
    text-align: center;
}

/* Animation for the tire rotation */
@keyframes tire-spin {
    from { transform: rotate(-90deg); }
    to { transform: rotate(270deg); }
}

.tire-progress.spinning svg {
    animation: tire-spin 2s linear infinite;
}

/* Fixed Overlay Progress Bar (Mobile-Friendly) */
#tire-progress-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    z-index: 9999; /* Above modals */
    justify-content: center;
    align-items: center;
}

.tire-progress-overlay-content {
    background: white;
    border-radius: 15px;
    padding: 30px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    max-width: 90vw;
    max-height: 90vh;
}

/* Mobile optimizations */
@media (max-width: 768px) {
    .tire-progress-overlay-content {
        padding: 20px;
        border-radius: 10px;
    }

    .tire-progress {
        width: 100px !important;
        height: 100px !important;
    }

    .tire-progress-text {
        font-size: 14px !important;
    }

    .tire-progress-label {
        font-size: 12px !important;
    }
}

/* Very small screens */
@media (max-width: 480px) {
    .tire-progress-overlay-content {
        padding: 15px;
        margin: 10px;
    }

    .tire-progress {
        width: 80px !important;
        height: 80px !important;
    }

    .tire-progress-text {
        font-size: 12px !important;
    }
}

.tire-progress-details {
    margin-top: 10px;
    color: #6c757d;
    font-size: 12px;
}

/* Company name text around the tire */
.tire-company-text {
    font-family: 'Arial Black', Arial, sans-serif;
    font-size: 6px;
    font-weight: bold;
    fill: #7f8c8d;
    opacity: 0.7;
    letter-spacing: 1px;
}

/* Animation for the company text rotation */
@keyframes company-text-spin {
    from { transform: rotate(-90deg); }
    to { transform: rotate(270deg); }
}

.tire-progress.spinning .tire-company-text {
    animation: company-text-spin 4s linear infinite; /* Slower than tire for cool effect */
    transform-origin: 60px 60px;
}

.main-image-preview {
    max-height: 120px;
    object-fit: cover;
    border-radius: 8px;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Function to initialize WYSIWYG editor
    function initializeWYSIWYGEditor(settings) {
        const descriptionField = document.getElementById('description');
        // Debug: Attempting to initialize WYSIWYG editor
        
        if (descriptionField && settings.enableWysiwyg && !descriptionField.dataset.wysiwygInitialized) {
            // Creating new WYSIWYG editor instance
            new SecureWYSIWYGEditor('description', {
                maxImages: settings.maxImages,
                maxImageSize: settings.maxImageSize * 1024 * 1024, // Convert MB to bytes
                allowedImageTypes: settings.allowedTypes
            });
            descriptionField.dataset.wysiwygInitialized = 'true';
        } else {
            // WYSIWYG editor initialization skipped
        }
    }
    
    // Get event image settings from server
    fetch('<?php echo URLROOT; ?>/calendar/getEventImageSettings', {
        method: 'GET',
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(settings => {
        // Store settings globally for WYSIWYG editor
        window.eventImageSettings = settings;
        
        // Initialize WYSIWYG editor if enabled
        initializeWYSIWYGEditor(settings);
    })
    .catch(error => {
        console.error('Error loading event image settings:', error);
        // Initialize with defaults if settings fail to load
        window.eventImageSettings = {
            maxImages: 2,
            maxImageSize: 2,
            allowedTypes: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'],
            enableWysiwyg: true
        };
        
        // Initialize WYSIWYG editor with defaults if enabled
        initializeWYSIWYGEditor(window.eventImageSettings);
    });
    
    // Initialize venue modal
    initializeVenueModal();
    
    // Initialize main image functionality
    initializeMainImageSelector();
});

// Main Image Selector Functions
function initializeMainImageSelector() {
    const selectImageBtn = document.getElementById('select-main-image');
    const changeImageBtn = document.getElementById('change-main-image');
    const removeImageBtn = document.getElementById('remove-main-image');
    
    // Select/Change image button click
    if (selectImageBtn) {
        selectImageBtn.addEventListener('click', openImageSelector);
    }
    if (changeImageBtn) {
        changeImageBtn.addEventListener('click', openImageSelector);
    }
    
    // Remove image button click
    if (removeImageBtn) {
        removeImageBtn.addEventListener('click', removeMainImage);
    }
}

function openImageSelector() {
    // Open image selector in a modal
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.id = 'imageSelector';
    modal.innerHTML = `
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Select Main Event Image</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row mb-3">
                        <div class="col-md-8">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-outline-primary active" id="browse-images-tab">
                                    <i class="fas fa-images me-2"></i>Browse Images
                                </button>
                                <button type="button" class="btn btn-outline-primary" id="upload-new-tab">
                                    <i class="fas fa-upload me-2"></i>Upload New
                                </button>
                            </div>
                        </div>
                        <div class="col-md-4 text-end">
                            <button type="button" class="btn btn-success" id="select-image-btn" disabled>
                                <i class="fas fa-check me-2"></i>Select Image
                            </button>
                        </div>
                    </div>
                    
                    <!-- Browse Images Tab -->
                    <div id="browse-images-content">
                        <div id="image-grid" class="row">
                            <div class="col-12 text-center py-4">
                                <div class="spinner-border" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <p class="mt-2">Loading images...</p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Upload New Tab -->
                    <div id="upload-new-content" style="display: none;">
                        <div class="card">
                            <div class="card-body text-center">
                                <div id="upload-area" class="border-dashed p-4 rounded">
                                    <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                                    <h6>Upload New Image</h6>
                                    <p class="text-muted">Drag and drop an image here, or click to select</p>
                                    <input type="file" id="image-upload" accept="image/jpeg,image/jpg,image/png" style="display: none;">
                                    <button type="button" class="btn btn-primary" onclick="document.getElementById('image-upload').click()">
                                        Choose File
                                    </button>
                                    <div class="mt-2">
                                        <small class="text-muted">JPG, PNG up to 10MB</small>
                                    </div>
                                </div>
                                <!-- Small inline progress indicator -->
                                <div id="upload-progress-inline" style="display: none;" class="mt-3">
                                    <div class="text-center">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Uploading...</span>
                                        </div>
                                        <p class="mt-2 mb-0">Uploading image...</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const bsModal = new bootstrap.Modal(modal);
    bsModal.show();

    // Create fixed overlay progress bar (mobile-friendly)
    if (!document.getElementById('tire-progress-overlay')) {
        const overlay = document.createElement('div');
        overlay.id = 'tire-progress-overlay';
        overlay.innerHTML = `
            <div class="tire-progress-overlay-content">
                <div class="tire-progress" id="tire-progress">
                    <svg viewBox="0 0 120 120">
                        <!-- Define circular path for company name -->
                        <defs>
                            <path id="circle-path" d="M 60, 60 m -42, 0 a 42,42 0 1,1 84,0 a 42,42 0 1,1 -84,0" />
                        </defs>

                        <!-- Tire outer rim -->
                        <circle cx="60" cy="60" r="50" class="tire-outer"></circle>
                        <!-- Tire inner rim -->
                        <circle cx="60" cy="60" r="35" class="tire-inner"></circle>

                        <!-- Company name spinning around the tire -->
                        <text class="tire-company-text">
                            <textPath href="#circle-path" startOffset="0%">
                                ROWAN ELITE RIDES • ROWAN ELITE RIDES •
                            </textPath>
                        </text>

                        <!-- Tire spokes -->
                        <g class="tire-spokes">
                            <line x1="60" y1="20" x2="60" y2="35" stroke="#7f8c8d" stroke-width="2"></line>
                            <line x1="60" y1="85" x2="60" y2="100" stroke="#7f8c8d" stroke-width="2"></line>
                            <line x1="20" y1="60" x2="35" y2="60" stroke="#7f8c8d" stroke-width="2"></line>
                            <line x1="85" y1="60" x2="100" y2="60" stroke="#7f8c8d" stroke-width="2"></line>
                            <line x1="35.86" y1="35.86" x2="46.46" y2="46.46" stroke="#7f8c8d" stroke-width="2"></line>
                            <line x1="73.54" y1="73.54" x2="84.14" y2="84.14" stroke="#7f8c8d" stroke-width="2"></line>
                            <line x1="84.14" y1="35.86" x2="73.54" y2="46.46" stroke="#7f8c8d" stroke-width="2"></line>
                            <line x1="46.46" y1="73.54" x2="35.86" y2="84.14" stroke="#7f8c8d" stroke-width="2"></line>
                        </g>
                        <!-- Progress fill -->
                        <circle cx="60" cy="60" r="50" class="tire-fill" id="tire-fill"></circle>
                        <!-- Center hub -->
                        <circle cx="60" cy="60" r="15" class="tire-center"></circle>
                    </svg>
                    <div class="tire-progress-text" id="tire-progress-text">0%</div>
                </div>
                <div class="tire-progress-label">Uploading Image...</div>
                <div class="tire-progress-details">
                    <small id="upload-speed"></small>
                </div>
            </div>
        `;
        document.body.appendChild(overlay);
    }
    
    // Load images
    loadEventImages();
    
    // Setup tab switching
    setupImageSelectorTabs();
    
    // Setup upload functionality
    setupImageUpload();
    
    // Cleanup when modal is closed
    modal.addEventListener('hidden.bs.modal', function() {
        document.body.removeChild(modal);
    });
}

function loadEventImages() {
    const eventId = <?php echo $data['id']; ?>;
    
    fetch(`<?php echo URLROOT; ?>/image_editor/getEventImages/${eventId}`)
        .then(response => response.json())
        .then(data => {
            const imageGrid = document.getElementById('image-grid');
            
            if (data.success && data.images && data.images.length > 0) {
                imageGrid.innerHTML = '';
                
                data.images.forEach(image => {
                    const imageCard = document.createElement('div');
                    imageCard.className = 'col-md-3 col-sm-4 col-6 mb-3';
                    imageCard.innerHTML = `
                        <div class="card image-selector-card" data-image-id="${image.id}">
                            <img src="<?php echo URLROOT; ?>/${image.thumbnail_path}" 
                                 class="card-img-top" 
                                 style="height: 150px; object-fit: cover;">
                            <div class="card-body p-2">
                                <small class="text-muted">${image.file_name}</small>
                                <br>
                                <small class="text-muted">${Math.round(image.file_size / 1024)} KB</small>
                            </div>
                        </div>
                    `;
                    
                    imageCard.addEventListener('click', function() {
                        selectImageInGrid(this, image);
                    });
                    
                    imageGrid.appendChild(imageCard);
                });
            } else {
                imageGrid.innerHTML = `
                    <div class="col-12 text-center py-4">
                        <i class="fas fa-images fa-3x text-muted mb-3"></i>
                        <h6>No Images Found</h6>
                        <p class="text-muted">Upload some images for this event first, or upload a new image using the Upload New tab.</p>
                    </div>
                `;
            }
        })
        .catch(error => {
            console.error('Error loading images:', error);
            document.getElementById('image-grid').innerHTML = `
                <div class="col-12 text-center py-4">
                    <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                    <h6>Error Loading Images</h6>
                    <p class="text-muted">Please try again or upload a new image.</p>
                </div>
            `;
        });
}

let selectedImageData = null;

function selectImageInGrid(cardElement, imageData) {
    // Remove previous selection
    document.querySelectorAll('.image-selector-card').forEach(card => {
        card.classList.remove('border-primary', 'bg-light');
    });
    
    // Add selection to clicked card
    cardElement.querySelector('.card').classList.add('border-primary', 'bg-light');
    
    // Store selected image data
    selectedImageData = imageData;
    
    // Enable select button
    document.getElementById('select-image-btn').disabled = false;
}

function setupImageSelectorTabs() {
    const browseTab = document.getElementById('browse-images-tab');
    const uploadTab = document.getElementById('upload-new-tab');
    const browseContent = document.getElementById('browse-images-content');
    const uploadContent = document.getElementById('upload-new-content');
    
    browseTab.addEventListener('click', function() {
        browseTab.classList.add('active');
        uploadTab.classList.remove('active');
        browseContent.style.display = 'block';
        uploadContent.style.display = 'none';
    });
    
    uploadTab.addEventListener('click', function() {
        uploadTab.classList.add('active');
        browseTab.classList.remove('active');
        browseContent.style.display = 'none';
        uploadContent.style.display = 'block';
    });
}

function setupImageUpload() {
    const fileInput = document.getElementById('image-upload');
    const uploadArea = document.getElementById('upload-area');
    const selectBtn = document.getElementById('select-image-btn');
    
    fileInput.addEventListener('change', function(e) {
        if (e.target.files.length > 0) {
            uploadImage(e.target.files[0]);
        }
    });
    
    // Drag and drop functionality
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        uploadArea.classList.add('border-primary');
    });
    
    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('border-primary');
    });
    
    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('border-primary');
        
        if (e.dataTransfer.files.length > 0) {
            uploadImage(e.dataTransfer.files[0]);
        }
    });
    
    // Select image button
    selectBtn.addEventListener('click', function() {
        if (selectedImageData) {
            setMainImage(selectedImageData);
            bootstrap.Modal.getInstance(document.getElementById('imageSelector')).hide();
        }
    });
}

function uploadImage(file) {
    // Validate file
    if (!file.type.match(/^image\/(jpeg|png|gif|webp)$/)) {
        alert('Please select a valid image file (JPG, PNG, GIF, or WebP).');
        return;
    }
    
    if (file.size > 10 * 1024 * 1024) {
        alert('File size must be less than 10MB.');
        return;
    }
    
    const formData = new FormData();
    formData.append('image', file);
    formData.append('entity_type', 'event');
    formData.append('entity_id', <?php echo $data['id']; ?>);
    formData.append('title', 'Event Main Image');
    formData.append('<?php echo CSRF_TOKEN_NAME; ?>', '<?php echo $_SESSION['csrf_token']; ?>');
    
    // Show fixed overlay progress bar (mobile-friendly)
    const progressOverlay = document.getElementById('tire-progress-overlay');
    const tireProgress = document.getElementById('tire-progress');
    const tireFill = document.getElementById('tire-fill');
    const progressText = document.getElementById('tire-progress-text');
    const uploadSpeed = document.getElementById('upload-speed');

    // Show overlay and start tire spinning
    progressOverlay.style.display = 'flex';
    tireProgress.classList.add('spinning');

    // Track upload speed
    let startTime = Date.now();
    let lastLoaded = 0;

    // Create XMLHttpRequest for progress tracking
    const xhr = new XMLHttpRequest();

    // Track upload progress
    xhr.upload.addEventListener('progress', function(e) {
        if (e.lengthComputable) {
            const percentComplete = Math.round((e.loaded / e.total) * 100);
            updateTireProgress(percentComplete);

            // Calculate upload speed
            const currentTime = Date.now();
            const timeElapsed = (currentTime - startTime) / 1000; // seconds
            const bytesUploaded = e.loaded - lastLoaded;
            const speed = bytesUploaded / timeElapsed; // bytes per second

            if (timeElapsed > 1) { // Update speed every second
                const speedMB = (speed / (1024 * 1024)).toFixed(1);
                const remainingBytes = e.total - e.loaded;
                const remainingTime = Math.round(remainingBytes / speed);

                if (uploadSpeed) {
                    uploadSpeed.textContent = `${speedMB} MB/s • ${remainingTime}s remaining`;
                }

                lastLoaded = e.loaded;
                startTime = currentTime;
            }
        }
    });

    // Handle completion
    xhr.addEventListener('load', function() {
        tireProgress.classList.remove('spinning');

        if (xhr.status === 200) {
            try {
                const data = JSON.parse(xhr.responseText);

                if (data.success) {
                    // Show 100% completion briefly
                    updateTireProgress(100);

                    setTimeout(() => {
                        progressOverlay.style.display = 'none';

                        // Automatically select the uploaded image
                        selectedImageData = data.image;
                        setMainImage(data.image);
                        bootstrap.Modal.getInstance(document.getElementById('imageSelector')).hide();
                    }, 500);
                } else {
                    progressOverlay.style.display = 'none';
                    alert('Upload failed: ' + (data.message || 'Unknown error'));
                }
            } catch (e) {
                progressOverlay.style.display = 'none';
                alert('Upload failed: Invalid response from server');
            }
        } else {
            progressOverlay.style.display = 'none';
            alert('Upload failed: Server error (Status: ' + xhr.status + ')');
        }
    });

    // Handle errors
    xhr.addEventListener('error', function() {
        tireProgress.classList.remove('spinning');
        progressOverlay.style.display = 'none';
        alert('Upload failed: Network error');
    });

    // Handle abort
    xhr.addEventListener('abort', function() {
        tireProgress.classList.remove('spinning');
        progressOverlay.style.display = 'none';
    });

    // Send the request
    xhr.open('POST', '<?php echo URLROOT; ?>/image_editor/upload_ajax');
    xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
    xhr.send(formData);
}

// Function to update tire progress
function updateTireProgress(percent) {
    const tireFill = document.getElementById('tire-fill');
    const progressText = document.getElementById('tire-progress-text');

    // Calculate the stroke-dashoffset based on percentage
    // Full circumference is 314.16 (2 * PI * 50)
    const circumference = 314.16;
    const offset = circumference - (percent / 100) * circumference;

    // Update the tire fill
    tireFill.style.strokeDashoffset = offset;

    // Update the percentage text
    progressText.textContent = percent + '%';

    // Change tire color based on progress
    if (percent < 30) {
        tireFill.style.stroke = '#e74c3c'; // Red for low progress
    } else if (percent < 70) {
        tireFill.style.stroke = '#f39c12'; // Orange for medium progress
    } else {
        tireFill.style.stroke = '#27ae60'; // Green for high progress
    }
}

function setMainImage(imageData) {
    // Get the current image ID before changing it
    const currentImageId = document.getElementById('main_image_id').value;

    // If there's already a main image and it's different from the new one, delete the old one
    if (currentImageId && currentImageId !== imageData.id.toString()) {
        // Delete the old main image from the server
        const formData = new FormData();
        formData.append('image_id', currentImageId);
        formData.append('<?php echo CSRF_TOKEN_NAME; ?>', '<?php echo $_SESSION['csrf_token']; ?>');

        fetch('<?php echo URLROOT; ?>/image_editor/deleteImage', {
            method: 'POST',
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('Old main image deleted successfully');
            } else {
                console.warn('Failed to delete old main image:', data.message);
            }
        })
        .catch(error => {
            console.error('Error deleting old main image:', error);
        });
    }

    // Update hidden field
    document.getElementById('main_image_id').value = imageData.id;

    // Update UI
    const currentImageSection = document.getElementById('current-image-section');
    const uploadImageSection = document.getElementById('upload-image-section');
    const changeImageSection = document.getElementById('change-image-section');

    // Update image preview
    document.getElementById('current-image-preview').src = '<?php echo URLROOT; ?>/' + imageData.thumbnail_path;
    document.getElementById('current-image-name').textContent = imageData.file_name;

    // Show current image section, hide upload section
    currentImageSection.style.display = 'block';
    uploadImageSection.style.display = 'none';
    changeImageSection.style.display = 'block';
}

function removeMainImage() {
    if (confirm('Are you sure you want to remove the main image? This will permanently delete the image from the server.')) {
        const currentImageId = document.getElementById('main_image_id').value;

        if (currentImageId) {
            // Delete the image from the server
            const formData = new FormData();
            formData.append('image_id', currentImageId);
            formData.append('<?php echo CSRF_TOKEN_NAME; ?>', '<?php echo $_SESSION['csrf_token']; ?>');

            fetch('<?php echo URLROOT; ?>/image_editor/deleteImage', {
                method: 'POST',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Clear hidden field
                    document.getElementById('main_image_id').value = '';

                    // Update UI
                    const currentImageSection = document.getElementById('current-image-section');
                    const uploadImageSection = document.getElementById('upload-image-section');
                    const changeImageSection = document.getElementById('change-image-section');

                    currentImageSection.style.display = 'none';
                    uploadImageSection.style.display = 'block';
                    changeImageSection.style.display = 'none';

                    // Refresh the image browser if it's open
                    if (typeof loadEventImages === 'function') {
                        loadEventImages();
                    }
                } else {
                    alert('Failed to delete image: ' + (data.message || 'Unknown error'));
                }
            })
            .catch(error => {
                console.error('Error deleting image:', error);
                alert('Failed to delete image. Please try again.');
            });
        } else {
            // No image ID, just clear the UI
            document.getElementById('main_image_id').value = '';

            const currentImageSection = document.getElementById('current-image-section');
            const uploadImageSection = document.getElementById('upload-image-section');
            const changeImageSection = document.getElementById('change-image-section');

            currentImageSection.style.display = 'none';
            uploadImageSection.style.display = 'block';
            changeImageSection.style.display = 'none';
        }
    }
}

function initializeVenueModal() {
    // Initialize the venue modal
    const venueModal = new VenueModal({
        apiEndpoint: '<?php echo URLROOT; ?>/calendar/createVenueApi',
        csrfToken: '<?php echo $_SESSION['csrf_token']; ?>',
        onVenueCreated: function(venue) {
            // Automatically select the newly created venue
            console.log('onVenueCreated callback called with venue:', venue);
            console.log('selectVenue function exists:', typeof window.selectVenue);
            
            if (typeof window.selectVenue === 'function') {
                window.selectVenue({
                    id: venue.id,
                    name: venue.name,
                    address: venue.address || '',
                    city: venue.city || '',
                    state: venue.state || '',
                    zip: venue.zip || '',
                    lat: venue.latitude || '',
                    lng: venue.longitude || ''
                });
            } else {
                console.error('selectVenue function is not available');
                // Fallback: just close the modal without selecting
                alert('Venue created successfully! Please refresh the page and select the venue manually.');
            }
        }
    });
    
    // Attach click event to the create venue button
    const createVenueBtn = document.getElementById('createVenueModalBtn');
    if (createVenueBtn) {
        createVenueBtn.addEventListener('click', function() {
            venueModal.open();
        });
    }
}
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>