
-- --------------------------------------------------------

--
-- Table structure for table `field_mappings`
--
-- Creation: Jun 23, 2025 at 08:30 PM
--

CREATE TABLE `field_mappings` (
  `id` int(11) NOT NULL,
  `form_field_id` varchar(255) DEFAULT NULL,
  `template_field_id` varchar(255) DEFAULT NULL,
  `db_column` varchar(255) DEFAULT NULL,
  `database_column` varchar(255) DEFAULT NULL,
  `field_type` varchar(50) DEFAULT 'text',
  `created_at` datetime DEFAULT current_timestamp(),
  `updated_at` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- RELATIONSHIPS FOR TABLE `field_mappings`:
--

--
-- Dumping data for table `field_mappings`
--

INSERT INTO `field_mappings` (`id`, `form_field_id`, `template_field_id`, `db_column`, `database_column`, `field_type`, `created_at`, `updated_at`) VALUES
(33, 'field_1748214173466', 'field_1748214173466', 'field_1748214173466', 'field_1748214173466', 'text', '2025-05-25 19:03:17', '2025-06-04 11:55:35'),
(34, 'field_1748192695244', 'field_1748192695244', 'field_1748192695244', 'field_1748192695244', 'text', '2025-05-25 21:25:04', '2025-06-04 11:55:35'),
(35, 'field_1748193065612', 'field_1748193065612', 'field_1748193065612', 'field_1748193065612', 'text', '2025-05-25 21:25:05', '2025-06-04 11:55:35'),
(36, 'vehicle_id', 'vehicle_id', 'vehicle_id', 'vehicle_id', 'select', '2025-05-27 07:48:25', '2025-05-27 07:48:25'),
(37, 'category_id', 'category_id', 'category_id', 'category_id', 'select', '2025-05-27 07:48:25', '2025-05-27 07:48:25'),
(38, 'plate', 'plate', 'plate', 'plate', 'text', '2025-05-27 07:48:25', '2025-05-27 07:48:25'),
(39, 'vehiclename', 'vehiclename', 'vehiclename', 'vehiclename', 'text', '2025-05-27 07:48:25', '2025-05-27 07:48:25'),
(40, 'special_features', 'special_features', 'special_features', 'special_features', 'textarea', '2025-05-27 07:48:25', '2025-05-27 07:48:25'),
(41, 'vehicle_condition', 'vehicle_condition', 'vehicle_condition', 'vehicle_condition', 'select', '2025-05-27 07:48:25', '2025-05-27 07:48:25'),
(42, 'year_of_manufacture', 'year_of_manufacture', 'year_of_manufacture', 'year_of_manufacture', 'select', '2025-05-27 07:48:25', '2025-05-27 18:37:38'),
(43, 'payment_method_id', 'payment_method_id', 'payment_method_id', 'payment_method_id', 'select', '2025-05-27 07:48:25', '2025-05-27 07:48:25'),
(44, 'additional_notes', 'additional_notes', 'additional_notes', 'additional_notes', 'textarea', '2025-05-27 07:48:25', '2025-05-27 07:48:25'),
(45, 'agree_terms', 'agree_terms', 'agree_terms', 'agree_terms', 'checkbox', '2025-05-27 07:48:25', '2025-05-27 07:48:25'),
(46, 'test', 'test', 'test', 'test', 'text', '2025-06-04 11:31:48', '2025-06-10 06:29:26'),
(47, 'jsdfhsdf', 'jsdfhsdf', 'jsdfhsdf', 'jsdfhsdf', 'textarea', '2025-06-04 13:04:30', '2025-06-10 06:29:26'),
(48, 'eee', 'eee', 'eee', 'eee', 'radio', '2025-06-04 13:04:30', '2025-06-10 06:29:26'),
(49, 'rich', 'rich', 'rich', 'rich', 'richtext', '2025-06-04 13:10:21', '2025-06-10 06:29:26'),
(50, 'radio_choice', 'radio_choice', 'radio_choice', 'radio_choice', 'radio', '2025-06-04 13:29:56', '2025-06-04 13:29:56'),
(51, 'radio_option', 'radio_option', 'radio_option', 'radio_option', 'radio', '2025-06-04 13:29:56', '2025-06-04 13:29:56'),
(52, 'radio_selection', 'radio_selection', 'radio_selection', 'radio_selection', 'radio', '2025-06-04 13:29:56', '2025-06-04 13:29:56'),
(53, 'radio_preference', 'radio_preference', 'radio_preference', 'radio_preference', 'radio', '2025-06-04 13:29:56', '2025-06-04 13:29:56'),
(54, 'choice_radio', 'choice_radio', 'choice_radio', 'choice_radio', 'radio', '2025-06-04 13:29:56', '2025-06-04 13:29:56'),
(55, 'option_radio', 'option_radio', 'option_radio', 'option_radio', 'radio', '2025-06-04 13:29:56', '2025-06-04 13:29:56'),
(56, 'selection_radio', 'selection_radio', 'selection_radio', 'selection_radio', 'radio', '2025-06-04 13:29:56', '2025-06-04 13:29:56'),
(57, 'preference_radio', 'preference_radio', 'preference_radio', 'preference_radio', 'radio', '2025-06-04 13:29:56', '2025-06-04 13:29:56'),
(58, 'chk', 'chk', 'chk', 'chk', 'checkbox', '2025-06-04 14:16:04', '2025-06-10 06:29:26'),
(59, 'drp', 'drp', 'drp', 'drp', 'select', '2025-06-04 14:16:04', '2025-06-10 06:29:26'),
(60, 'datandtime', 'datandtime', 'datandtime', 'datandtime', 'datetime', '2025-06-04 14:16:04', '2025-06-10 06:29:26'),
(61, 'email', 'email', 'email', 'email', 'email', '2025-06-04 14:16:04', '2025-06-10 06:29:26'),
(62, 'field_1749561018636', 'field_1749561018636', 'field_1749561018636', 'field_1749561018636', 'text', '2025-06-10 15:56:37', '2025-06-24 12:37:32'),
(63, 'address1', 'address1', 'address1', 'address1', 'text', '2025-06-16 10:24:41', '2025-07-21 00:10:27'),
(64, 'address2', 'address2', 'address2', 'address2', 'text', '2025-06-16 10:24:41', '2025-07-21 00:10:27'),
(65, 'city', 'city', 'city', 'city', 'text', '2025-06-16 10:24:41', '2025-07-21 00:10:27'),
(66, 'state', 'state', 'state', 'state', 'select', '2025-06-16 10:24:41', '2025-07-21 00:10:27'),
(67, 'zipcode', 'zipcode', 'zipcode', 'zipcode', 'text', '2025-06-16 10:24:41', '2025-07-21 00:10:27'),
(68, 'club', 'club', 'club', 'club', 'text', '2025-06-24 13:31:28', '2025-07-21 00:10:27'),
(69, 'Judge_time', 'Judge_time', 'Judge_time', 'Judge_time', 'time', '2025-07-19 19:07:23', '2025-07-21 00:10:27'),
(70, 'awards_time', 'awards_time', 'awards_time', 'awards_time', 'time', '2025-07-19 19:07:23', '2025-07-21 00:10:27');
