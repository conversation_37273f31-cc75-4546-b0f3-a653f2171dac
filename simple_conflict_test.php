<?php
/**
 * Simple Judging Conflicts Test
 */

require_once 'config/config.php';
require_once 'core/Database.php';

echo "<h1>Simple Judging Conflicts Test</h1>\n";

try {
    echo "<p>Testing database connection...</p>\n";
    $db = new Database();
    echo "<p style='color: green;'>✓ Database connected</p>\n";
    
    echo "<p>Testing judging_conflicts table...</p>\n";
    $db->query("SELECT COUNT(*) as count FROM judging_conflicts");
    $result = $db->single();
    echo "<p style='color: green;'>✓ judging_conflicts table accessible (count: {$result->count})</p>\n";
    
    echo "<p>Loading JudgingConflictModel...</p>\n";
    require_once APPROOT . '/models/JudgingConflictModel.php';
    $conflictModel = new JudgingConflictModel();
    echo "<p style='color: green;'>✓ JudgingConflictModel loaded</p>\n";
    
    echo "<p>Testing getConflicts method...</p>\n";
    $conflicts = $conflictModel->getConflicts();
    echo "<p style='color: green;'>✓ getConflicts() works - found " . count($conflicts) . " conflicts</p>\n";
    
    echo "<p>Testing getConflictStatistics method...</p>\n";
    $stats = $conflictModel->getConflictStatistics();
    echo "<p style='color: green;'>✓ getConflictStatistics() works</p>\n";
    
    echo "<hr>\n";
    echo "<h2>✅ SUCCESS!</h2>\n";
    echo "<p style='color: green; font-weight: bold;'>The Judging Conflicts System is working properly!</p>\n";
    
    echo "<h3>Ready to use:</h3>\n";
    echo "<ul>\n";
    echo "<li><a href='" . BASE_URL . "/judging_conflict/dashboard'>Admin Dashboard</a></li>\n";
    echo "<li><a href='" . BASE_URL . "/judging_conflict/report'>Report a Conflict</a></li>\n";
    echo "<li><a href='" . BASE_URL . "/judging_conflict/my_reports'>My Reports</a></li>\n";
    echo "</ul>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>\n";
}

echo "<hr>\n";
echo "<p><a href='" . BASE_URL . "'>← Return to Home</a></p>\n";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 600px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}

h1, h2 {
    color: #333;
}

p {
    margin: 5px 0;
}

ul {
    padding-left: 20px;
}

a {
    color: #007bff;
    text-decoration: none;
}

a:hover {
    text-decoration: underline;
}

pre {
    background: #f5f5f5;
    padding: 10px;
    border-radius: 4px;
    overflow-x: auto;
}
</style>