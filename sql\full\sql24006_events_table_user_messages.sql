
-- --------------------------------------------------------

--
-- Table structure for table `user_messages`
--
-- Creation: Jul 12, 2025 at 06:22 PM
--

CREATE TABLE `user_messages` (
  `id` int(11) NOT NULL,
  `from_user_id` int(10) UNSIGNED NOT NULL,
  `to_user_id` int(10) UNSIGNED NOT NULL,
  `subject` varchar(255) NOT NULL,
  `message` text NOT NULL,
  `show_id` int(11) DEFAULT NULL COMMENT 'Related show if applicable',
  `parent_message_id` int(11) DEFAULT NULL COMMENT 'For reply threading',
  `requires_reply` tinyint(1) DEFAULT 0 COMMENT 'Whether sender expects a reply',
  `is_read` tinyint(1) DEFAULT 0,
  `is_archived` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `read_at` timestamp NULL DEFAULT NULL,
  `reply_used` tinyint(1) DEFAULT 0 COMMENT 'Whether the recipient has used their reply for this message',
  `allows_reply` tinyint(1) DEFAULT 0 COMMENT 'Whether this message allows replies (contains [reply])'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- RELATIONSHIPS FOR TABLE `user_messages`:
--   `from_user_id`
--       `users` -> `id`
--   `parent_message_id`
--       `user_messages` -> `id`
--   `to_user_id`
--       `users` -> `id`
--
