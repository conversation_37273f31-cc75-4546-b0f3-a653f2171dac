
-- --------------------------------------------------------

--
-- Table structure for table `notification_settings`
--
-- Creation: Jul 13, 2025 at 12:20 AM
--

CREATE TABLE `notification_settings` (
  `id` int(11) NOT NULL,
  `setting_key` varchar(100) NOT NULL,
  `setting_value` text NOT NULL,
  `setting_type` enum('string','integer','boolean','json') DEFAULT 'string',
  `description` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `email_enabled` tinyint(1) DEFAULT 1,
  `sms_enabled` tinyint(1) DEFAULT 0,
  `push_enabled` tinyint(1) DEFAULT 1,
  `toast_enabled` tinyint(1) DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- RELATIONSHIPS FOR TABLE `notification_settings`:
--

--
-- Dumping data for table `notification_settings`
--

INSERT INTO `notification_settings` (`id`, `setting_key`, `setting_value`, `setting_type`, `description`, `created_at`, `updated_at`, `email_enabled`, `sms_enabled`, `push_enabled`, `toast_enabled`) VALUES
(1, 'email_enabled', '1', 'boolean', 'Enable email notifications globally', '2025-06-22 01:24:12', '2025-07-15 19:40:42', 1, 0, 1, 1),
(2, 'sms_enabled', '0', 'boolean', 'Enable SMS notifications globally', '2025-06-22 01:24:12', '2025-07-15 19:40:42', 1, 0, 1, 1),
(3, 'push_enabled', '1', 'boolean', 'Enable push notifications globally', '2025-06-22 01:24:12', '2025-07-15 19:40:42', 1, 0, 1, 1),
(4, 'toast_enabled', '1', 'boolean', 'Enable toast notifications globally', '2025-06-22 01:24:12', '2025-07-15 19:40:42', 1, 0, 1, 1),
(5, 'default_notification_times', '[1440, 60, 15]', 'json', 'Default notification times in minutes before event (24h, 1h, 15min)', '2025-06-22 01:24:12', '2025-06-22 01:24:12', 1, 0, 1, 1),
(6, 'max_notification_attempts', '3', 'integer', 'Maximum number of attempts to send a notification', '2025-06-22 01:24:12', '2025-07-15 19:40:42', 1, 0, 1, 1),
(7, 'notification_retry_interval', '30', 'integer', 'Minutes to wait before retrying failed notifications', '2025-06-22 01:24:12', '2025-07-15 19:40:42', 1, 0, 1, 1),
(8, 'email_from_address', '<EMAIL>', 'string', 'Default from email address for notifications', '2025-06-22 01:24:12', '2025-07-15 19:40:42', 1, 0, 1, 1),
(9, 'email_from_name', 'Rowan Elite Rides Events', 'string', 'Default from name for email notifications', '2025-06-22 01:24:12', '2025-07-15 19:40:42', 1, 0, 1, 1);
