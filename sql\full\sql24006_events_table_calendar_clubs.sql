
-- --------------------------------------------------------

--
-- Table structure for table `calendar_clubs`
--
-- Creation: Jul 10, 2025 at 01:46 PM
--

CREATE TABLE `calendar_clubs` (
  `id` int(10) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `logo` varchar(255) DEFAULT NULL,
  `website` varchar(255) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `phone` varchar(50) DEFAULT NULL,
  `owner_id` int(10) UNSIGNED DEFAULT NULL,
  `is_verified` tinyint(1) NOT NULL DEFAULT 0,
  `verification_status` enum('pending','approved','denied') DEFAULT NULL,
  `verification_requested_at` timestamp NULL DEFAULT NULL,
  `verification_notes` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- RELATIONSHIPS FOR TABLE `calendar_clubs`:
--

--
-- Dumping data for table `calendar_clubs`
--

INSERT INTO `calendar_clubs` (`id`, `name`, `description`, `logo`, `website`, `email`, `phone`, `owner_id`, `is_verified`, `verification_status`, `verification_requested_at`, `verification_notes`, `created_at`, `updated_at`) VALUES
(1, 'Rowan Elite Rides', '', '', 'http://www.rowaneliterides.com', '', '', 3, 1, NULL, NULL, NULL, '2025-06-21 19:53:35', '2025-06-24 00:19:06');
