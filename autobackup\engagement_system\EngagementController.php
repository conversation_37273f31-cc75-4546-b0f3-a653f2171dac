<?php
/**
 * Engagement Controller
 * 
 * Handles photo engagement features: likes, comments, shares, tags, favorites
 */

class EngagementController extends Controller {
    
    private $db;
    private $auth;
    
    public function __construct() {
        $this->db = new Database();
        $this->auth = new Auth();
    }
    
    /**
     * Toggle like on a photo
     */
    public function toggleLike() {
        header('Content-Type: application/json');
        
        if (!$this->auth->isLoggedIn()) {
            http_response_code(401);
            echo json_encode(['error' => 'Login required']);
            return;
        }
        
        $input = json_decode(file_get_contents('php://input'), true);
        $photoId = $input['photo_id'] ?? null;
        $userId = $this->auth->getCurrentUserId();
        
        if (!$photoId) {
            http_response_code(400);
            echo json_encode(['error' => 'Photo ID required']);
            return;
        }
        
        try {
            // Check if already liked
            $this->db->query('SELECT id FROM photo_likes WHERE photo_id = :photo_id AND user_id = :user_id');
            $this->db->bind(':photo_id', $photoId);
            $this->db->bind(':user_id', $userId);
            $existingLike = $this->db->single();
            
            if ($existingLike) {
                // Unlike
                $this->db->query('DELETE FROM photo_likes WHERE photo_id = :photo_id AND user_id = :user_id');
                $this->db->bind(':photo_id', $photoId);
                $this->db->bind(':user_id', $userId);
                $this->db->execute();
                
                $liked = false;
            } else {
                // Like
                $this->db->query('INSERT INTO photo_likes (photo_id, user_id) VALUES (:photo_id, :user_id)');
                $this->db->bind(':photo_id', $photoId);
                $this->db->bind(':user_id', $userId);
                $this->db->execute();
                
                $liked = true;
                
                // Create notification for photo owner
                $this->createEngagementNotification($photoId, 'like', $userId);
            }
            
            // Update engagement stats
            $this->updateEngagementStats($photoId);
            
            // Get updated like count
            $likeCount = $this->getLikeCount($photoId);
            
            echo json_encode([
                'success' => true,
                'liked' => $liked,
                'like_count' => $likeCount
            ]);
            
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => 'Failed to toggle like']);
        }
    }
    
    /**
     * Add comment to photo
     */
    public function addComment() {
        header('Content-Type: application/json');
        
        if (!$this->auth->isLoggedIn()) {
            http_response_code(401);
            echo json_encode(['error' => 'Login required']);
            return;
        }
        
        $input = json_decode(file_get_contents('php://input'), true);
        $photoId = $input['photo_id'] ?? null;
        $comment = trim($input['comment'] ?? '');
        $parentCommentId = $input['parent_comment_id'] ?? null;
        $userId = $this->auth->getCurrentUserId();
        
        if (!$photoId || !$comment) {
            http_response_code(400);
            echo json_encode(['error' => 'Photo ID and comment required']);
            return;
        }
        
        try {
            // Extract mentions (@username)
            $mentions = $this->extractMentions($comment);
            
            // Insert comment
            $this->db->query('INSERT INTO photo_comments (photo_id, user_id, comment, parent_comment_id, mentions) 
                             VALUES (:photo_id, :user_id, :comment, :parent_comment_id, :mentions)');
            $this->db->bind(':photo_id', $photoId);
            $this->db->bind(':user_id', $userId);
            $this->db->bind(':comment', $comment);
            $this->db->bind(':parent_comment_id', $parentCommentId);
            $this->db->bind(':mentions', json_encode($mentions));
            $this->db->execute();
            
            $commentId = $this->db->lastInsertId();
            
            // Create notifications
            if ($parentCommentId) {
                $this->createEngagementNotification($photoId, 'reply', $userId, $parentCommentId);
            } else {
                $this->createEngagementNotification($photoId, 'comment', $userId);
            }
            
            // Create mention notifications
            foreach ($mentions as $mentionedUserId) {
                $this->createEngagementNotification($photoId, 'mention', $userId, null, $mentionedUserId);
            }
            
            // Update engagement stats
            $this->updateEngagementStats($photoId);
            
            // Get comment with user info
            $comment = $this->getCommentById($commentId);
            
            echo json_encode([
                'success' => true,
                'comment' => $comment
            ]);
            
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => 'Failed to add comment']);
        }
    }
    
    /**
     * Get comments for a photo
     */
    public function getComments($photoId) {
        header('Content-Type: application/json');
        
        try {
            $this->db->query('SELECT c.*, u.name as user_name, u.profile_image 
                             FROM photo_comments c 
                             JOIN users u ON c.user_id = u.id 
                             WHERE c.photo_id = :photo_id AND c.is_approved = 1 
                             ORDER BY c.created_at ASC');
            $this->db->bind(':photo_id', $photoId);
            $comments = $this->db->resultSet();
            
            // Organize comments into threads
            $commentTree = $this->organizeCommentTree($comments);
            
            echo json_encode([
                'success' => true,
                'comments' => $commentTree
            ]);
            
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => 'Failed to load comments']);
        }
    }
    
    /**
     * Toggle favorite on a photo
     */
    public function toggleFavorite() {
        header('Content-Type: application/json');
        
        if (!$this->auth->isLoggedIn()) {
            http_response_code(401);
            echo json_encode(['error' => 'Login required']);
            return;
        }
        
        $input = json_decode(file_get_contents('php://input'), true);
        $photoId = $input['photo_id'] ?? null;
        $collection = $input['collection'] ?? 'My Favorites';
        $userId = $this->auth->getCurrentUserId();
        
        if (!$photoId) {
            http_response_code(400);
            echo json_encode(['error' => 'Photo ID required']);
            return;
        }
        
        try {
            // Check if already favorited
            $this->db->query('SELECT id FROM photo_favorites WHERE photo_id = :photo_id AND user_id = :user_id');
            $this->db->bind(':photo_id', $photoId);
            $this->db->bind(':user_id', $userId);
            $existingFavorite = $this->db->single();
            
            if ($existingFavorite) {
                // Remove from favorites
                $this->db->query('DELETE FROM photo_favorites WHERE photo_id = :photo_id AND user_id = :user_id');
                $this->db->bind(':photo_id', $photoId);
                $this->db->bind(':user_id', $userId);
                $this->db->execute();
                
                $favorited = false;
            } else {
                // Add to favorites
                $this->db->query('INSERT INTO photo_favorites (photo_id, user_id, collection_name) 
                                 VALUES (:photo_id, :user_id, :collection)');
                $this->db->bind(':photo_id', $photoId);
                $this->db->bind(':user_id', $userId);
                $this->db->bind(':collection', $collection);
                $this->db->execute();
                
                $favorited = true;
            }
            
            // Update engagement stats
            $this->updateEngagementStats($photoId);
            
            echo json_encode([
                'success' => true,
                'favorited' => $favorited
            ]);
            
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => 'Failed to toggle favorite']);
        }
    }
    
    /**
     * Track photo share
     */
    public function trackShare() {
        header('Content-Type: application/json');
        
        $input = json_decode(file_get_contents('php://input'), true);
        $photoId = $input['photo_id'] ?? null;
        $platform = $input['platform'] ?? null;
        $userId = $this->auth->isLoggedIn() ? $this->auth->getCurrentUserId() : null;
        
        if (!$photoId || !$platform) {
            http_response_code(400);
            echo json_encode(['error' => 'Photo ID and platform required']);
            return;
        }
        
        try {
            // Track the share
            $this->db->query('INSERT INTO photo_shares (photo_id, user_id, platform) 
                             VALUES (:photo_id, :user_id, :platform)');
            $this->db->bind(':photo_id', $photoId);
            $this->db->bind(':user_id', $userId);
            $this->db->bind(':platform', $platform);
            $this->db->execute();
            
            // Update engagement stats
            $this->updateEngagementStats($photoId);
            
            echo json_encode(['success' => true]);
            
        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => 'Failed to track share']);
        }
    }
    
    /**
     * Helper Methods
     */

    private function getLikeCount($photoId) {
        $this->db->query('SELECT COUNT(*) as count FROM photo_likes WHERE photo_id = :photo_id');
        $this->db->bind(':photo_id', $photoId);
        $result = $this->db->single();
        return $result->count ?? 0;
    }

    private function extractMentions($comment) {
        preg_match_all('/@(\w+)/', $comment, $matches);
        $mentions = [];

        if (!empty($matches[1])) {
            foreach ($matches[1] as $username) {
                $this->db->query('SELECT id FROM users WHERE name = :username OR email = :username');
                $this->db->bind(':username', $username);
                $user = $this->db->single();

                if ($user) {
                    $mentions[] = $user->id;
                }
            }
        }

        return array_unique($mentions);
    }

    private function createEngagementNotification($photoId, $type, $fromUserId, $commentId = null, $targetUserId = null) {
        // Get photo owner if no specific target
        if (!$targetUserId) {
            $this->db->query('SELECT user_id FROM images WHERE id = :photo_id');
            $this->db->bind(':photo_id', $photoId);
            $photo = $this->db->single();
            $targetUserId = $photo->user_id ?? null;
        }

        // Don't notify yourself
        if ($targetUserId == $fromUserId) {
            return;
        }

        if ($targetUserId) {
            $this->db->query('INSERT INTO engagement_notifications
                             (user_id, photo_id, type, from_user_id)
                             VALUES (:user_id, :photo_id, :type, :from_user_id)');
            $this->db->bind(':user_id', $targetUserId);
            $this->db->bind(':photo_id', $photoId);
            $this->db->bind(':type', $type);
            $this->db->bind(':from_user_id', $fromUserId);
            $this->db->execute();
        }
    }

    private function updateEngagementStats($photoId) {
        // Get current counts
        $this->db->query('SELECT
                         (SELECT COUNT(*) FROM photo_likes WHERE photo_id = :photo_id1) as likes_count,
                         (SELECT COUNT(*) FROM photo_comments WHERE photo_id = :photo_id2 AND is_approved = 1) as comments_count,
                         (SELECT COUNT(*) FROM photo_shares WHERE photo_id = :photo_id3) as shares_count,
                         (SELECT COUNT(*) FROM photo_favorites WHERE photo_id = :photo_id4) as favorites_count,
                         (SELECT COUNT(*) FROM photo_user_tags WHERE photo_id = :photo_id5 AND status = "approved") as tags_count');

        $this->db->bind(':photo_id1', $photoId);
        $this->db->bind(':photo_id2', $photoId);
        $this->db->bind(':photo_id3', $photoId);
        $this->db->bind(':photo_id4', $photoId);
        $this->db->bind(':photo_id5', $photoId);
        $stats = $this->db->single();

        // Update or insert stats
        $this->db->query('INSERT INTO photo_engagement_stats
                         (photo_id, likes_count, comments_count, shares_count, favorites_count, tags_count)
                         VALUES (:photo_id, :likes, :comments, :shares, :favorites, :tags)
                         ON DUPLICATE KEY UPDATE
                         likes_count = :likes2, comments_count = :comments2, shares_count = :shares2,
                         favorites_count = :favorites2, tags_count = :tags2');

        $this->db->bind(':photo_id', $photoId);
        $this->db->bind(':likes', $stats->likes_count);
        $this->db->bind(':comments', $stats->comments_count);
        $this->db->bind(':shares', $stats->shares_count);
        $this->db->bind(':favorites', $stats->favorites_count);
        $this->db->bind(':tags', $stats->tags_count);
        $this->db->bind(':likes2', $stats->likes_count);
        $this->db->bind(':comments2', $stats->comments_count);
        $this->db->bind(':shares2', $stats->shares_count);
        $this->db->bind(':favorites2', $stats->favorites_count);
        $this->db->bind(':tags2', $stats->tags_count);
        $this->db->execute();
    }

    private function getCommentById($commentId) {
        $this->db->query('SELECT c.*, u.name as user_name, u.profile_image
                         FROM photo_comments c
                         JOIN users u ON c.user_id = u.id
                         WHERE c.id = :comment_id');
        $this->db->bind(':comment_id', $commentId);
        return $this->db->single();
    }

    private function organizeCommentTree($comments) {
        $tree = [];
        $lookup = [];

        // First pass: create lookup and add top-level comments
        foreach ($comments as $comment) {
            $lookup[$comment->id] = $comment;
            $comment->replies = [];

            if (!$comment->parent_comment_id) {
                $tree[] = $comment;
            }
        }

        // Second pass: add replies to their parents
        foreach ($comments as $comment) {
            if ($comment->parent_comment_id && isset($lookup[$comment->parent_comment_id])) {
                $lookup[$comment->parent_comment_id]->replies[] = $comment;
            }
        }

        return $tree;
    }

    /**
     * Get engagement data for a photo
     */
    public function getEngagementData($photoId) {
        header('Content-Type: application/json');

        try {
            // Get engagement stats
            $this->db->query('SELECT * FROM photo_engagement_stats WHERE photo_id = :photo_id');
            $this->db->bind(':photo_id', $photoId);
            $stats = $this->db->single();

            // Get user's engagement status if logged in
            $userEngagement = [
                'liked' => false,
                'favorited' => false
            ];

            if ($this->auth->isLoggedIn()) {
                $userId = $this->auth->getCurrentUserId();

                // Check if user liked
                $this->db->query('SELECT id FROM photo_likes WHERE photo_id = :photo_id AND user_id = :user_id');
                $this->db->bind(':photo_id', $photoId);
                $this->db->bind(':user_id', $userId);
                $userEngagement['liked'] = $this->db->single() ? true : false;

                // Check if user favorited
                $this->db->query('SELECT id FROM photo_favorites WHERE photo_id = :photo_id AND user_id = :user_id');
                $this->db->bind(':photo_id', $photoId);
                $this->db->bind(':user_id', $userId);
                $userEngagement['favorited'] = $this->db->single() ? true : false;
            }

            echo json_encode([
                'success' => true,
                'stats' => $stats ?: (object)[
                    'likes_count' => 0,
                    'comments_count' => 0,
                    'shares_count' => 0,
                    'favorites_count' => 0,
                    'tags_count' => 0
                ],
                'user_engagement' => $userEngagement
            ]);

        } catch (Exception $e) {
            http_response_code(500);
            echo json_encode(['error' => 'Failed to load engagement data']);
        }
    }
}
