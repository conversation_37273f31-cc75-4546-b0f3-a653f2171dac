<?php
/**
 * Image Editor Model
 * 
 * This model handles all image editing related functionality.
 */
class ImageEditorModel {
    private $db;
    private $settingsModel;
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->db = new Database();
        
        // We'll load the settings model when needed instead of in the constructor
        
        // Ensure the images table has the user_id column
        $this->ensureUserIdColumn();
    }
    
    /**
     * Get settings model
     * 
     * @return object SettingsModel instance
     */
    private function getSettingsModel() {
        if (!$this->settingsModel) {
            // Check if SettingsModel.php exists
            $settingsModelPath = APPROOT . '/models/SettingsModel.php';
            if (file_exists($settingsModelPath)) {
                require_once $settingsModelPath;
                $this->settingsModel = new SettingsModel();
            } else {
                // Fallback to default settings if model can't be loaded
                $this->settingsModel = (object)[
                    'getSetting' => function($key, $default) {
                        return $default;
                    }
                ];
                error_log('SettingsModel.php not found. Using default settings.');
            }
        }
        return $this->settingsModel;
    }
    
    /**
     * Ensure the images table has the user_id column
     * This is a temporary method to handle database migration
     */
    private function ensureUserIdColumn() {
        try {
            // Check if user_id column exists
            $this->db->query("SHOW COLUMNS FROM images LIKE 'user_id'");
            $columnExists = $this->db->rowCount() > 0;
            
            if (!$columnExists) {
                // Add user_id column
                $this->db->query("ALTER TABLE images ADD COLUMN user_id INT UNSIGNED NOT NULL AFTER id");
                
                // Add index for faster queries
                $this->db->query("ALTER TABLE images ADD INDEX idx_user_id (user_id)");
            }
            
            // Check if thumbnail_path column exists
            $this->db->query("SHOW COLUMNS FROM images LIKE 'thumbnail_path'");
            $thumbnailColumnExists = $this->db->rowCount() > 0;
            
            if (!$thumbnailColumnExists) {
                // Add thumbnail_path column
                $this->db->query("ALTER TABLE images ADD COLUMN thumbnail_path VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL AFTER file_path");
            }
            
            // Check if optimized column exists
            $this->db->query("SHOW COLUMNS FROM images LIKE 'optimized'");
            $optimizedColumnExists = $this->db->rowCount() > 0;
            
            if (!$optimizedColumnExists) {
                // Add optimized column
                $this->db->query("ALTER TABLE images ADD COLUMN optimized TINYINT(1) NOT NULL DEFAULT 0 AFTER is_primary");
            }
        } catch (Exception $e) {
            // Log error but continue
            error_log('Error ensuring user_id column: ' . $e->getMessage());
        }
    }
    
    /**
     * Get image by ID
     * 
     * @param int $id Image ID
     * @return object Image
     */
    public function getImageById($id) {
        $this->db->query('SELECT * FROM images WHERE id = :id');
        $this->db->bind(':id', $id);
        return $this->db->single();
    }
    
    /**
     * Get images by entity
     * 
     * @param string $entityType Entity type (vehicle, show, etc.)
     * @param int $entityId Entity ID
     * @return array Images
     */
    public function getImagesByEntity($entityType, $entityId) {
        $this->db->query('SELECT * FROM images WHERE entity_type = :entity_type AND entity_id = :entity_id ORDER BY is_primary DESC, created_at DESC');
        $this->db->bind(':entity_type', $entityType);
        $this->db->bind(':entity_id', $entityId);
        return $this->db->resultSet();
    }
    
    /**
     * Get show images
     * 
     * @param int $showId Show ID
     * @return array Show images
     */
    public function getShowImages($showId) {
        return $this->getImagesByEntity('show', $showId);
    }
    
    /**
     * Get images with pagination
     * 
     * @param int $page Page number
     * @param int $itemsPerPage Items per page
     * @param string $search Search term
     * @param string $type Entity type filter
     * @param int|null $userId Filter by user ID (null for all users, admin only)
     * @param string|null $entityType Entity type for permission filtering (show, vehicle)
     * @param int|null $entityId Entity ID for permission filtering
     * @return array Images and pagination data
     */
    public function getImages($page = 1, $itemsPerPage = 12, $search = '', $type = '', $userId = null, $entityType = null, $entityId = null) {
        // Calculate offset
        $offset = ($page - 1) * $itemsPerPage;
        
        // Build query
        $sql = 'SELECT * FROM images WHERE 1=1';
        $params = [];
        
        // Add filters based on provided parameters
        // Case 1: We have both user ID and entity filters
        if ($userId !== null && $entityType !== null && $entityId !== null) {
            // Show images that either belong to the user OR match the entity type/id
            $sql .= ' AND (user_id = :user_id OR (entity_type = :entity_type AND entity_id = :entity_id))';
            $params[':user_id'] = $userId;
            $params[':entity_type'] = $entityType;
            $params[':entity_id'] = $entityId;
        }
        // Case 2: We only have user ID filter
        else if ($userId !== null) {
            $sql .= ' AND user_id = :user_id';
            $params[':user_id'] = $userId;
        }
        // Case 3: We only have entity filter
        else if ($entityType !== null && $entityId !== null) {
            $sql .= ' AND entity_type = :entity_type AND entity_id = :entity_id';
            $params[':entity_type'] = $entityType;
            $params[':entity_id'] = $entityId;
        }
        
        // Add search condition
        if (!empty($search)) {
            $sql .= ' AND (file_name LIKE :search OR entity_type LIKE :search)';
            $params[':search'] = '%' . $search . '%';
        }
        
        // Add type condition
        if (!empty($type)) {
            $sql .= ' AND entity_type = :type';
            $params[':type'] = $type;
        }
        
        // Count total items
        $countSql = str_replace('SELECT *', 'SELECT COUNT(*)', $sql);
        $this->db->query($countSql);
        
        // Bind parameters for count query
        foreach ($params as $param => $value) {
            $this->db->bind($param, $value);
        }
        
        $totalItems = $this->db->single()->{'COUNT(*)'};
        $totalPages = ceil($totalItems / $itemsPerPage);
        
        // Add order and limit
        $sql .= ' ORDER BY created_at DESC LIMIT :offset, :limit';
        
        // Execute main query
        $this->db->query($sql);
        
        // Bind parameters
        foreach ($params as $param => $value) {
            $this->db->bind($param, $value);
        }
        
        $this->db->bind(':offset', $offset);
        $this->db->bind(':limit', $itemsPerPage);
        
        // Store debug info in session (only in development)
        if (defined('ENVIRONMENT') && ENVIRONMENT === 'development') {
            $_SESSION['debug_sql'] = [
                'query' => $sql,
                'params' => $params,
                'user_id' => $userId,
                'entity_type' => $entityType,
                'entity_id' => $entityId,
                'total_items' => $totalItems
            ];
        }
        
        return [
            'images' => $this->db->resultSet(),
            'total_items' => $totalItems,
            'total_pages' => $totalPages
        ];
    }
    
    /**
     * Get recent images
     * 
     * @param int $limit Number of images to return
     * @param int|null $userId Filter by user ID (null for all users, admin only)
     * @return array Recent images
     */
    public function getRecentImages($limit = 8, $userId = null) {
        if ($userId !== null) {
            $this->db->query('SELECT * FROM images WHERE user_id = :user_id ORDER BY created_at DESC LIMIT :limit');
            $this->db->bind(':user_id', $userId);
        } else {
            $this->db->query('SELECT * FROM images ORDER BY created_at DESC LIMIT :limit');
        }
        $this->db->bind(':limit', $limit);
        return $this->db->resultSet();
    }
    
    /**
     * Get all images with optional limit
     * 
     * @param int $limit Optional limit on number of images to return
     * @param int|null $userId Filter by user ID (null for all users, admin only)
     * @return array All images
     */
    public function getAllImages($limit = null, $userId = null) {
        $sql = 'SELECT * FROM images';
        
        if ($userId !== null) {
            $sql .= ' WHERE user_id = :user_id';
        }
        
        $sql .= ' ORDER BY created_at DESC';
        
        if ($limit) {
            $sql .= ' LIMIT :limit';
        }
        
        $this->db->query($sql);
        
        if ($userId !== null) {
            $this->db->bind(':user_id', $userId);
        }
        
        if ($limit) {
            $this->db->bind(':limit', $limit);
        }
        
        return $this->db->resultSet();
    }
    
    /**
     * Get images by entity type
     * 
     * @param string $entityType Entity type (vehicle, show, etc.)
     * @param int $entityId Optional entity ID
     * @return array Images
     */
    public function getImagesByEntityType($entityType, $entityId = null) {
        if ($entityId) {
            return $this->getImagesByEntity($entityType, $entityId);
        } else {
            $this->db->query('SELECT * FROM images WHERE entity_type = :entity_type ORDER BY created_at DESC');
            $this->db->bind(':entity_type', $entityType);
            return $this->db->resultSet();
        }
    }
    
    /**
     * Add image
     * 
     * @param array $data Image data
     * @return int|bool Image ID or false
     */
    public function addImage($data) {
        $this->db->query('INSERT INTO images (user_id, entity_type, entity_id, file_name, file_path, thumbnail_path, file_size, file_type, width, height, is_primary, optimized, created_at, updated_at) 
                          VALUES (:user_id, :entity_type, :entity_id, :file_name, :file_path, :thumbnail_path, :file_size, :file_type, :width, :height, :is_primary, :optimized, NOW(), NOW())');
        
        $this->db->bind(':user_id', $data['user_id']);
        $this->db->bind(':entity_type', $data['entity_type']);
        $this->db->bind(':entity_id', $data['entity_id']);
        $this->db->bind(':file_name', $data['file_name']);
        $this->db->bind(':file_path', $data['file_path']);
        $this->db->bind(':thumbnail_path', $data['thumbnail_path'] ?? null);
        $this->db->bind(':file_size', $data['file_size']);
        $this->db->bind(':file_type', $data['file_type']);
        $this->db->bind(':width', $data['width']);
        $this->db->bind(':height', $data['height']);
        $this->db->bind(':is_primary', $data['is_primary']);
        $this->db->bind(':optimized', $data['optimized'] ?? 0);
        
        if ($this->db->execute()) {
            $imageId = $this->db->lastInsertId();
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log('ImageEditorModel::addImage - Successfully inserted image with ID: ' . $imageId);
            }
            return $imageId;
        } else {
            if (defined('DEBUG_MODE') && DEBUG_MODE) {
                error_log('ImageEditorModel::addImage - Failed to execute INSERT query');
                error_log('ImageEditorModel::addImage - Data: ' . print_r($data, true));
            }
            return false;
        }
    }
    
    /**
     * Update image
     * 
     * @param int $id Image ID
     * @param array $data Image data
     * @return bool Success or failure
     */
    public function updateImage($id, $data) {
        $this->db->query('UPDATE images 
                          SET file_name = :file_name, file_path = :file_path, thumbnail_path = :thumbnail_path, 
                              file_size = :file_size, file_type = :file_type, width = :width, height = :height, 
                              is_primary = :is_primary, optimized = :optimized, updated_at = NOW() 
                          WHERE id = :id');
        
        $this->db->bind(':id', $id);
        $this->db->bind(':file_name', $data['file_name']);
        $this->db->bind(':file_path', $data['file_path']);
        $this->db->bind(':thumbnail_path', $data['thumbnail_path'] ?? null);
        $this->db->bind(':file_size', $data['file_size']);
        $this->db->bind(':file_type', $data['file_type']);
        $this->db->bind(':width', $data['width']);
        $this->db->bind(':height', $data['height']);
        $this->db->bind(':is_primary', $data['is_primary']);
        $this->db->bind(':optimized', $data['optimized'] ?? 0);
        
        return $this->db->execute();
    }
    
    /**
     * Delete image
     * 
     * @param int $id Image ID
     * @return bool Success or failure
     */
    public function deleteImage($id) {
        // Get image first to get the file path
        $image = $this->getImageById($id);
        
        if (!$image) {
            error_log('ImageEditorModel::deleteImage - Image not found with ID: ' . $id);
            return false;
        }
        
        error_log('ImageEditorModel::deleteImage - Deleting image: ' . print_r($image, true));
        
        // Delete the original file
        error_log('ImageEditorModel::deleteImage - Checking original file: ' . $image->file_path);
        if (file_exists($image->file_path)) {
            error_log('ImageEditorModel::deleteImage - Deleting original file: ' . $image->file_path);
            unlink($image->file_path);
        } else {
            error_log('ImageEditorModel::deleteImage - Original file not found: ' . $image->file_path);
        }
        
        // Delete the thumbnail if it exists
        if (!empty($image->thumbnail_path)) {
            error_log('ImageEditorModel::deleteImage - Checking thumbnail: ' . $image->thumbnail_path);
            if (file_exists($image->thumbnail_path)) {
                error_log('ImageEditorModel::deleteImage - Deleting thumbnail: ' . $image->thumbnail_path);
                unlink($image->thumbnail_path);
            } else {
                error_log('ImageEditorModel::deleteImage - Thumbnail not found: ' . $image->thumbnail_path);
            }
        } else {
            error_log('ImageEditorModel::deleteImage - No thumbnail path specified');
        }
        
        // Delete from database
        $this->db->query('DELETE FROM images WHERE id = :id');
        $this->db->bind(':id', $id);
        $result = $this->db->execute();
        
        if ($result) {
            error_log('ImageEditorModel::deleteImage - Successfully deleted image from database: ' . $id);
        } else {
            error_log('ImageEditorModel::deleteImage - Failed to delete image from database: ' . $id);
        }
        
        return $result;
    }
    
    /**
     * Set primary image
     * 
     * @param int $id Image ID
     * @return bool Success or failure
     */
    public function setPrimaryImage($id) {
        // Get image first
        $image = $this->getImageById($id);
        
        if (!$image) {
            return false;
        }
        
        // Start transaction
        $this->db->beginTransaction();
        
        // Reset all images for this entity
        $this->db->query('UPDATE images SET is_primary = 0 WHERE entity_type = :entity_type AND entity_id = :entity_id');
        $this->db->bind(':entity_type', $image->entity_type);
        $this->db->bind(':entity_id', $image->entity_id);
        $this->db->execute();
        
        // Set this image as primary
        $this->db->query('UPDATE images SET is_primary = 1 WHERE id = :id');
        $this->db->bind(':id', $id);
        $result = $this->db->execute();
        
        // Commit or rollback
        if ($result) {
            $this->db->commit();
            return true;
        } else {
            $this->db->rollBack();
            return false;
        }
    }
    
    /**
     * Process image upload
     * 
     * @param array $file File data from $_FILES
     * @param string $entityType Entity type
     * @param int $entityId Entity ID
     * @param string $uploadDir Upload directory
     * @param int $userId User ID who is uploading the image
     * @param bool $isPrimary Whether this image should be set as primary
     * @return array|bool Image data or false
     */
    public function processImageUpload($file, $entityType, $entityId, $uploadDir = 'uploads/images/', $userId = null, $isPrimary = false) {
        // Check if DEBUG_MODE is enabled
        $debugMode = defined('DEBUG_MODE') && DEBUG_MODE === true;
        
        if ($debugMode) {
            error_log('ImageEditorModel::processImageUpload - Starting with upload dir: ' . $uploadDir);
        }
        
        // Check if file was uploaded without errors
        if ($file['error'] !== UPLOAD_ERR_OK) {
            error_log('ImageEditorModel::processImageUpload - File upload error: ' . $file['error']);
            return false;
        }
        
        // Get current user ID if not provided
        if ($userId === null && isset($_SESSION['user_id'])) {
            $userId = $_SESSION['user_id'];
        }
        
        // Get settings model
        $settingsModel = $this->getSettingsModel();
        
        // Get image settings
        $imageQuality = (int)$settingsModel->getSetting('image_image_quality', 80);
        $thumbnailSize = (int)$settingsModel->getSetting('image_thumbnail_size', 200);
        $maxUploadSize = (int)$settingsModel->getSetting('image_max_upload_size', 5) * 1024 * 1024; // Convert MB to bytes
        $optimizeImages = $settingsModel->getSetting('image_optimize_images', '0') === '1';
        $resizeLargeImages = $settingsModel->getSetting('image_resize_large_images', '0') === '1';
        $maxWidth = (int)$settingsModel->getSetting('image_max_width', 1920);
        $maxHeight = (int)$settingsModel->getSetting('image_max_height', 1080);
        $allowedExtensionsStr = $settingsModel->getSetting('image_allowed_extensions', 'jpg,jpeg,png,gif,webp');
        $allowedExtensions = explode(',', $allowedExtensionsStr);

        // Apply event-specific optimizations for better web performance
        if ($entityType === 'event') {
            $optimizeImages = true; // Always optimize event images
            $resizeLargeImages = true; // Always resize large event images
            $imageQuality = 75; // Use slightly lower quality for better compression
            $maxWidth = 800; // Smaller max width for event images (they're displayed smaller)
            $maxHeight = 600; // Smaller max height for event images

            if ($debugMode) {
                error_log('ImageEditorModel::processImageUpload - Applying event-specific optimizations: quality=' . $imageQuality . ', maxWidth=' . $maxWidth . ', maxHeight=' . $maxHeight);
            }
        }
        
        // Get file info
        $fileName = $file['name'];
        $fileTmpName = $file['tmp_name'];
        $fileSize = $file['size'];
        $fileType = $file['type'];
        
        // Get file extension
        $fileExt = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
        
        // Check if file is an image with allowed extension
        if (!in_array($fileExt, $allowedExtensions)) {
            error_log('ImageEditorModel::processImageUpload - Invalid file extension: ' . $fileExt);
            return false;
        }
        
        // Check file size against settings
        if ($fileSize > $maxUploadSize) {
            error_log('ImageEditorModel::processImageUpload - File too large: ' . $fileSize . ' bytes (max: ' . $maxUploadSize . ' bytes)');
            return false;
        }
        
        // Generate unique file name
        $newFileName = uniqid($entityType . '_' . $entityId . '_') . '.' . $fileExt;
        
        // Ensure upload directory has a trailing slash
        if (substr($uploadDir, -1) !== '/') {
            $uploadDir .= '/';
        }
        
        // Make sure upload directory is absolute
        if (strpos($uploadDir, '/') !== 0 && strpos($uploadDir, ':') !== 1) {
            // If it's a relative path, make it absolute
            $uploadDir = APPROOT . '/' . $uploadDir;
            
            if ($debugMode) {
                error_log('ImageEditorModel::processImageUpload - Converted to absolute path: ' . $uploadDir);
            }
        }
        
        // Create upload directory if it doesn't exist
        if (!file_exists($uploadDir)) {
            if (!mkdir($uploadDir, 0777, true)) {
                error_log('ImageEditorModel::processImageUpload - Failed to create upload directory: ' . $uploadDir);
                return false;
            }
            
            if ($debugMode) {
                error_log('ImageEditorModel::processImageUpload - Created upload directory: ' . $uploadDir);
            }
        }
        
        // Create thumbnails directory if it doesn't exist
        $thumbnailDir = $uploadDir . 'thumbnails/';
        if (!file_exists($thumbnailDir)) {
            if (!mkdir($thumbnailDir, 0777, true)) {
                error_log('ImageEditorModel::processImageUpload - Failed to create thumbnail directory: ' . $thumbnailDir);
                return false;
            }
            
            if ($debugMode) {
                error_log('ImageEditorModel::processImageUpload - Created thumbnail directory: ' . $thumbnailDir);
            }
        }
        
        $uploadPath = $uploadDir . $newFileName;
        $thumbnailPath = $thumbnailDir . $newFileName;
        
        if ($debugMode) {
            error_log('ImageEditorModel::processImageUpload - Upload path: ' . $uploadPath);
            error_log('ImageEditorModel::processImageUpload - Thumbnail path: ' . $thumbnailPath);
        }
        
        // Upload file - use different methods depending on whether it's a real upload or a local file
        $uploadSuccess = false;
        
        // Check if the file is a real uploaded file
        if (is_uploaded_file($fileTmpName)) {
            // This is a real uploaded file, use move_uploaded_file
            $uploadSuccess = move_uploaded_file($fileTmpName, $uploadPath);
            
            if ($debugMode) {
                error_log('ImageEditorModel::processImageUpload - Using move_uploaded_file for real upload');
            }
        } else {
            // This is a local file (e.g., from Facebook download), use copy or rename
            if (copy($fileTmpName, $uploadPath)) {
                $uploadSuccess = true;
                
                if ($debugMode) {
                    error_log('ImageEditorModel::processImageUpload - Using copy for local file');
                }
            } else {
                // Try rename as a fallback
                $uploadSuccess = rename($fileTmpName, $uploadPath);
                
                if ($debugMode) {
                    error_log('ImageEditorModel::processImageUpload - Using rename for local file: ' . ($uploadSuccess ? 'success' : 'failed'));
                }
            }
        }
        
        if (!$uploadSuccess) {
            error_log('ImageEditorModel::processImageUpload - Failed to move uploaded file from ' . $fileTmpName . ' to ' . $uploadPath);
            return false;
        }
        
        if ($debugMode) {
            error_log('ImageEditorModel::processImageUpload - File moved successfully');
        }
        
        // Get image dimensions
        list($width, $height) = getimagesize($uploadPath);
        
        // Resize large images if enabled in settings
        if ($resizeLargeImages && ($width > $maxWidth || $height > $maxHeight)) {
            $this->resizeImageToMaxDimensions($uploadPath, $maxWidth, $maxHeight);
            
            // Update dimensions after resize
            list($width, $height) = getimagesize($uploadPath);
            
            if ($debugMode) {
                error_log('ImageEditorModel::processImageUpload - Image resized to ' . $width . 'x' . $height);
            }
        }
        
        // Optimize image if enabled in settings
        $optimized = 0;
        if ($optimizeImages) {
            // For events, convert PNG to JPEG for better compression (unless it has transparency)
            $convertPngToJpeg = ($entityType === 'event');
            $this->optimizeImage($uploadPath, $imageQuality, $convertPngToJpeg);
            $optimized = 1;

            // Update file size after optimization
            $fileSize = filesize($uploadPath);

            if ($debugMode) {
                error_log('ImageEditorModel::processImageUpload - Image optimized, new size: ' . $fileSize . ' bytes');
            }
        }
        
        // Generate thumbnail
        $this->createThumbnail($uploadPath, $thumbnailPath, $thumbnailSize);
        
        if ($debugMode) {
            error_log('ImageEditorModel::processImageUpload - Thumbnail created');
        }
        
        // If isPrimary is not explicitly set, check if this is the first image
        if ($isPrimary === false) {
            $isPrimary = count($this->getImagesByEntity($entityType, $entityId)) == 0;
        }
        
        // For database storage, use relative paths
        $relativeUploadPath = str_replace(APPROOT . '/', '', $uploadPath);
        $relativeThumbnailPath = str_replace(APPROOT . '/', '', $thumbnailPath);
        
        if ($debugMode) {
            error_log('ImageEditorModel::processImageUpload - Relative upload path: ' . $relativeUploadPath);
            error_log('ImageEditorModel::processImageUpload - Relative thumbnail path: ' . $relativeThumbnailPath);
        }
        
        // Prepare image data
        $imageData = [
            'user_id' => $userId,
            'entity_type' => $entityType,
            'entity_id' => $entityId,
            'file_name' => $newFileName,
            'file_path' => $relativeUploadPath,
            'thumbnail_path' => $relativeThumbnailPath,
            'file_size' => $fileSize,
            'file_type' => $fileType,
            'width' => $width,
            'height' => $height,
            'is_primary' => $isPrimary ? 1 : 0,
            'optimized' => $optimized
        ];
        
        // Add image to database
        $imageId = $this->addImage($imageData);
        
        if ($imageId) {
            $imageData['id'] = $imageId;
            
            // If this image is set as primary, make sure all other images are not primary
            if ($isPrimary) {
                $this->db->query('UPDATE images SET is_primary = 0 WHERE entity_type = :entity_type AND entity_id = :entity_id AND id != :id');
                $this->db->bind(':entity_type', $entityType);
                $this->db->bind(':entity_id', $entityId);
                $this->db->bind(':id', $imageId);
                $this->db->execute();
                
                if ($debugMode) {
                    error_log('ImageEditorModel::processImageUpload - Set as primary image');
                }
            }
            
            if ($debugMode) {
                error_log('ImageEditorModel::processImageUpload - Image added to database with ID: ' . $imageId);
            }

            // Handle event photo metadata if this is an event photo
            if ($entityType === 'event_photo') {
                // Ensure event context data is available
                if (!isset($_POST['event_photo_event_type'])) {
                    $_POST['event_photo_event_type'] = 'event'; // Default to event
                }
                if (!isset($_POST['event_photo_event_id'])) {
                    $_POST['event_photo_event_id'] = $entityId; // Use the entity ID passed to upload
                }

                $this->saveEventPhotoMetadata($imageId, $_POST);
            }

            return $imageData;
        } else {
            // Delete uploaded files if database insert failed
            if (file_exists($uploadPath)) {
                unlink($uploadPath);
            }
            if (file_exists($thumbnailPath)) {
                unlink($thumbnailPath);
            }
            error_log('ImageEditorModel::processImageUpload - Failed to add image to database');
            return false;
        }
    }

    /**
     * Save event photo metadata
     *
     * @param int $imageId Image ID
     * @param array $postData POST data containing event photo metadata
     * @return bool Success
     */
    private function saveEventPhotoMetadata($imageId, $postData) {
        try {
            // Create event_photo_metadata table if it doesn't exist
            $this->db->query('CREATE TABLE IF NOT EXISTS event_photo_metadata (
                id INT UNSIGNED PRIMARY KEY AUTO_INCREMENT,
                image_id INT UNSIGNED NOT NULL,
                category ENUM("vehicle", "atmosphere", "awards", "vendors", "people") NOT NULL,
                caption TEXT,
                privacy_level ENUM("public", "attendees", "friends", "private") DEFAULT "public",
                latitude DECIMAL(10, 8),
                longitude DECIMAL(11, 8),
                event_type ENUM("event", "show") NOT NULL,
                event_id INT UNSIGNED NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (image_id) REFERENCES images(id) ON DELETE CASCADE,
                INDEX idx_event_photo_image (image_id),
                INDEX idx_event_photo_event (event_type, event_id)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci');
            $this->db->execute();

            // Insert metadata
            $this->db->query('INSERT INTO event_photo_metadata (
                image_id, category, caption, privacy_level, latitude, longitude, event_type, event_id
            ) VALUES (
                :image_id, :category, :caption, :privacy_level, :latitude, :longitude, :event_type, :event_id
            )');

            $this->db->bind(':image_id', $imageId);
            $this->db->bind(':category', $postData['event_photo_category'] ?? 'atmosphere');
            $this->db->bind(':caption', $postData['event_photo_caption'] ?? '');
            $this->db->bind(':privacy_level', $postData['event_photo_privacy'] ?? 'public');
            $this->db->bind(':latitude', $postData['event_photo_latitude'] ?? null);
            $this->db->bind(':longitude', $postData['event_photo_longitude'] ?? null);
            $this->db->bind(':event_type', $postData['event_photo_event_type'] ?? 'event');
            $this->db->bind(':event_id', $postData['event_photo_event_id'] ?? 0);

            return $this->db->execute();

        } catch (Exception $e) {
            error_log('ImageEditorModel::saveEventPhotoMetadata - Error: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Optimize image
     *
     * @param string $imagePath Path to the image
     * @param int $quality Quality (1-100)
     * @param bool $convertPngToJpeg Whether to convert PNG to JPEG for better compression
     * @return bool Success or failure
     */
    public function optimizeImage($imagePath, $quality = 85, $convertPngToJpeg = false) {
        // Get image info
        $imageInfo = getimagesize($imagePath);
        $mime = $imageInfo['mime'];

        // Create image resource based on type
        switch ($mime) {
            case 'image/jpeg':
                $image = imagecreatefromjpeg($imagePath);
                break;
            case 'image/png':
                $image = imagecreatefrompng($imagePath);

                // Check if PNG has transparency
                $hasTransparency = false;
                if (function_exists('imagecolortransparent')) {
                    $transparentIndex = imagecolortransparent($image);
                    $hasTransparency = $transparentIndex >= 0;
                }

                // If converting to JPEG and no transparency, create white background
                if ($convertPngToJpeg && !$hasTransparency) {
                    $width = imagesx($image);
                    $height = imagesy($image);
                    $jpegImage = imagecreatetruecolor($width, $height);

                    // Fill with white background
                    $white = imagecolorallocate($jpegImage, 255, 255, 255);
                    imagefill($jpegImage, 0, 0, $white);

                    // Copy PNG onto white background
                    imagecopy($jpegImage, $image, 0, 0, 0, 0, $width, $height);
                    imagedestroy($image);
                    $image = $jpegImage;
                    $mime = 'image/jpeg'; // Change MIME type for saving
                } else {
                    // Preserve transparency for PNG
                    imagealphablending($image, false);
                    imagesavealpha($image, true);
                }
                break;
            case 'image/gif':
                $image = imagecreatefromgif($imagePath);
                break;
            case 'image/webp':
                $image = imagecreatefromwebp($imagePath);
                break;
            default:
                return false;
        }
        
        // Save the optimized image
        switch ($mime) {
            case 'image/jpeg':
                return imagejpeg($image, $imagePath, $quality);
            case 'image/png':
                // PNG quality is 0-9, convert from 0-100
                $pngQuality = round(9 - (($quality / 100) * 9));
                return imagepng($image, $imagePath, $pngQuality);
            case 'image/gif':
                return imagegif($image, $imagePath);
            case 'image/webp':
                return imagewebp($image, $imagePath, $quality);
            default:
                return false;
        }
    }
    
    /**
     * Create thumbnail from image
     * 
     * @param string $sourcePath Path to the source image
     * @param string $thumbnailPath Path where thumbnail should be saved
     * @param int $maxSize Maximum size of the longest edge
     * @return bool Success or failure
     */
    public function createThumbnail($sourcePath, $thumbnailPath, $maxSize = 200) {
        // Get image info
        $imageInfo = getimagesize($sourcePath);
        $width = $imageInfo[0];
        $height = $imageInfo[1];
        $mime = $imageInfo['mime'];
        
        // Calculate new dimensions while maintaining aspect ratio
        if ($width > $height) {
            $newWidth = $maxSize;
            $newHeight = floor($height * ($maxSize / $width));
        } else {
            $newHeight = $maxSize;
            $newWidth = floor($width * ($maxSize / $height));
        }
        
        // Create image resource based on type
        switch ($mime) {
            case 'image/jpeg':
                $sourceImage = imagecreatefromjpeg($sourcePath);
                break;
            case 'image/png':
                $sourceImage = imagecreatefrompng($sourcePath);
                // Preserve transparency
                imagealphablending($sourceImage, false);
                imagesavealpha($sourceImage, true);
                break;
            case 'image/gif':
                $sourceImage = imagecreatefromgif($sourcePath);
                break;
            case 'image/webp':
                $sourceImage = imagecreatefromwebp($sourcePath);
                break;
            default:
                return false;
        }
        
        // Create thumbnail image
        $thumbnailImage = imagecreatetruecolor($newWidth, $newHeight);
        
        // Preserve transparency for PNG and GIF
        if ($mime == 'image/png' || $mime == 'image/gif') {
            imagealphablending($thumbnailImage, false);
            imagesavealpha($thumbnailImage, true);
            $transparent = imagecolorallocatealpha($thumbnailImage, 255, 255, 255, 127);
            imagefilledrectangle($thumbnailImage, 0, 0, $newWidth, $newHeight, $transparent);
        }
        
        // Resize image
        imagecopyresampled($thumbnailImage, $sourceImage, 0, 0, 0, 0, $newWidth, $newHeight, $width, $height);
        
        // Save thumbnail
        $result = false;
        switch ($mime) {
            case 'image/jpeg':
                $result = imagejpeg($thumbnailImage, $thumbnailPath, 85); // Use 85% quality for thumbnails
                break;
            case 'image/png':
                $result = imagepng($thumbnailImage, $thumbnailPath, 6); // Use compression level 6 for thumbnails
                break;
            case 'image/gif':
                $result = imagegif($thumbnailImage, $thumbnailPath);
                break;
            case 'image/webp':
                $result = imagewebp($thumbnailImage, $thumbnailPath, 85);
                break;
        }
        
        // Free memory
        imagedestroy($sourceImage);
        imagedestroy($thumbnailImage);
        
        return $result;
    }
    
    /**
     * Resize image to fit within maximum dimensions while maintaining aspect ratio
     * 
     * @param string $imagePath Path to the image
     * @param int $maxWidth Maximum width
     * @param int $maxHeight Maximum height
     * @return bool Success or failure
     */
    public function resizeImageToMaxDimensions($imagePath, $maxWidth = 1920, $maxHeight = 1080) {
        // Get image info
        $imageInfo = getimagesize($imagePath);
        $width = $imageInfo[0];
        $height = $imageInfo[1];
        $mime = $imageInfo['mime'];
        
        // If image is already smaller than max dimensions, no need to resize
        if ($width <= $maxWidth && $height <= $maxHeight) {
            return true;
        }
        
        // Calculate new dimensions while maintaining aspect ratio
        $ratio = min($maxWidth / $width, $maxHeight / $height);
        $newWidth = round($width * $ratio);
        $newHeight = round($height * $ratio);
        
        // Create image resource based on type
        switch ($mime) {
            case 'image/jpeg':
                $sourceImage = imagecreatefromjpeg($imagePath);
                break;
            case 'image/png':
                $sourceImage = imagecreatefrompng($imagePath);
                // Preserve transparency
                imagealphablending($sourceImage, false);
                imagesavealpha($sourceImage, true);
                break;
            case 'image/gif':
                $sourceImage = imagecreatefromgif($imagePath);
                break;
            case 'image/webp':
                $sourceImage = imagecreatefromwebp($imagePath);
                break;
            default:
                return false;
        }
        
        // Create resized image
        $resizedImage = imagecreatetruecolor($newWidth, $newHeight);
        
        // Preserve transparency for PNG and GIF
        if ($mime == 'image/png' || $mime == 'image/gif') {
            imagealphablending($resizedImage, false);
            imagesavealpha($resizedImage, true);
            $transparent = imagecolorallocatealpha($resizedImage, 255, 255, 255, 127);
            imagefilledrectangle($resizedImage, 0, 0, $newWidth, $newHeight, $transparent);
        }
        
        // Resize image
        imagecopyresampled($resizedImage, $sourceImage, 0, 0, 0, 0, $newWidth, $newHeight, $width, $height);
        
        // Save resized image
        $result = false;
        switch ($mime) {
            case 'image/jpeg':
                $result = imagejpeg($resizedImage, $imagePath, 90); // Use 90% quality for resized images
                break;
            case 'image/png':
                $result = imagepng($resizedImage, $imagePath, 6); // Use compression level 6
                break;
            case 'image/gif':
                $result = imagegif($resizedImage, $imagePath);
                break;
            case 'image/webp':
                $result = imagewebp($resizedImage, $imagePath, 90);
                break;
        }
        
        // Free memory
        imagedestroy($sourceImage);
        imagedestroy($resizedImage);
        
        return $result;
    }
    
    /**
     * Resize image
     * 
     * @param string $imagePath Path to the image
     * @param int $width New width
     * @param int $height New height
     * @param bool $crop Whether to crop the image
     * @return bool Success or failure
     */
    public function resizeImage($imagePath, $width, $height, $crop = false) {
        // Get image info
        $imageInfo = getimagesize($imagePath);
        $mime = $imageInfo['mime'];
        $srcWidth = $imageInfo[0];
        $srcHeight = $imageInfo[1];
        
        // Create image resource based on type
        switch ($mime) {
            case 'image/jpeg':
                $srcImage = imagecreatefromjpeg($imagePath);
                break;
            case 'image/png':
                $srcImage = imagecreatefrompng($imagePath);
                break;
            case 'image/gif':
                $srcImage = imagecreatefromgif($imagePath);
                break;
            case 'image/webp':
                $srcImage = imagecreatefromwebp($imagePath);
                break;
            default:
                return false;
        }
        
        // Calculate dimensions
        if ($crop) {
            // Calculate aspect ratios
            $srcRatio = $srcWidth / $srcHeight;
            $destRatio = $width / $height;
            
            // Calculate crop dimensions
            if ($srcRatio > $destRatio) {
                // Crop width
                $cropWidth = $srcHeight * $destRatio;
                $cropHeight = $srcHeight;
                $cropX = ($srcWidth - $cropWidth) / 2;
                $cropY = 0;
            } else {
                // Crop height
                $cropWidth = $srcWidth;
                $cropHeight = $srcWidth / $destRatio;
                $cropX = 0;
                $cropY = ($srcHeight - $cropHeight) / 2;
            }
            
            // Create destination image
            $destImage = imagecreatetruecolor($width, $height);
            
            // Preserve transparency for PNG and GIF
            if ($mime == 'image/png' || $mime == 'image/gif') {
                imagealphablending($destImage, false);
                imagesavealpha($destImage, true);
                $transparent = imagecolorallocatealpha($destImage, 255, 255, 255, 127);
                imagefilledrectangle($destImage, 0, 0, $width, $height, $transparent);
            }
            
            // Resize and crop
            imagecopyresampled($destImage, $srcImage, 0, 0, $cropX, $cropY, $width, $height, $cropWidth, $cropHeight);
        } else {
            // Calculate new dimensions while maintaining aspect ratio
            if ($srcWidth / $srcHeight > $width / $height) {
                $newWidth = $width;
                $newHeight = $srcHeight * ($width / $srcWidth);
            } else {
                $newHeight = $height;
                $newWidth = $srcWidth * ($height / $srcHeight);
            }
            
            // Create destination image
            $destImage = imagecreatetruecolor($newWidth, $newHeight);
            
            // Preserve transparency for PNG and GIF
            if ($mime == 'image/png' || $mime == 'image/gif') {
                imagealphablending($destImage, false);
                imagesavealpha($destImage, true);
                $transparent = imagecolorallocatealpha($destImage, 255, 255, 255, 127);
                imagefilledrectangle($destImage, 0, 0, $newWidth, $newHeight, $transparent);
            }
            
            // Resize
            imagecopyresampled($destImage, $srcImage, 0, 0, 0, 0, $newWidth, $newHeight, $srcWidth, $srcHeight);
        }
        
        // Save the resized image
        switch ($mime) {
            case 'image/jpeg':
                return imagejpeg($destImage, $imagePath, 90);
            case 'image/png':
                return imagepng($destImage, $imagePath, 9);
            case 'image/gif':
                return imagegif($destImage, $imagePath);
            case 'image/webp':
                return imagewebp($destImage, $imagePath, 90);
            default:
                return false;
        }
    }
    
    /**
     * Crop image
     * 
     * @param string $imagePath Path to the image
     * @param int $x X coordinate
     * @param int $y Y coordinate
     * @param int $width Width
     * @param int $height Height
     * @return bool Success or failure
     */
    public function cropImage($imagePath, $x, $y, $width, $height) {
        // Get image info
        $imageInfo = getimagesize($imagePath);
        $mime = $imageInfo['mime'];
        
        // Create image resource based on type
        switch ($mime) {
            case 'image/jpeg':
                $srcImage = imagecreatefromjpeg($imagePath);
                break;
            case 'image/png':
                $srcImage = imagecreatefrompng($imagePath);
                break;
            case 'image/gif':
                $srcImage = imagecreatefromgif($imagePath);
                break;
            case 'image/webp':
                $srcImage = imagecreatefromwebp($imagePath);
                break;
            default:
                return false;
        }
        
        // Create destination image
        $destImage = imagecreatetruecolor($width, $height);
        
        // Preserve transparency for PNG and GIF
        if ($mime == 'image/png' || $mime == 'image/gif') {
            imagealphablending($destImage, false);
            imagesavealpha($destImage, true);
            $transparent = imagecolorallocatealpha($destImage, 255, 255, 255, 127);
            imagefilledrectangle($destImage, 0, 0, $width, $height, $transparent);
        }
        
        // Crop
        imagecopy($destImage, $srcImage, 0, 0, $x, $y, $width, $height);
        
        // Save the cropped image
        switch ($mime) {
            case 'image/jpeg':
                return imagejpeg($destImage, $imagePath, 90);
            case 'image/png':
                return imagepng($destImage, $imagePath, 9);
            case 'image/gif':
                return imagegif($destImage, $imagePath);
            case 'image/webp':
                return imagewebp($destImage, $imagePath, 90);
            default:
                return false;
        }
    }
    
    /**
     * Rotate image
     * 
     * @param string $imagePath Path to the image
     * @param int $degrees Degrees to rotate
     * @return bool Success or failure
     */
    public function rotateImage($imagePath, $degrees) {
        // Get image info
        $imageInfo = getimagesize($imagePath);
        $mime = $imageInfo['mime'];
        
        // Create image resource based on type
        switch ($mime) {
            case 'image/jpeg':
                $srcImage = imagecreatefromjpeg($imagePath);
                break;
            case 'image/png':
                $srcImage = imagecreatefrompng($imagePath);
                break;
            case 'image/gif':
                $srcImage = imagecreatefromgif($imagePath);
                break;
            case 'image/webp':
                $srcImage = imagecreatefromwebp($imagePath);
                break;
            default:
                return false;
        }
        
        // Rotate
        $transparent = imagecolorallocatealpha($srcImage, 255, 255, 255, 127);
        $rotated = imagerotate($srcImage, $degrees, $transparent);
        
        // Preserve transparency
        imagealphablending($rotated, false);
        imagesavealpha($rotated, true);
        
        // Save the rotated image
        switch ($mime) {
            case 'image/jpeg':
                return imagejpeg($rotated, $imagePath, 90);
            case 'image/png':
                return imagepng($rotated, $imagePath, 9);
            case 'image/gif':
                return imagegif($rotated, $imagePath);
            case 'image/webp':
                return imagewebp($rotated, $imagePath, 90);
            default:
                return false;
        }
    }
    
    /**
     * Apply filter to image
     * 
     * @param string $imagePath Path to the image
     * @param string $filter Filter to apply
     * @return bool Success or failure
     */
    public function applyFilter($imagePath, $filter) {
        // Get image info
        $imageInfo = getimagesize($imagePath);
        $mime = $imageInfo['mime'];
        
        // Create image resource based on type
        switch ($mime) {
            case 'image/jpeg':
                $image = imagecreatefromjpeg($imagePath);
                break;
            case 'image/png':
                $image = imagecreatefrompng($imagePath);
                break;
            case 'image/gif':
                $image = imagecreatefromgif($imagePath);
                break;
            case 'image/webp':
                $image = imagecreatefromwebp($imagePath);
                break;
            default:
                return false;
        }
        
        // Apply filter
        switch ($filter) {
            case 'grayscale':
                imagefilter($image, IMG_FILTER_GRAYSCALE);
                break;
            case 'sepia':
                imagefilter($image, IMG_FILTER_GRAYSCALE);
                imagefilter($image, IMG_FILTER_COLORIZE, 90, 60, 40);
                break;
            case 'negative':
                imagefilter($image, IMG_FILTER_NEGATE);
                break;
            case 'brightness':
                imagefilter($image, IMG_FILTER_BRIGHTNESS, 20);
                break;
            case 'contrast':
                imagefilter($image, IMG_FILTER_CONTRAST, -20);
                break;
            case 'edgedetect':
                imagefilter($image, IMG_FILTER_EDGEDETECT);
                break;
            case 'emboss':
                imagefilter($image, IMG_FILTER_EMBOSS);
                break;
            case 'blur':
                imagefilter($image, IMG_FILTER_GAUSSIAN_BLUR);
                break;
            case 'sharpen':
                $sharpen = [
                    [-1, -1, -1],
                    [-1, 16, -1],
                    [-1, -1, -1]
                ];
                $divisor = array_sum(array_map('array_sum', $sharpen));
                imageconvolution($image, $sharpen, $divisor, 0);
                break;
            default:
                return false;
        }
        
        // Save the filtered image
        switch ($mime) {
            case 'image/jpeg':
                return imagejpeg($image, $imagePath, 90);
            case 'image/png':
                return imagepng($image, $imagePath, 9);
            case 'image/gif':
                return imagegif($image, $imagePath);
            case 'image/webp':
                return imagewebp($image, $imagePath, 90);
            default:
                return false;
        }
    }
    
    /**
     * Add text to image
     * 
     * @param string $imagePath Path to the image
     * @param string $text Text to add
     * @param int $x X coordinate
     * @param int $y Y coordinate
     * @param array $options Text options
     * @return bool Success or failure
     */
    public function addTextToImage($imagePath, $text, $x, $y, $options = []) {
        // Default options
        $defaults = [
            'font_size' => 20,
            'font_path' => 'fonts/arial.ttf',
            'color' => [255, 255, 255],
            'angle' => 0
        ];
        
        // Merge options
        $options = array_merge($defaults, $options);
        
        // Get image info
        $imageInfo = getimagesize($imagePath);
        $mime = $imageInfo['mime'];
        
        // Create image resource based on type
        switch ($mime) {
            case 'image/jpeg':
                $image = imagecreatefromjpeg($imagePath);
                break;
            case 'image/png':
                $image = imagecreatefrompng($imagePath);
                break;
            case 'image/gif':
                $image = imagecreatefromgif($imagePath);
                break;
            case 'image/webp':
                $image = imagecreatefromwebp($imagePath);
                break;
            default:
                return false;
        }
        
        // Create color
        $color = imagecolorallocate($image, $options['color'][0], $options['color'][1], $options['color'][2]);
        
        // Add text
        if (file_exists($options['font_path'])) {
            // Use TrueType font
            imagettftext($image, $options['font_size'], $options['angle'], $x, $y, $color, $options['font_path'], $text);
        } else {
            // Use built-in font
            $fontSize = 5; // Maximum built-in font size
            imagestring($image, $fontSize, $x, $y, $text, $color);
        }
        
        // Save the image with text
        switch ($mime) {
            case 'image/jpeg':
                return imagejpeg($image, $imagePath, 90);
            case 'image/png':
                return imagepng($image, $imagePath, 9);
            case 'image/gif':
                return imagegif($image, $imagePath);
            case 'image/webp':
                return imagewebp($image, $imagePath, 90);
            default:
                return false;
        }
    }
    
    /**
     * Draw on image
     * 
     * @param string $imagePath Path to the image
     * @param array $shapes Shapes to draw
     * @return bool Success or failure
     */
    public function drawOnImage($imagePath, $shapes) {
        error_log("Drawing on image: " . $imagePath);
        error_log("Shapes: " . print_r($shapes, true));
        
        // Check if file exists and is writable
        if (!file_exists($imagePath)) {
            error_log("Error: Image file does not exist: " . $imagePath);
            return false;
        }
        
        if (!is_writable($imagePath)) {
            error_log("Error: Image file is not writable: " . $imagePath);
            
            // Try to make the file writable
            if (!chmod($imagePath, 0666)) {
                error_log("Error: Failed to make image file writable: " . $imagePath);
                return false;
            }
            
            error_log("Successfully made image file writable: " . $imagePath);
        }
        
        // Get image info
        $imageInfo = getimagesize($imagePath);
        $mime = $imageInfo['mime'];
        error_log("Image mime type: " . $mime);
        
        // Create image resource based on type
        switch ($mime) {
            case 'image/jpeg':
                $image = imagecreatefromjpeg($imagePath);
                break;
            case 'image/png':
                $image = imagecreatefrompng($imagePath);
                break;
            case 'image/gif':
                $image = imagecreatefromgif($imagePath);
                break;
            case 'image/webp':
                $image = imagecreatefromwebp($imagePath);
                break;
            default:
                return false;
        }
        
        // Draw shapes
        foreach ($shapes as $shape) {
            // Create color
            $color = imagecolorallocate($image, $shape['color'][0], $shape['color'][1], $shape['color'][2]);
            
            // Draw based on shape type
            switch ($shape['type']) {
                case 'line':
                    imageline($image, $shape['x1'], $shape['y1'], $shape['x2'], $shape['y2'], $color);
                    break;
                case 'rectangle':
                    if (!empty($shape['fill']) && $shape['fill']) {
                        imagefilledrectangle($image, $shape['x1'], $shape['y1'], $shape['x2'], $shape['y2'], $color);
                    } else {
                        imagerectangle($image, $shape['x1'], $shape['y1'], $shape['x2'], $shape['y2'], $color);
                    }
                    break;
                case 'ellipse':
                    $width = abs($shape['x2'] - $shape['x1']);
                    $height = abs($shape['y2'] - $shape['y1']);
                    $centerX = min($shape['x1'], $shape['x2']) + $width / 2;
                    $centerY = min($shape['y1'], $shape['y2']) + $height / 2;
                    
                    if (!empty($shape['fill']) && $shape['fill']) {
                        imagefilledellipse($image, $centerX, $centerY, $width, $height, $color);
                    } else {
                        imageellipse($image, $centerX, $centerY, $width, $height, $color);
                    }
                    break;
                case 'polygon':
                    // Debug information
                    error_log("Drawing polygon with " . count($shape['points']) . " points");
                    error_log("Points: " . print_r($shape['points'], true));
                    
                    // Make sure we have at least 3 points (6 values) for a polygon
                    if (count($shape['points']) >= 6) {
                        if (!empty($shape['fill']) && $shape['fill']) {
                            // Fix for PHP 8.4 deprecation
                            imagefilledpolygon($image, $shape['points'], $color);
                        } else {
                            // Fix for PHP 8.4 deprecation
                            imagepolygon($image, $shape['points'], $color);
                        }
                    } else {
                        error_log("Not enough points for polygon: " . count($shape['points']));
                    }
                    break;
            }
        }
        
        // Create a temporary file for saving
        $tempPath = $imagePath . '.tmp';
        
        // Create a backup of the original image
        $backupPath = $imagePath . '.bak';
        if (!file_exists($backupPath)) {
            copy($imagePath, $backupPath);
            error_log("Created backup of original image: " . $backupPath);
        }
        
        // Save the image with drawings to the temporary file
        $result = false;
        switch ($mime) {
            case 'image/jpeg':
                $result = imagejpeg($image, $tempPath, 90);
                break;
            case 'image/png':
                $result = imagepng($image, $tempPath, 9);
                break;
            case 'image/gif':
                $result = imagegif($image, $tempPath);
                break;
            case 'image/webp':
                $result = imagewebp($image, $tempPath, 90);
                break;
        }
        
        // If the save was successful, replace the original file
        if ($result) {
            // Make sure the temporary file is writable
            chmod($tempPath, 0666);
            
            // Replace the original file with the temporary file
            if (rename($tempPath, $imagePath)) {
                error_log("Successfully replaced original image with edited version");
                
                // Make sure the new file is writable
                chmod($imagePath, 0666);
            } else {
                error_log("Failed to replace original image with edited version");
                $result = false;
                
                // Clean up the temporary file
                if (file_exists($tempPath)) {
                    unlink($tempPath);
                }
            }
        } else {
            error_log("Failed to save image to temporary file: " . $tempPath);
            
            // Clean up the temporary file
            if (file_exists($tempPath)) {
                unlink($tempPath);
            }
        }
        
        // Free up memory
        imagedestroy($image);
        
        // Log result
        if ($result) {
            error_log("Successfully saved image: " . $imagePath);
        } else {
            error_log("Failed to save image: " . $imagePath);
        }
        
        return $result;
    }
    
    /**
     * Process a downloaded image
     * 
     * @param string $imageContent Raw image content
     * @param string $fileName Filename to use (with extension)
     * @param string $entityType Entity type (e.g., 'user', 'vehicle', 'show')
     * @param int $entityId Entity ID
     * @param string $uploadDir Upload directory
     * @param int $userId User ID
     * @param bool $isPrimary Whether this is the primary image
     * @return array|bool Image data on success, false on failure
     */
    public function processDownloadedImage($imageContent, $fileName, $entityType, $entityId, $uploadDir = 'uploads/images/', $userId = null, $isPrimary = false) {
        error_log('======= PROCESS DOWNLOADED IMAGE STARTED =======');
        error_log('ImageEditorModel::processDownloadedImage - Processing downloaded image for ' . $entityType . ' ' . $entityId);
        error_log('ImageEditorModel::processDownloadedImage - Image content length: ' . strlen($imageContent) . ' bytes');
        error_log('ImageEditorModel::processDownloadedImage - Filename: ' . $fileName);
        error_log('ImageEditorModel::processDownloadedImage - Upload directory: ' . $uploadDir);
        error_log('ImageEditorModel::processDownloadedImage - User ID: ' . ($userId ?? 'null'));
        
        // Get current user ID if not provided
        if ($userId === null && isset($_SESSION['user_id'])) {
            $userId = $_SESSION['user_id'];
        }
        
        // Get settings model
        $settingsModel = $this->getSettingsModel();
        
        // Get image settings
        $imageQuality = (int)$settingsModel->getSetting('image_image_quality', 80);
        $thumbnailSize = (int)$settingsModel->getSetting('image_thumbnail_size', 200);
        $maxUploadSize = (int)$settingsModel->getSetting('image_max_upload_size', 5) * 1024 * 1024; // Convert MB to bytes
        $optimizeImages = $settingsModel->getSetting('image_optimize_images', '0') === '1';
        $resizeLargeImages = $settingsModel->getSetting('image_resize_large_images', '0') === '1';
        $maxWidth = (int)$settingsModel->getSetting('image_max_width', 1920);
        $maxHeight = (int)$settingsModel->getSetting('image_max_height', 1080);
        
        // Check file size against settings
        $fileSize = strlen($imageContent);
        if ($fileSize > $maxUploadSize) {
            error_log('ImageEditorModel::processDownloadedImage - File too large: ' . $fileSize . ' bytes (max: ' . $maxUploadSize . ' bytes)');
            return false;
        }
        
        // Get file extension
        $fileExt = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
        if (empty($fileExt)) {
            $fileExt = 'jpg'; // Default to jpg if no extension provided
        }
        
        // Generate unique file name
        $newFileName = uniqid($entityType . '_' . $entityId . '_') . '.' . $fileExt;
        
        // Create upload directory if it doesn't exist
        if (!file_exists($uploadDir)) {
            if (!mkdir($uploadDir, 0777, true)) {
                error_log('ImageEditorModel::processDownloadedImage - Failed to create upload directory: ' . $uploadDir);
                return false;
            }
        }
        
        // Create thumbnails directory if it doesn't exist
        $thumbnailDir = $uploadDir . 'thumbnails/';
        if (!file_exists($thumbnailDir)) {
            if (!mkdir($thumbnailDir, 0777, true)) {
                error_log('ImageEditorModel::processDownloadedImage - Failed to create thumbnails directory: ' . $thumbnailDir);
                return false;
            }
        }
        
        $uploadPath = $uploadDir . $newFileName;
        $thumbnailPath = $thumbnailDir . $newFileName;
        
        error_log('ImageEditorModel::processDownloadedImage - Attempting to save to: ' . $uploadPath);
        error_log('ImageEditorModel::processDownloadedImage - Directory exists: ' . (file_exists($uploadDir) ? 'Yes' : 'No'));
        error_log('ImageEditorModel::processDownloadedImage - Directory writable: ' . (is_writable($uploadDir) ? 'Yes' : 'No'));
        
        // Get absolute path for better debugging
        $absoluteUploadPath = realpath($uploadDir) . '/' . $newFileName;
        error_log('ImageEditorModel::processDownloadedImage - Absolute path: ' . $absoluteUploadPath);
        
        // Save the image content to the file
        $result = file_put_contents($uploadPath, $imageContent);
        if ($result === false) {
            error_log('ImageEditorModel::processDownloadedImage - Failed to save image content to ' . $uploadPath);
            error_log('ImageEditorModel::processDownloadedImage - PHP Error: ' . json_encode(error_get_last()));
            return false;
        }
        
        error_log('ImageEditorModel::processDownloadedImage - Saved image to ' . $uploadPath . ' (' . $result . ' bytes written)');
        
        // Get image dimensions
        $imageInfo = getimagesize($uploadPath);
        if (!$imageInfo) {
            error_log('ImageEditorModel::processDownloadedImage - Failed to get image info for ' . $uploadPath);
            @unlink($uploadPath);
            return false;
        }
        
        $width = $imageInfo[0];
        $height = $imageInfo[1];
        $mime = $imageInfo['mime'];
        
        // Resize image if needed
        if ($resizeLargeImages && ($width > $maxWidth || $height > $maxHeight)) {
            $this->resizeImage($uploadPath, $maxWidth, $maxHeight);
            
            // Get new dimensions after resize
            $imageInfo = getimagesize($uploadPath);
            $width = $imageInfo[0];
            $height = $imageInfo[1];
        }
        
        // Create thumbnail
        $this->createThumbnail($uploadPath, $thumbnailPath, $thumbnailSize);
        
        // Optimize image if enabled
        if ($optimizeImages) {
            $this->optimizeImage($uploadPath, $imageQuality);
        }
        
        // Add image to database
        $this->db->query('INSERT INTO images (entity_type, entity_id, file_name, file_path, thumbnail_path, width, height, mime_type, file_size, user_id, is_primary, created_at) 
                          VALUES (:entity_type, :entity_id, :file_name, :file_path, :thumbnail_path, :width, :height, :mime_type, :file_size, :user_id, :is_primary, NOW())');
        
        $this->db->bind(':entity_type', $entityType);
        $this->db->bind(':entity_id', $entityId);
        $this->db->bind(':file_name', $newFileName);
        $this->db->bind(':file_path', $uploadPath);
        $this->db->bind(':thumbnail_path', $thumbnailPath);
        $this->db->bind(':width', $width);
        $this->db->bind(':height', $height);
        $this->db->bind(':mime_type', $mime);
        $this->db->bind(':file_size', $fileSize);
        $this->db->bind(':user_id', $userId);
        $this->db->bind(':is_primary', $isPrimary ? 1 : 0);
        
        if ($this->db->execute()) {
            $imageId = $this->db->lastInsertId();
            
            // If this is the primary image, update all other images to not be primary
            if ($isPrimary) {
                $this->db->query('UPDATE images SET is_primary = 0 WHERE entity_type = :entity_type AND entity_id = :entity_id AND id != :id');
                $this->db->bind(':entity_type', $entityType);
                $this->db->bind(':entity_id', $entityId);
                $this->db->bind(':id', $imageId);
                $this->db->execute();
            }
            
            // Return image data
            return [
                'id' => $imageId,
                'entity_type' => $entityType,
                'entity_id' => $entityId,
                'file_name' => $newFileName,
                'file_path' => $uploadPath,
                'thumbnail_path' => $thumbnailPath,
                'width' => $width,
                'height' => $height,
                'mime_type' => $mime,
                'file_size' => $fileSize,
                'user_id' => $userId,
                'is_primary' => $isPrimary
            ];
        } else {
            error_log('ImageEditorModel::processDownloadedImage - Failed to add image to database');
            return false;
        }
    }
}