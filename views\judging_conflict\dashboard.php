<?php require APPROOT . '/views/includes/header.php'; ?>

<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-8">
            <h1><i class="fas fa-exclamation-triangle me-2"></i>Judging Conflicts Dashboard</h1>
            <p class="text-muted">Manage and resolve judging conflicts and disputes</p>
        </div>
        <div class="col-md-4 text-end">
            <?php if ($user_role === 'admin'): ?>
                <button type="button" class="btn btn-primary me-2" data-bs-toggle="modal" data-bs-target="#detectConflictsModal">
                    <i class="fas fa-search me-2"></i>Detect Conflicts
                </button>
            <?php endif; ?>
            <a href="<?php echo BASE_URL; ?>/judging_conflict/report" class="btn btn-success">
                <i class="fas fa-plus me-2"></i>Report Conflict
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-2">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo $stats->total_conflicts ?? 0; ?></h4>
                            <small>Total Conflicts</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-exclamation-triangle fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo $stats->open_conflicts ?? 0; ?></h4>
                            <small>Open</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clock fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo $stats->under_review_conflicts ?? 0; ?></h4>
                            <small>Under Review</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-eye fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo $stats->resolved_conflicts ?? 0; ?></h4>
                            <small>Resolved</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-check fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo $stats->urgent_conflicts ?? 0; ?></h4>
                            <small>Urgent</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-fire fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card bg-secondary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="mb-0"><?php echo round($stats->avg_resolution_hours ?? 0, 1); ?>h</h4>
                            <small>Avg Resolution</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-stopwatch fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Filters</h5>
        </div>
        <div class="card-body">
            <form method="GET" action="<?php echo BASE_URL; ?>/judging_conflict/dashboard">
                <div class="row">
                    <div class="col-md-2">
                        <label for="status" class="form-label">Status</label>
                        <select name="status" id="status" class="form-select">
                            <option value="">All Statuses</option>
                            <option value="open" <?php echo $filters['status'] === 'open' ? 'selected' : ''; ?>>Open</option>
                            <option value="under_review" <?php echo $filters['status'] === 'under_review' ? 'selected' : ''; ?>>Under Review</option>
                            <option value="resolved" <?php echo $filters['status'] === 'resolved' ? 'selected' : ''; ?>>Resolved</option>
                            <option value="dismissed" <?php echo $filters['status'] === 'dismissed' ? 'selected' : ''; ?>>Dismissed</option>
                            <option value="escalated" <?php echo $filters['status'] === 'escalated' ? 'selected' : ''; ?>>Escalated</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="priority" class="form-label">Priority</label>
                        <select name="priority" id="priority" class="form-select">
                            <option value="">All Priorities</option>
                            <option value="urgent" <?php echo $filters['priority'] === 'urgent' ? 'selected' : ''; ?>>Urgent</option>
                            <option value="high" <?php echo $filters['priority'] === 'high' ? 'selected' : ''; ?>>High</option>
                            <option value="normal" <?php echo $filters['priority'] === 'normal' ? 'selected' : ''; ?>>Normal</option>
                            <option value="low" <?php echo $filters['priority'] === 'low' ? 'selected' : ''; ?>>Low</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="conflict_type" class="form-label">Type</label>
                        <select name="conflict_type" id="conflict_type" class="form-select">
                            <option value="">All Types</option>
                            <option value="score_discrepancy" <?php echo $filters['conflict_type'] === 'score_discrepancy' ? 'selected' : ''; ?>>Score Discrepancy</option>
                            <option value="assignment_conflict" <?php echo $filters['conflict_type'] === 'assignment_conflict' ? 'selected' : ''; ?>>Assignment Conflict</option>
                            <option value="scoring_dispute" <?php echo $filters['conflict_type'] === 'scoring_dispute' ? 'selected' : ''; ?>>Scoring Dispute</option>
                            <option value="technical_error" <?php echo $filters['conflict_type'] === 'technical_error' ? 'selected' : ''; ?>>Technical Error</option>
                            <option value="owner_complaint" <?php echo $filters['conflict_type'] === 'owner_complaint' ? 'selected' : ''; ?>>Owner Complaint</option>
                            <option value="judge_concern" <?php echo $filters['conflict_type'] === 'judge_concern' ? 'selected' : ''; ?>>Judge Concern</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label for="show_id" class="form-label">Show</label>
                        <select name="show_id" id="show_id" class="form-select">
                            <option value="">All Shows</option>
                            <?php foreach ($shows as $show): ?>
                                <option value="<?php echo $show->id; ?>" <?php echo $filters['show_id'] == $show->id ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($show->name); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <?php if ($user_role === 'admin'): ?>
                            <div class="form-text small">Showing recent/active shows only</div>
                        <?php endif; ?>
                    </div>
                    <div class="col-md-3">
                        <label for="search" class="form-label">Search</label>
                        <input type="text" name="search" id="search" class="form-control" 
                               placeholder="Search title or description..." 
                               value="<?php echo htmlspecialchars($filters['search']); ?>">
                    </div>
                    <div class="col-md-1">
                        <label class="form-label">&nbsp;</label>
                        <div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i>
                            </button>
                            <a href="<?php echo BASE_URL; ?>/judging_conflict/dashboard" class="btn btn-secondary">
                                <i class="fas fa-times"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Conflicts Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="mb-0"><i class="fas fa-list me-2"></i>Conflicts</h5>
        </div>
        <div class="card-body">
            <?php if (empty($conflicts)): ?>
                <div class="text-center py-4">
                    <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                    <h5>No conflicts found</h5>
                    <p class="text-muted">No judging conflicts match your current filters.</p>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Title</th>
                                <th>Type</th>
                                <th>Show</th>
                                <th>Vehicle</th>
                                <th>Reporter</th>
                                <th>Priority</th>
                                <th>Status</th>
                                <th>Created</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($conflicts as $conflict): ?>
                                <tr>
                                    <td>
                                        <span class="badge bg-secondary">#<?php echo $conflict->id; ?></span>
                                    </td>
                                    <td>
                                        <strong><?php echo htmlspecialchars($conflict->title); ?></strong>
                                        <?php if ($conflict->auto_detected): ?>
                                            <span class="badge bg-info ms-1" title="Automatically detected">
                                                <i class="fas fa-robot"></i>
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-light text-dark">
                                            <?php echo ucwords(str_replace('_', ' ', $conflict->conflict_type)); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <small>
                                            <?php echo htmlspecialchars($conflict->show_name); ?>
                                            <?php if ($conflict->show_start_date): ?>
                                                <br><span class="text-muted">
                                                    <?php echo formatDateTimeForUser($conflict->show_start_date, $_SESSION['user_id'] ?? null, 'M j, Y'); ?>
                                                </span>
                                            <?php endif; ?>
                                        </small>
                                    </td>
                                    <td>
                                        <?php if ($conflict->registration_number): ?>
                                            <small>
                                                #<?php echo htmlspecialchars($conflict->registration_number); ?>
                                                <?php if ($conflict->year && $conflict->make && $conflict->model): ?>
                                                    <br><?php echo htmlspecialchars($conflict->year . ' ' . $conflict->make . ' ' . $conflict->model); ?>
                                                <?php endif; ?>
                                            </small>
                                        <?php else: ?>
                                            <span class="text-muted">N/A</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <small>
                                            <?php echo htmlspecialchars($conflict->reported_by_name); ?>
                                            <br><span class="text-muted"><?php echo ucfirst($conflict->reported_by_role); ?></span>
                                        </small>
                                    </td>
                                    <td>
                                        <?php
                                        $priorityClass = [
                                            'urgent' => 'bg-danger',
                                            'high' => 'bg-warning',
                                            'normal' => 'bg-primary',
                                            'low' => 'bg-secondary'
                                        ][$conflict->priority] ?? 'bg-secondary';
                                        ?>
                                        <span class="badge <?php echo $priorityClass; ?>">
                                            <?php echo ucfirst($conflict->priority); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?php
                                        $statusClass = [
                                            'open' => 'bg-warning',
                                            'under_review' => 'bg-info',
                                            'resolved' => 'bg-success',
                                            'dismissed' => 'bg-secondary',
                                            'escalated' => 'bg-danger'
                                        ][$conflict->status] ?? 'bg-secondary';
                                        ?>
                                        <span class="badge <?php echo $statusClass; ?>">
                                            <?php echo ucwords(str_replace('_', ' ', $conflict->status)); ?>
                                        </span>
                                    </td>
                                    <td>
                                        <small>
                                            <?php echo formatDateTimeForUser($conflict->created_at, $_SESSION['user_id'] ?? null, 'M j, Y g:i A'); ?>
                                        </small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <a href="<?php echo BASE_URL; ?>/judging_conflict/viewConflict/<?php echo $conflict->id; ?>" 
                                               class="btn btn-outline-primary" title="View Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <?php if (in_array($conflict->status, ['open', 'under_review'])): ?>
                                                <button type="button" class="btn btn-outline-success" 
                                                        onclick="quickStatusUpdate(<?php echo $conflict->id; ?>, 'resolved')" 
                                                        title="Mark Resolved">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <?php if ($pagination['total_pages'] > 1): ?>
                    <nav aria-label="Conflicts pagination" class="mt-3">
                        <ul class="pagination justify-content-center">
                            <?php if ($pagination['has_prev']): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo $pagination['current_page'] - 1; ?>&<?php echo http_build_query(array_filter($filters)); ?>">
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                </li>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $pagination['current_page'] - 2); $i <= min($pagination['total_pages'], $pagination['current_page'] + 2); $i++): ?>
                                <li class="page-item <?php echo $i === $pagination['current_page'] ? 'active' : ''; ?>">
                                    <a class="page-link" href="?page=<?php echo $i; ?>&<?php echo http_build_query(array_filter($filters)); ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                            <?php endfor; ?>
                            
                            <?php if ($pagination['has_next']): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?page=<?php echo $pagination['current_page'] + 1; ?>&<?php echo http_build_query(array_filter($filters)); ?>">
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                    
                    <div class="text-center text-muted">
                        Showing <?php echo (($pagination['current_page'] - 1) * $pagination['per_page']) + 1; ?> to 
                        <?php echo min($pagination['current_page'] * $pagination['per_page'], $pagination['total']); ?> of 
                        <?php echo $pagination['total']; ?> conflicts
                    </div>
                <?php endif; ?>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Detect Conflicts Modal -->
<?php if ($user_role === 'admin'): ?>
<div class="modal fade" id="detectConflictsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Detect Conflicts</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="detectConflictsForm">
                    <input type="hidden" name="<?php echo CSRF_TOKEN_NAME; ?>" value="<?php echo $csrf_token; ?>">
                    <div class="mb-3">
                        <label for="detect_show_id" class="form-label">Select Show</label>
                        <select name="show_id" id="detect_show_id" class="form-select" required>
                            <option value="">Choose a show...</option>
                            <?php foreach ($shows as $show): ?>
                                <option value="<?php echo $show->id; ?>">
                                    <?php echo htmlspecialchars($show->name); ?>
                                    <?php if ($show->start_date): ?>
                                        - <?php echo formatDateTimeForUser($show->start_date, $_SESSION['user_id'] ?? null, 'M j, Y'); ?>
                                    <?php endif; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        This will automatically detect score discrepancies and other conflicts for the selected show.
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="runConflictDetection()">
                    <i class="fas fa-search me-2"></i>Detect Conflicts
                </button>
            </div>
        </div>
    </div>
</div>
<?php endif; ?>

<script>
// Quick status update
function quickStatusUpdate(conflictId, status) {
    if (!confirm('Are you sure you want to mark this conflict as ' + status + '?')) {
        return;
    }
    
    const formData = new FormData();
    formData.append('status', status);
    formData.append('<?php echo CSRF_TOKEN_NAME; ?>', '<?php echo $csrf_token; ?>');
    
    fetch(`<?php echo BASE_URL; ?>/judging_conflict/update/${conflictId}`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Error: ' + (data.error || 'Failed to update conflict'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while updating the conflict');
    });
}

// Run conflict detection
<?php if ($user_role === 'admin'): ?>
function runConflictDetection() {
    const showId = document.getElementById('detect_show_id').value;
    if (!showId) {
        alert('Please select a show');
        return;
    }
    
    const formData = new FormData(document.getElementById('detectConflictsForm'));
    
    fetch(`<?php echo BASE_URL; ?>/judging_conflict/detectConflicts/${showId}`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            bootstrap.Modal.getInstance(document.getElementById('detectConflictsModal')).hide();
            location.reload();
        } else {
            alert('Error: ' + (data.error || 'Failed to detect conflicts'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred while detecting conflicts');
    });
}
<?php endif; ?>

// Auto-submit form on filter change
document.addEventListener('DOMContentLoaded', function() {
    const filterForm = document.querySelector('form[method="GET"]');
    const filterInputs = filterForm.querySelectorAll('select, input');
    
    filterInputs.forEach(input => {
        if (input.type !== 'text') { // Don't auto-submit on text input changes
            input.addEventListener('change', function() {
                filterForm.submit();
            });
        }
    });
});
</script>

<?php require APPROOT . '/views/includes/footer.php'; ?>