
-- --------------------------------------------------------

--
-- Table structure for table `system_settings`
--
-- Creation: Jun 23, 2025 at 08:30 PM
-- Last update: Aug 01, 2025 at 12:00 AM
--

CREATE TABLE `system_settings` (
  `id` int(10) UNSIGNED NOT NULL,
  `setting_key` varchar(255) NOT NULL,
  `setting_value` text DEFAULT NULL,
  `setting_description` text DEFAULT NULL,
  `setting_group` varchar(50) NOT NULL DEFAULT 'general',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `description` varchar(64) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- RELATIONSHIPS FOR TABLE `system_settings`:
--

--
-- Dumping data for table `system_settings`
--

INSERT INTO `system_settings` (`id`, `setting_key`, `setting_value`, `setting_description`, `setting_group`, `created_at`, `updated_at`, `description`) VALUES
(1, 'site_logo', '/uploads/branding/logo_1751468505_rides_logo.png', NULL, 'branding', '2025-05-18 23:02:57', '2025-07-02 18:21:21', ''),
(2, 'site_favicon', '', NULL, 'branding', '2025-05-18 23:02:57', '2025-07-02 18:21:21', ''),
(3, 'primary_color', '#4353cb', NULL, 'branding', '2025-05-18 23:02:57', '2025-07-02 18:21:21', ''),
(4, 'secondary_color', '#6c757d', NULL, 'branding', '2025-05-18 23:02:57', '2025-07-02 18:21:21', ''),
(5, 'accent_color', '#058a07', NULL, 'branding', '2025-05-18 23:02:57', '2025-07-02 18:21:21', ''),
(6, 'background_color', '#f8f9fa', NULL, 'branding', '2025-05-18 23:02:57', '2025-07-02 18:21:21', ''),
(7, 'text_color', '#212529', NULL, 'branding', '2025-05-18 23:02:57', '2025-07-02 18:21:21', ''),
(8, 'enable_white_labeling', '0', NULL, 'branding', '2025-05-18 23:02:57', '2025-07-02 18:21:21', ''),
(9, 'custom_css', '', NULL, 'branding', '2025-05-18 23:02:57', '2025-07-02 18:21:21', ''),
(10, 'footer_text', 'Events and Shows Management System', NULL, 'branding', '2025-05-18 23:02:57', '2025-07-02 18:21:21', ''),
(11, 'qr_code_enabled', '1', NULL, 'judging', '2025-05-18 23:02:57', '2025-05-29 00:25:13', ''),
(12, 'qr_code_logo', '/uploads/branding/qr_logo_1748478313_ROWANqrpng.png', NULL, 'judging', '2025-05-18 23:02:57', '2025-05-29 00:25:13', ''),
(13, 'qr_code_color', '#000000', NULL, 'judging', '2025-05-18 23:02:57', '2025-05-29 00:25:13', ''),
(14, 'qr_code_background', '#ffffff', NULL, 'judging', '2025-05-18 23:02:57', '2025-05-29 00:25:13', ''),
(15, 'image_max_upload_size', '10', NULL, 'general', '2025-05-21 13:10:11', '2025-05-30 12:39:24', ''),
(16, 'image_allowed_extensions', 'jpg,jpeg,png,gif', NULL, 'general', '2025-05-21 13:10:11', '2025-05-30 12:39:24', ''),
(17, 'image_optimize_images', '1', NULL, 'general', '2025-05-21 13:10:11', '2025-05-30 12:39:24', ''),
(18, 'image_watermark_enabled', '1', NULL, 'general', '2025-05-21 13:10:12', '2025-05-30 12:39:24', ''),
(19, 'image_watermark_text', 'Rowan Elite Rides', NULL, 'general', '2025-05-21 13:10:12', '2025-05-30 12:39:24', ''),
(20, 'image_watermark_position', 'bottom-right', NULL, 'general', '2025-05-21 13:10:12', '2025-05-30 12:39:24', ''),
(21, 'image_watermark_opacity', '50', NULL, 'general', '2025-05-21 13:10:12', '2025-05-30 12:39:24', ''),
(22, 'app_version', '2.39.0', NULL, 'system', '2025-05-21 13:10:12', '2025-05-30 16:35:26', ''),
(23, 'image_image_quality', '80', NULL, 'general', '2025-05-21 13:13:01', '2025-05-30 12:39:24', ''),
(24, 'image_thumbnail_size', '200', NULL, 'general', '2025-05-21 13:13:01', '2025-05-30 12:39:24', ''),
(25, 'image_viewer_enabled', '1', NULL, 'images', '2025-05-21 18:06:07', '2025-05-21 18:06:07', ''),
(26, 'primary_image_display', '1', NULL, 'images', '2025-05-21 18:06:07', '2025-05-21 18:06:07', ''),
(27, 'disabled_field_types', '[\"range\",\"rating\",\"html\"]', NULL, 'general', '2025-05-22 10:27:09', '2025-05-22 10:27:09', ''),
(44, 'db_version', '2.19.82', NULL, 'system', '2025-05-28 17:57:24', '2025-05-28 18:22:32', ''),
(51, 'qrcode_size', '300', NULL, 'judging', '2025-05-28 18:09:50', '2025-05-29 00:25:13', ''),
(52, 'qrcode_margin', '10', NULL, 'judging', '2025-05-28 18:09:50', '2025-05-29 00:25:13', ''),
(54, 'qr_code_error_correction', 'L', NULL, 'judging', '2025-05-28 18:26:27', '2025-05-29 00:25:13', ''),
(60, 'image_max_width', '1200', NULL, 'general', '2025-05-30 12:30:15', '2025-05-30 12:39:24', ''),
(61, 'image_max_height', '1000', NULL, 'general', '2025-05-30 12:30:15', '2025-05-30 12:39:24', ''),
(62, 'image_resize_large_images', '1', NULL, 'general', '2025-05-30 12:30:15', '2025-05-30 12:39:24', ''),
(64, 'dev_admin_bypass', '0', NULL, 'development', '2025-05-30 20:48:22', '2025-06-27 12:24:05', ''),
(67, 'system_version', '2.69.0', NULL, 'general', '2025-05-31 12:21:31', '2025-05-31 18:53:59', ''),
(71, 'version', '3.35.16', NULL, 'system', '2025-06-04 15:59:08', '2025-06-13 18:06:25', 'Current system version'),
(76, 'app_name', 'Rowan Elite Rides Public Events and Shows', NULL, 'general', '2025-06-10 11:58:11', '2025-06-10 12:07:41', ''),
(77, 'fb_app_id', 'sdfsdfsdfsdfs', NULL, 'general', '2025-06-10 11:58:11', '2025-06-10 12:07:41', ''),
(78, 'fb_app_secret', '435345345345', NULL, 'general', '2025-06-10 11:58:11', '2025-06-10 12:07:41', ''),
(79, 'email_smtp_host', 'us1.workspace.org', NULL, 'general', '2025-06-12 20:49:55', '2025-07-16 01:13:35', 'SMTP server hostname'),
(80, 'email_smtp_port', '2465', NULL, 'general', '2025-06-12 20:49:55', '2025-07-16 01:13:35', 'SMTP server port'),
(81, 'email_smtp_username', '<EMAIL>', NULL, 'general', '2025-06-12 20:49:55', '2025-07-16 01:13:35', 'SMTP username'),
(82, 'email_smtp_password', 't12181977', NULL, 'general', '2025-06-12 20:49:55', '2025-07-16 00:24:12', 'SMTP password'),
(83, 'email_smtp_encryption', 'ssl', NULL, 'general', '2025-06-12 20:49:55', '2025-07-16 01:13:35', 'SMTP encryption (tls/ssl)'),
(84, 'email_from_address', '<EMAIL>', NULL, 'general', '2025-06-12 20:49:55', '2025-07-16 01:13:35', 'Default from email address'),
(85, 'email_from_name', 'Rowan Elite Rides Events and Shows', NULL, 'general', '2025-06-12 20:49:55', '2025-07-16 01:13:35', 'Default from name'),
(86, 'email_notification_new_show', '1', NULL, 'general', '2025-06-12 20:49:55', '2025-07-16 01:13:35', 'Send notification when new show is created'),
(87, 'default_listing_fee', '20.00', NULL, 'payment', '2025-06-12 20:49:55', '2025-06-12 22:09:33', 'Default fee for listing a show'),
(88, 'listing_fee_type', 'per_show', NULL, 'payment', '2025-06-12 20:49:55', '2025-06-12 22:09:33', 'Type of listing fee (monthly/per_show)'),
(90, 'facebook_login_enabled', '1', NULL, 'general', '2025-06-13 16:17:02', '2025-06-13 16:17:02', ''),
(92, 'data_deletion_enabled', '1', NULL, 'general', '2025-06-13 16:44:38', '2025-06-13 16:44:38', ''),
(94, 'session_lifetime', '2592000', 'Session lifetime in seconds (default: 86400 = 24 hours)', 'development', '2025-06-14 17:48:44', '2025-06-27 12:24:05', ''),
(95, 'facebook_session_lifetime', '2592000', 'Facebook session lifetime in seconds (default: 86400 = 24 hours)', 'development', '2025-06-14 17:48:44', '2025-06-27 12:24:05', ''),
(96, 'remember_me_lifetime', '31536000', 'Remember Me lifetime in seconds (default: 2592000 = 30 days)', 'development', '2025-06-14 17:48:44', '2025-06-27 12:24:05', ''),
(97, 'enable_facebook_sharing', '1', NULL, 'social_media', '2025-06-15 19:45:24', '2025-06-15 20:24:23', ''),
(98, 'facebook_app_id', '781908744267875', NULL, 'social_media', '2025-06-15 19:45:24', '2025-06-16 00:21:32', ''),
(99, 'facebook_app_secret', '********************************', NULL, 'social_media', '2025-06-15 19:45:24', '2025-06-16 00:21:32', ''),
(100, 'facebook_page_id', '', NULL, 'social_media', '2025-06-15 19:45:24', '2025-06-15 19:45:24', ''),
(101, 'facebook_default_share_text', 'Check out this awesome car show: {show_name} on {show_date} at {show_location}! {show_url}', NULL, 'social_media', '2025-06-15 19:45:24', '2025-06-15 19:45:24', ''),
(102, 'enable_facebook_events', '1', NULL, 'social_media', '2025-06-15 19:45:24', '2025-06-15 20:24:23', ''),
(103, 'facebook_event_template', '{show_description}\\n\\nRegistration opens: {registration_start}\\nRegistration closes: {registration_end}\\n\\nMore details at: {show_url}', NULL, 'social_media', '2025-06-15 19:45:24', '2025-06-15 19:45:24', ''),
(104, 'show_facebook_share_button', '1', NULL, 'social_media', '2025-06-15 19:45:24', '2025-06-15 19:45:24', ''),
(105, 'show_facebook_event_button', '1', NULL, 'social_media', '2025-06-15 19:45:24', '2025-06-15 19:45:24', ''),
(106, 'header_bg_image', 'https://events.rowaneliterides.com/public/images/cf.jpg', NULL, 'general', '2025-07-02 12:29:49', '2025-07-29 17:52:14', ''),
(107, 'header_bg_size', '100% auto', NULL, 'general', '2025-07-02 12:36:06', '2025-07-29 17:52:14', ''),
(108, 'header_bg_position', 'center', NULL, 'general', '2025-07-02 12:36:06', '2025-07-29 17:52:14', ''),
(109, 'header_bg_opacity', '0', NULL, 'general', '2025-07-02 12:40:19', '2025-07-02 12:55:52', ''),
(110, 'header_carbon_opacity', '88', NULL, 'general', '2025-07-02 13:12:51', '2025-07-29 17:52:14', ''),
(111, 'header_bg_brightness', '23', NULL, 'general', '2025-07-02 13:12:51', '2025-07-29 17:52:14', ''),
(112, 'pwa_enabled', '1', 'Enable Progressive Web App features', 'general', '2025-07-02 21:09:53', '2025-07-02 21:09:53', ''),
(113, 'push_notifications_enabled', '1', 'Enable push notifications', 'general', '2025-07-02 21:09:53', '2025-07-02 21:09:53', ''),
(114, 'offline_mode_enabled', '1', 'Enable offline functionality', 'general', '2025-07-02 21:09:53', '2025-07-02 21:09:53', ''),
(115, 'pwa_app_name', 'RER Events', 'PWA application name', 'general', '2025-07-02 21:09:53', '2025-07-02 21:09:53', ''),
(116, 'pwa_short_name', 'RER Events', 'PWA short name', 'general', '2025-07-02 21:09:53', '2025-07-02 21:09:53', ''),
(117, 'pwa_description', 'Rowan Elite Rides Events & Shows Management', 'PWA description', 'general', '2025-07-02 21:09:53', '2025-07-02 21:09:53', ''),
(118, 'pwa_theme_color', '#1338BE', 'PWA theme color', 'general', '2025-07-02 21:09:53', '2025-07-07 18:40:05', ''),
(119, 'pwa_background_color', '#ffffff', 'PWA background color', 'general', '2025-07-02 21:09:53', '2025-07-02 21:09:53', ''),
(120, 'vapid_public_key', '', 'VAPID public key for push notifications', 'general', '2025-07-02 21:09:53', '2025-07-02 21:09:53', ''),
(121, 'vapid_private_key', '', 'VAPID private key for push notifications (encrypted)', 'general', '2025-07-02 21:09:53', '2025-07-02 21:09:53', ''),
(122, 'pwa_migration_version', '1.0.0', 'PWA features migration version', 'general', '2025-07-02 21:09:53', '2025-07-02 21:09:53', ''),
(123, 'camera_banner_delay', '3000', 'Banner rotation delay in milliseconds for camera modals', 'media', '2025-07-03 20:50:07', '2025-07-06 18:58:49', 'Camera banner delay'),
(128, 'email_processing_enabled', '1', NULL, 'general', '2025-07-16 18:48:41', '2025-07-17 21:38:41', ''),
(129, 'email_server_protocol', 'imap', NULL, 'general', '2025-07-16 18:48:41', '2025-07-17 21:38:41', ''),
(130, 'email_server_host', 'us1.workspace.org', NULL, 'general', '2025-07-16 18:48:41', '2025-07-17 21:38:41', ''),
(131, 'email_server_port', '993', NULL, 'general', '2025-07-16 18:48:41', '2025-07-17 21:38:41', ''),
(132, 'email_server_username', '<EMAIL>', NULL, 'general', '2025-07-16 18:48:41', '2025-07-17 21:38:41', ''),
(133, 'email_server_password', 't12181977', NULL, 'general', '2025-07-16 18:48:41', '2025-07-17 21:38:41', ''),
(134, 'email_server_encryption', 'ssl', NULL, 'general', '2025-07-16 18:48:41', '2025-07-17 21:38:41', ''),
(135, 'email_delete_after_processing', '1', NULL, 'general', '2025-07-16 18:48:41', '2025-07-17 21:38:41', ''),
(136, 'email_spam_filtering', '1', NULL, 'general', '2025-07-16 18:48:41', '2025-07-17 21:38:41', ''),
(137, 'email_max_size_mb', '10', NULL, 'general', '2025-07-16 18:48:41', '2025-07-17 21:38:41', ''),
(138, 'email_fetch_limit', '50', NULL, 'general', '2025-07-16 18:48:41', '2025-07-17 21:38:41', ''),
(139, 'email_attachment_enabled', '1', NULL, 'general', '2025-07-16 18:48:41', '2025-07-17 21:38:41', ''),
(140, 'email_attachment_max_size_mb', '5', NULL, 'general', '2025-07-16 18:48:41', '2025-07-17 21:38:41', ''),
(141, 'email_auto_reply_enabled', '1', NULL, 'general', '2025-07-16 18:48:41', '2025-07-17 21:38:41', ''),
(142, 'ticket_number_prefix', 'RER', NULL, 'general', '2025-07-16 18:48:41', '2025-07-17 21:38:41', ''),
(143, 'email_log_retention_days', '30', NULL, 'general', '2025-07-16 18:48:41', '2025-07-17 21:38:41', ''),
(144, 'reminder_last_cleanup', '2025-08-01', NULL, 'general', '2025-07-16 19:58:57', '2025-08-01 00:00:06', ''),
(145, 'sticky_header_desktop', '0', NULL, 'general', '2025-07-21 13:16:32', '2025-07-29 17:52:14', ''),
(146, 'sticky_header_ipad_portrait', '0', NULL, 'general', '2025-07-21 13:16:32', '2025-07-29 17:52:14', ''),
(147, 'sticky_header_ipad_landscape', '0', NULL, 'general', '2025-07-21 13:16:32', '2025-07-29 17:52:14', ''),
(148, 'sticky_header_android_portrait', '0', NULL, 'general', '2025-07-21 13:16:32', '2025-07-29 17:52:14', ''),
(149, 'sticky_header_android_landscape', '0', NULL, 'general', '2025-07-21 13:16:32', '2025-07-29 17:52:14', ''),
(150, 'sticky_header_iphone', '0', NULL, 'general', '2025-07-21 13:16:32', '2025-07-29 17:52:14', ''),
(151, 'logo_size_desktop', '62', NULL, 'general', '2025-07-29 17:48:32', '2025-07-29 17:52:14', ''),
(152, 'logo_size_mobile', '52', NULL, 'general', '2025-07-29 17:48:32', '2025-07-29 17:52:14', ''),
(153, 'logo_size_tablet', '63', NULL, 'general', '2025-07-29 17:48:32', '2025-07-29 17:52:14', ''),
(154, 'logo_size_apple', '52', NULL, 'general', '2025-07-29 17:48:32', '2025-07-29 17:52:14', '');
